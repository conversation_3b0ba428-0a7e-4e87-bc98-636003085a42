{"name": "vue", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@vueuse/core": "^12.5.0", "ant-design-vue": "4.x", "axios": "^1.7.9", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "gsap": "^3.12.7", "pinia": "^2.3.0", "screenfull": "^6.0.2", "vue": "^3.5.13", "vue-count-to": "^1.0.13", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.20", "less": "^4.2.1", "mockjs": "^1.1.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "vite": "^6.0.5"}}