<?php
namespace app\model;

use support\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Permission extends Model
{
    use SoftDeletes;

    protected $table = 'permissions';
    
    protected $fillable = [
        'name',
        'path',
        'method',
        'menu_type',
        'parent_id',
        'component',
        'permission_code',
        'icon',
        'sort',
        'status'
    ];

    protected $casts = [
        'parent_id' => 'integer',
        'sort' => 'integer',
        'status' => 'integer'
    ];

    /**
     * 获取子权限
     */
    public function children()
    {
        return $this->hasMany(self::class, 'parent_id', 'id');
    }

    /**
     * 获取父权限
     */
    public function parent()
    {
        return $this->belongsTo(self::class, 'parent_id', 'id');
    }

    /**
     * 获取拥有此权限的角色
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'role_permissions', 'permission_id', 'role_id');
    }

    /**
     * 获取树形结构
     */
    public static function getTree()
    {
        $permissions = self::where('status', 1)
            ->orderBy('sort')
            ->get()
            ->toArray();
        
        return self::buildTree($permissions);
    }

    /**
     * 构建树形结构
     */
    protected static function buildTree(array $elements, $parentId = 0)
    {
        $branch = [];
        foreach ($elements as $element) {
            if ($element['parent_id'] == $parentId) {
                $children = self::buildTree($elements, $element['id']);
                if ($children) {
                    $element['children'] = $children;
                }
                $branch[] = $element;
            }
        }
        return $branch;
    }
} 