-- 为定时任务表添加邮件通知相关字段
-- 执行时间：2025-07-04

-- 添加邮件通知相关字段到 gaodux_schedule 表
ALTER TABLE `gaodux_schedule` 
ADD COLUMN `email_notification` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用邮件通知：0-否，1-是' AFTER `retry_count`,
ADD COLUMN `email_recipients` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '邮件接收人列表，多个邮箱用逗号分隔' AFTER `email_notification`,
ADD COLUMN `email_subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '邮件主题模板' AFTER `email_recipients`,
ADD COLUMN `email_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '邮件内容模板' AFTER `email_subject`,
ADD COLUMN `email_on_success` tinyint(1) NOT NULL DEFAULT 0 COMMENT '任务成功时发送邮件：0-否，1-是' AFTER `email_content`,
ADD COLUMN `email_on_failure` tinyint(1) NOT NULL DEFAULT 1 COMMENT '任务失败时发送邮件：0-否，1-是' AFTER `email_on_success`;

-- 创建邮件配置表
CREATE TABLE `gaodux_email_config` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置名称',
  `smtp_host` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'SMTP服务器地址',
  `smtp_port` int(11) NOT NULL DEFAULT 587 COMMENT 'SMTP端口',
  `smtp_encryption` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'tls' COMMENT '加密方式：tls/ssl/none',
  `smtp_username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'SMTP用户名',
  `smtp_password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'SMTP密码（加密存储）',
  `from_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '发件人邮箱',
  `from_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '发件人姓名',
  `is_default` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为默认配置：0-否，1-是',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_name` (`name`) USING BTREE,
  KEY `idx_is_default` (`is_default`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件配置表';

-- 插入默认邮件配置（需要根据实际情况修改）
INSERT INTO `gaodux_email_config` (
  `name`, 
  `smtp_host`, 
  `smtp_port`, 
  `smtp_encryption`, 
  `smtp_username`, 
  `smtp_password`, 
  `from_email`, 
  `from_name`, 
  `is_default`, 
  `status`, 
  `created_at`, 
  `updated_at`
) VALUES (
  '默认邮件配置',
  'smtp.qq.com',
  587,
  'tls',
  '<EMAIL>',
  'your-smtp-password',
  '<EMAIL>',
  '报关系统',
  1,
  1,
  NOW(),
  NOW()
);

-- 创建邮件发送日志表
CREATE TABLE `gaodux_email_logs` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `schedule_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '关联的定时任务ID',
  `email_config_id` bigint(20) UNSIGNED NULL DEFAULT NULL COMMENT '使用的邮件配置ID',
  `recipients` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '收件人列表（JSON格式）',
  `subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮件主题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮件内容',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '发送状态：0-失败，1-成功',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '错误信息',
  `sent_at` timestamp NULL DEFAULT NULL COMMENT '发送时间',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_schedule_id` (`schedule_id`) USING BTREE,
  KEY `idx_email_config_id` (`email_config_id`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_sent_at` (`sent_at`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='邮件发送日志表';
