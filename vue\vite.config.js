import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    extensions: ['.js', '.vue', '.json'], // 添加 .vue 后缀支持
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  // server: {
  //   host: '0.0.0.0',
  //   port: 3000,
  //   open: true,
  //   cors: true,
  //   // 代理配置
  //   proxy: {
  //     // 当使用本地代理时可以启用下面的配置
  //     '/api': {
  //       target: 'http://localhost:8787',
  //       changeOrigin: true,
  //       rewrite: (path) => path.replace(/^\/api/, ''),
  //     },
  //   },
  // },
})
