<?php
/**
 * Here is your custom functions.
 */

 // get请求
function get($url)
{
    // 初始化 cURL
    $ch = curl_init();

    // 设置 cURL 选项
    curl_setopt($ch, CURLOPT_URL, $url); // 设置请求的URL
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // 返回响应内容而不是直接输出
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // 忽略SSL证书验证
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false); // 忽略SSL主机名验证

    // 执行请求并获取响应
    $response = curl_exec($ch);

    // 检查是否有错误
    if (curl_errno($ch)) {
        throw new Exception('cURL Error: ' . curl_error($ch));
    }

    // 关闭 cURL 资源
    curl_close($ch);

    // 返回响应内容
    return $response;
}
