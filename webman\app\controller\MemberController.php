<?php

namespace app\controller;

use support\Request;
use app\model\Member as MemberModel;

/**
 * 成员管理控制器
 */
class MemberController extends Controller
{
    /**
     * 获取当前登录用户信息
     * @param Request $request
     * @return \support\Response
     */
    public function info(Request $request)
    {
        // 获取用户ID
        $userId = $this->getUserId($request);
        if (!$userId) {
            return json(['code' => 401, 'message' => '未登录或token已过期']);
        }

        // 获取用户信息
        $member = MemberModel::find($userId);
        if (!$member) {
            return json(['code' => 404, 'message' => '用户不存在']);
        }

        // 返回用户信息
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'id' => $member->id,
                'nickname' => $member->nickname,
                'is_super' => $member->is_super,
                'name' => $member->name,
                'status' => $member->status,
                'last_login_at' => $member->last_login_at,
            ]
        ]);
    }

    /**
     *  获取成员列表
     */
    public function list(Request $request)
    {
        $page = $request->get('page', 1);
        $pageSize = $request->get('pageSize', 10);
        $members = MemberModel::paginate($pageSize, ['*'], 'page', $page);
        return json(['code' => 200, 'message' => 'success', 'data' => $members]);
    }

    /**
     *  新增成员
     */
    public function create(Request $request)
    {
        $data = $request->post();
        if(empty($data['nickname']) || empty($data['name']) || empty($data['password'])){
            return json(['code' => 400, 'message' => '必填参数不能为空']);
        }
        // 检查用户是否存在 不允许重复用户名 username
        $member = MemberModel::where('username', $data['username'])->first();
        if($member){
            return json(['code' => 400, 'message' => '用户名已存在, 请更换用户名']);
        }

        $member = new MemberModel();
        $member->nickname = $data['nickname'];
        $member->username = $data['username'];
        $member->name = $data['name'];
        $member->status = $data['status'];
        $member->mobile = $data['mobile'];
        $member->remark = $data['remark'];
        $member->password = password_hash($data['password'], PASSWORD_DEFAULT);

        if($member->save()){
            return json(['code' => 200, 'message' => '新增成功']);
        }else{
            return json(['code' => 500, 'message' => '新增失败']);
        }
    }

    /**
     * 更新用户信息
     * @param Request $request
     * @return \support\Response
     */
    public function update(Request $request)
    {
        // 获取当前用户ID
        $data = $request->post();
        // 查找用户
        $member = MemberModel::find($data['id']);
        if (!$member) {
            return json(['code' => 404, 'message' => '用户不存在']);
        }

        // 更新允许的字段
        $allowedFields = ['name', 'phone', 'nickname', 'status', 'remark'];
        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $member->$field = $data[$field];
            }
        }

        // 如果要更新密码
        if (!empty($data['password'])) {
            $member->password = password_hash($data['password'], PASSWORD_DEFAULT);
        }

        // 保存更新
        if ($member->save()) {
            return json(['code' => 200, 'message' => '更新成功']);
        } else {
            return json(['code' => 500, 'message' => '更新失败']);
        }
    }

    /**
     * 删除成员
     */
    public function delete(Request $request)
    {
        $data = $request->post();
        if(empty($data['id'])){
            return json(['code' => 400, 'message' => '必填参数不能为空']);
        }
        $member = MemberModel::find($data['id']);
        if($member){
            if($member->delete()){
                return json(['code' => 200, 'message' => '删除成功']);
            }else{
                return json(['code' => 500, 'message' => '删除失败']);
            }
        }else{
            return json(['code' => 404, 'message' => '用户不存在']);
        }
    }

    /**
     * 修改密码
     */
    public function updatePassword(Request $request)
    {
        $data = $request->post();
        if(empty($data['id']) || empty($data['password'])){
            return json(['code' => 400, 'message' => '必填参数不能为空']);
        }
        $member = MemberModel::find($data['id']);
        if($member){
            $member->password = password_hash($data['password'], PASSWORD_DEFAULT);
            if($member->save()){
                return json(['code' => 200, 'message' => '修改密码成功']);
            }else{
                return json(['code' => 500, 'message' => '修改密码失败']);
            }
        }else{
            return json(['code' => 404, 'message' => '用户不存在']);
        }
    }

    /**
     * 获取成员角色
     */
    public function getRoles(Request $request, $id)
    {
        $member = MemberModel::findOrFail($id);
        $roles = $member->roles;
        
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => $roles
        ]);
    }

    /**
     * 分配成员角色
     */
    public function assignRoles(Request $request, $id)
    {
        $member = MemberModel::findOrFail($id);
        $roleIds = $request->post('roleIds', []);
        
        $member->roles()->sync($roleIds);

        return json([
            'code' => 200,
            'message' => '角色分配成功'
        ]);
    }
}
