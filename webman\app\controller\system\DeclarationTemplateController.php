<?php
namespace app\controller\system;

use support\Request;
use app\model\DeclarationTemplate;
use app\model\Dictionary;
use app\model\DictionaryItem;
use app\model\Enterprise;
use app\model\App;
use app\model\AppConfigTemplate;
use support\Response;

class DeclarationTemplateController
{
    /**
     * 获取申报模板列表
     */
    public function index(Request $request)
    {
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);
        $name = $request->input('name');

        $query = DeclarationTemplate::with([
            'channel',
            'platformEnterprise',
            'ecommerceEnterprise',
            'logisticsEnterprise',
            'bondedEnterprise',
            'declareEnterprise',
            'transmissionEnterprise',
            'paymentEnterprises',
            'logisticsApp',
            'logisticsTemplate'
        ]);

        if ($name) {
            $query->where('name', 'like', "%{$name}%");
        }

        $total = $query->count();
        $list = $query->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->orderBy('id', 'desc')
            ->get();

        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'list' => $list,
                'total' => $total
            ]
        ]);
    }

    /**
     * 获取表单选项数据
     */
    public function getFormOptions()
    {
        // 获取渠道选项
        $channels = DictionaryItem::join('dictionary', 'dictionary_item.dict_id', '=', 'dictionary.id')
            ->where('dictionary.code', 'channel')
            ->select('dictionary_item.*')
            ->get();

        // 获取贸易方式选项
        $tradeModes = DictionaryItem::join('dictionary', 'dictionary_item.dict_id', '=', 'dictionary.id')
            ->where('dictionary.code', 'trade_mode')
            ->select('dictionary_item.*')
            ->get();

        // 获取海关关区选项
        $customs = DictionaryItem::join('dictionary', 'dictionary_item.dict_id', '=', 'dictionary.id')
            ->where('dictionary.code', 'customs')
            ->select('dictionary_item.*')
            ->get();

        // 获取运输方式选项
        $transportModes = DictionaryItem::join('dictionary', 'dictionary_item.dict_id', '=', 'dictionary.id')
            ->where('dictionary.code', 'cus_transport')
            ->select('dictionary_item.*')
            ->get();

        // 获取国家/地区选项
        $countries = DictionaryItem::join('dictionary', 'dictionary_item.dict_id', '=', 'dictionary.id')
            ->where('dictionary.code', 'country_area')
            ->select('dictionary_item.*')
            ->get();

        // 获取责任主体类型字典项
        $responsiblePartyItems = DictionaryItem::join('dictionary', 'dictionary_item.dict_id', '=', 'dictionary.id')
            ->where('dictionary.code', 'responsible_party')
            ->select('dictionary_item.*')
            ->get();

        // 获取电商平台企业
        $platformEnterprises = Enterprise::whereHas('types', function($query) use ($responsiblePartyItems) {
            $platformType = $responsiblePartyItems->where('value', 'platform')->first();
            if ($platformType) {
                $query->where('type_id', $platformType->id);
            }
        })->get();

        // 获取电商企业
        $ecommerceEnterprises = Enterprise::whereHas('types', function($query) use ($responsiblePartyItems) {
            $ecommerceType = $responsiblePartyItems->where('value', 'enterprise')->first();
            if ($ecommerceType) {
                $query->where('type_id', $ecommerceType->id);
            }
        })->get();

        // 获取支付企业
        $paymentEnterprises = Enterprise::whereHas('types', function($query) use ($responsiblePartyItems) {
            $paymentType = $responsiblePartyItems->where('value', 'payment')->first();
            if ($paymentType) {
                $query->where('type_id', $paymentType->id);
            }
        })->get();

        // 获取物流企业
        $logisticsEnterprises = Enterprise::whereHas('types', function($query) use ($responsiblePartyItems) {
            $logisticsType = $responsiblePartyItems->where('value', 'logistics')->first();
            if ($logisticsType) {
                $query->where('type_id', $logisticsType->id);
            }
        })->get();

        // 获取区内企业
        $bondedEnterprises = Enterprise::whereHas('types', function($query) use ($responsiblePartyItems) {
            $bondedType = $responsiblePartyItems->where('value', 'area')->first();
            if ($bondedType) {
                $query->where('type_id', $bondedType->id);
            }
        })->get();

        // 获取申报企业
        $declareEnterprises = Enterprise::whereHas('types', function($query) use ($responsiblePartyItems) {
            $declareType = $responsiblePartyItems->where('value', 'declare')->first();
            if ($declareType) {
                $query->where('type_id', $declareType->id);
            }
        })->get();

        // 获取传输企业
        $transmissionEnterprises = Enterprise::whereHas('types', function($query) use ($responsiblePartyItems) {
            $transmissionType = $responsiblePartyItems->where('value', 'transmission')->first();
            if ($transmissionType) {
                $query->where('type_id', $transmissionType->id);
            }
        })->get();

         // 获取应用列表
         $logisticsApps = App::where('type', 'logistics')
         ->where('status', 1)
         ->with(['configTemplates' => function($query) {
             $query->select(['id', 'app_id', 'name', 'description']);
         }])
         ->select(['id', 'name', 'code', 'type', 'description'])
         ->get();

        // 获取支付应用
        $paymentApps = App::where('type', 'payment')
            ->where('status', 1)
            ->with(['configTemplates' => function($query) {
                $query->select(['id', 'app_id', 'name', 'description']);
            }])
            ->select(['id', 'name', 'code', 'type', 'description'])
            ->get();

        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'channels' => $channels,
                'tradeModes' => $tradeModes,
                'customs' => $customs,
                'transportModes' => $transportModes,
                'countries' => $countries,
                'platformEnterprises' => $platformEnterprises,
                'ecommerceEnterprises' => $ecommerceEnterprises,
                'paymentEnterprises' => $paymentEnterprises,
                'logisticsEnterprises' => $logisticsEnterprises,
                'bondedEnterprises' => $bondedEnterprises,
                'declareEnterprises' => $declareEnterprises,
                'transmissionEnterprises' => $transmissionEnterprises,
                'logisticsApps' => $logisticsApps,
                'paymentApps' => $paymentApps
            ]
        ]);
    }

    /**
     * 创建申报模板
     */
    public function store(Request $request)
    {
        $data = $request->post();
        
        // 基础验证
        if (empty($data['name']) || mb_strlen($data['name']) > 10) {
            return json(['code' => 400, 'message' => '请输入10字以内的模板名称']);
        }
        if (empty($data['channel_id'])) {
            return json(['code' => 400, 'message' => '请选择渠道']);
        }

        // 验证物流应用
        if (!empty($data['logistics_app_id']) && empty($data['logistics_template_id'])) {
            return json(['code' => 400, 'message' => '请选择物流应用配置模板']);
        }

        // 验证支付企业和应用
        // if (!empty($data['payment_enterprise_ids'])) {
        //     if (empty($data['payment_apps']) || !is_array($data['payment_apps'])) {
        //         return json(['code' => 400, 'message' => '请为每个支付企业选择对应的支付应用']);
        //     }
        //     foreach ($data['payment_enterprise_ids'] as $enterpriseId) {
        //         if (empty($data['payment_apps'][$enterpriseId]['app_id'])) {
        //             return json(['code' => 400, 'message' => '请选择支付应用']);
        //         }
        //         if (empty($data['payment_apps'][$enterpriseId]['template_id'])) {
        //             return json(['code' => 400, 'message' => '请选择支付应用配置模板']);
        //         }
        //     }
        // }

        // 开启事务
        \support\Db::beginTransaction();
        try {
            // 创建申报模板
            $template = DeclarationTemplate::create([
                'name' => $data['name'],
                'channel_id' => $data['channel_id'],
                'trade_mode' => $data['trade_mode'],
                'platform_enterprise_id' => $data['platform_enterprise_id'],
                'ecommerce_enterprise_id' => $data['ecommerce_enterprise_id'],
                'logistics_enterprise_id' => $data['logistics_enterprise_id'],
                'bonded_enterprise_id' => $data['bonded_enterprise_id'],
                'declare_enterprise_id' => $data['declare_enterprise_id'],
                'transmission_enterprise_id' => $data['transmission_enterprise_id'],
                'declare_customs' => $data['declare_customs'],
                'port_customs' => $data['port_customs'],
                'transport_mode' => $data['transport_mode'],
                'departure_country' => $data['departure_country'],
                'account_book_no' => $data['account_book_no'],
                'logistics_app_id' => $data['logistics_app_id'] ?? null,
                'logistics_template_id' => $data['logistics_template_id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'created_by' => $request->user->id ?? null,
                'updated_by' => $request->user->id ?? null
            ]);

            // 关联支付企业和应用
            if (!empty($data['payment_enterprise_ids'])) {
                $paymentData = [];
                foreach ($data['payment_enterprise_ids'] as $enterpriseId) {
                    $appData = $data['payment_apps'][$enterpriseId];
                    $paymentData[$enterpriseId] = [
                        'app_id' => $appData['app_id'] ?? null,
                        'app_template_id' => $appData['template_id'] ?? null,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                }
                $template->paymentEnterprises()->attach($paymentData);
            }

            \support\Db::commit();
            return json([
                'code' => 200,
                'message' => '创建成功',
                'data' => $template
            ]);
        } catch (\Exception $e) {
            \support\Db::rollBack();
            return json([
                'code' => 500,
                'message' => '创建失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新申报模板
     */
    public function update(Request $request, $id)
    {
        $template = DeclarationTemplate::find($id);
        if (!$template) {
            return json(['code' => 404, 'message' => '模板不存在']);
        }

        $data = $request->post();
        
        // 基础验证
        if (!empty($data['name']) && mb_strlen($data['name']) > 10) {
            return json(['code' => 400, 'message' => '模板名称不能超过10个字']);
        }

        // 验证物流应用
        if (!empty($data['logistics_app_id']) && empty($data['logistics_template_id'])) {
            return json(['code' => 400, 'message' => '请选择物流应用配置模板']);
        }

        // 验证支付企业和应用
        // if (!empty($data['payment_enterprise_ids'])) {
        //     if (empty($data['payment_apps']) || !is_array($data['payment_apps'])) {
        //         return json(['code' => 400, 'message' => '请为每个支付企业选择对应的支付应用']);
        //     }
        //     foreach ($data['payment_enterprise_ids'] as $enterpriseId) {
        //         if (empty($data['payment_apps'][$enterpriseId]['app_id'])) {
        //             return json(['code' => 400, 'message' => '请选择支付应用']);
        //         }
        //         if (empty($data['payment_apps'][$enterpriseId]['template_id'])) {
        //             return json(['code' => 400, 'message' => '请选择支付应用配置模板']);
        //         }
        //     }
        // }

        // 开启事务
        \support\Db::beginTransaction();
        try {
            // 更新基本信息
            $template->update([
                'name' => $data['name'] ?? $template->name,
                'channel_id' => $data['channel_id'] ?? $template->channel_id,
                'trade_mode' => $data['trade_mode'] ?? $template->trade_mode,
                'platform_enterprise_id' => $data['platform_enterprise_id'] ?? $template->platform_enterprise_id,
                'ecommerce_enterprise_id' => $data['ecommerce_enterprise_id'] ?? $template->ecommerce_enterprise_id,
                'logistics_enterprise_id' => $data['logistics_enterprise_id'] ?? $template->logistics_enterprise_id,
                'bonded_enterprise_id' => $data['bonded_enterprise_id'] ?? $template->bonded_enterprise_id,
                'declare_enterprise_id' => $data['declare_enterprise_id'] ?? $template->declare_enterprise_id,
                'transmission_enterprise_id' => $data['transmission_enterprise_id'] ?? $template->transmission_enterprise_id,
                'declare_customs' => $data['declare_customs'] ?? $template->declare_customs,
                'port_customs' => $data['port_customs'] ?? $template->port_customs,
                'transport_mode' => $data['transport_mode'] ?? $template->transport_mode,
                'departure_country' => $data['departure_country'] ?? $template->departure_country,
                'account_book_no' => $data['account_book_no'] ?? $template->account_book_no,
                'logistics_app_id' => $data['logistics_app_id'] ?? $template->logistics_app_id,
                'logistics_template_id' => $data['logistics_template_id'] ?? $template->logistics_template_id,
                'updated_at' => date('Y-m-d H:i:s'),
                'updated_by' => $request->user->id ?? null
            ]);

            // 更新支付企业和应用关联
            if (isset($data['payment_enterprise_ids'])) {
                $paymentData = [];
                foreach ($data['payment_enterprise_ids'] as $enterpriseId) {
                    $appData = $data['payment_apps'][$enterpriseId];
                    $paymentData[$enterpriseId] = [
                        'app_id' => $appData['app_id'] ?? null,
                        'app_template_id' => $appData['template_id'] ?? null,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                }
                $template->paymentEnterprises()->sync($paymentData);
            }

            \support\Db::commit();
            return json([
                'code' => 200,
                'message' => '更新成功'
            ]);
        } catch (\Exception $e) {
            \support\Db::rollBack();
            return json([
                'code' => 500,
                'message' => '更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除申报模板
     */
    public function destroy(Request $request, $id)
    {
        $template = DeclarationTemplate::find($id);
        if (!$template) {
            return json(['code' => 404, 'message' => '模板不存在']);
        }

        // 开启事务
        \support\Db::beginTransaction();
        try {
            // 删除支付企业关联（模型会自动处理）
            $template->delete();

            \support\Db::commit();
            return json([
                'code' => 200,
                'message' => '删除成功'
            ]);
        } catch (\Exception $e) {
            \support\Db::rollBack();
            return json([
                'code' => 500,
                'message' => '删除失败：' . $e->getMessage()
            ]);
        }
    }
} 