import request from '@/utils/request';

// 获取角色列表
export function getRoleList(params) {
  return request({
    url: '/api/system/role/getList',
    method: 'get',
    params
  });
}

// 创建角色
export function createRole(data) {
  return request({
    url: '/api/system/role/postCreate',
    method: 'post',
    data
  });
}

// 更新角色
export function updateRole(id, data) {
  return request({
    url: `/api/system/role/putUpdate/${id}`,
    method: 'put',
    data
  });
}

// 删除角色
export function deleteRole(id) {
  return request({
    url: `/api/system/role/deleteRole/${id}`,
    method: 'delete'
  });
}

// 获取角色权限
export function getRolePermissions(id) {
  return request({
    url: `/api/system/role/getPermissions/${id}`,
    method: 'get'
  });
}

// 分配角色权限
export function assignRolePermissions(id, data) {
  return request({
    url: `/api/system/role/permissions/${id}`,
    method: 'post',
    data
  });
} 