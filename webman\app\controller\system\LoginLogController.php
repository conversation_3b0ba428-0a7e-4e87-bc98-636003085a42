<?php

namespace app\controller\system;

use support\Request;
use app\controller\Controller;
use app\model\LoginLog;

class <PERSON>ginLogController extends Controller
{
    /**
     * 获取登录日志列表
     * @param Request $request
     * @return \support\Response
     */
    public function list(Request $request)
    {
        $page = $request->get('page', 1);
        $pageSize = $request->get('pageSize', 10);
        $username = $request->get('username', '');
        $status = $request->get('status', '');
        $startTime = $request->get('startTime', '');
        $endTime = $request->get('endTime', '');

        $query = LoginLog::with('member')
            ->when($username, function ($query) use ($username) {
                return $query->where('username', 'like', "%{$username}%");
            })
            ->when($status, function ($query) use ($status) {
                return $query->where('status', $status);
            })
            ->when($startTime && $endTime, function ($query) use ($startTime, $endTime) {
                return $query->whereBetween('created_at', [$startTime, $endTime]);
            });

        $total = $query->count();
        $logs = $query->orderBy('created_at', 'desc')
            ->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->get();

        return json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'list' => $logs,
                'total' => $total,
                'page' => $page,
                'pageSize' => $pageSize
            ]
        ]);
    }

    /**
     * 清空登录日志
     * @return \support\Response
     */
    public function clear()
    {
        try {
            LoginLog::truncate();
            return json(['code' => 200, 'message' => '清空成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '清空失败：' . $e->getMessage()]);
        }
    }
} 