<template>
  <a-config-provider :locale="locale">
    <a-layout>
      <!-- 左侧边栏 -->
      <a-layout-sider
        v-model:collapsed="collapsed"
        class="sidebar"
        :trigger="null"
        collapsible
        :style="{ height: '100vh', position: 'fixed', left: 0, top: 0, bottom: 0, zIndex: 1001 }"
      >
        <!-- 系统logo -->
        <!-- <div class="system-container">
          <img class="system-logo" v-show="collapsed" src="@/assets/logo.png" alt="系统logo" />
          <span class="system-name" v-show="!collapsed">高度差进口报关系统</span>
        </div> -->

        <!-- 用户信息区域 -->
        <div class="user-profile" v-if="userInfo.name || userInfo.nickname">
          <img class="system-logo" src="@/assets/logo.png" alt="系统logo" />
          <div class="user-info">
            <div class="system-name" v-show="!collapsed">高度差 · 速通关</div>
            <div class="user-role">{{ userInfo.nickname }}</div>
          </div>
        </div>
        <div class="user-profile" v-else>
          <!-- 骨架屏-->
          <a-skeleton-avatar :size="50" />
          <div class="user-info" v-show="!collapsed">
            <a-skeleton-input :size="'large'" />
          </div>
        </div>

        <!-- 菜单容器 -->
        <div class="menu-container">
          <!-- 主菜单 -->
            <a-menu
            v-model:selectedKeys="selectedKeys"
            theme="light"
            mode="inline"
            class="custom-menu"
          >
            <a-menu-item 
              v-for="item in menuItems" 
              :key="item.key"
              class="custom-menu-item"
              style="margin: 30px 0 !important;"
              @click="handleMenuClick(item)"
            >
              <template #icon>
                <div class="menu-icon-wrapper" :class="{ active: selectedKeys.includes(item.key) }">
                  <component v-if="item.icon" :is="item.icon" />
                  <span v-else>{{ item.label[0] }}</span>
                </div>
              </template>
              <span>{{ item.label }}</span>
            </a-menu-item>
          </a-menu>

          <!-- 查看更多按钮 -->
          <!-- <div v-if="showViewMore" class="view-more">
            <a-button type="text" @click="handleViewMore">
              <DownOutlined /> 查看更多
            </a-button>
          </div> -->
        </div>

        <!-- 退出登录按钮 -->
        <div class="logout-wrapper">
          <a-menu
            theme="light"
            mode="inline"
            class="logout-menu"
          >
            <a-menu-item 
              :key="menuConfig.logoutMenuItem.key" 
              class="custom-menu-item" 
              @click="handleLogout"
            >
              <template #icon>
                <span class="menu-icon-wrapper">
                  <component :is="menuConfig.logoutMenuItem.icon" />
                </span>
              </template>
              <span>{{ menuConfig.logoutMenuItem.label }}</span>
            </a-menu-item>
          </a-menu>

          <!-- 版权信息 -->
          <div class="copyright-info">
            <a href="https://www.gaodux.com" target="_blank" class="website-link">
              高度差
            </a>
            <div class="copyright-text">© {{ currentYear }} All Rights Reserved</div>
          </div>
        </div>
      </a-layout-sider>

      <!-- 右侧内容区 -->
      <a-layout class="site-layout" :style="{ marginLeft: collapsed ? '80px' : '200px' }">
        <!-- 顶部导航 - 固定定位 -->
        <a-layout-header class="header">
          <div class="header-content">
            <div class="header-left">
              <menu-unfold-outlined
                v-if="collapsed"
                class="trigger"
                @click="() => (collapsed = !collapsed)"
              />
              <menu-fold-outlined
                v-else
                class="trigger"
                @click="() => (collapsed = !collapsed)"
              />
              <!-- 添加页面标题 -->
              <div class="page-title">{{ currentPageTitle }}</div>
            </div>
            <div class="header-right">
              <a-space>
                <a-badge count="5">
                  <BellOutlined class="header-icon" />
                </a-badge>
              </a-space>
            </div>
          </div>
        </a-layout-header>

        <!-- 内容区域 -->
        <div class="main-container">
          <a-watermark :content="[ userInfo.nickname + currentTime , '速通关 - 智能通关平台']" :font="{ fontSize: 13 }">
            <a-layout-content class="content">
              <router-view></router-view>
            </a-layout-content>
          </a-watermark>
        </div>
      </a-layout>
    </a-layout>
  </a-config-provider>
</template>

<script>
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  WalletOutlined,
  FundOutlined,
  TeamOutlined,
  SwapOutlined,
  BellOutlined,
  LogoutOutlined,
  DownOutlined,
  HomeOutlined,
  ShoppingOutlined,
  OrderedListOutlined,
  SettingOutlined,
  AppstoreOutlined
} from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import { menuConfig, defaultSelectedKey, getMenuKeyByPath } from '@/config/menu';
import { ref, watch, getCurrentInstance, computed } from 'vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { useUserStore } from '@/stores/user';
import { getUserInfo as getUserInfoApi } from '@/api/user';

export default {
  name: 'Layout',
  components: {
    MenuFoldOutlined,
    MenuUnfoldOutlined,
    WalletOutlined,
    FundOutlined,
    TeamOutlined,
    SwapOutlined,
    BellOutlined,
    LogoutOutlined,
    DownOutlined,
    HomeOutlined,
    ShoppingOutlined,
    OrderedListOutlined,
    SettingOutlined,
    AppstoreOutlined
  },
  setup() {
    // 获取组件实例
    const { proxy } = getCurrentInstance();
    
    // 页面标题映射
    const pageTitleMap = {
      '/home': '首页',
      '/members': '成员管理',
      '/products': '商品管理',
      '/products/registered': '备案商品',
      '/products/categories': '商品分类',
      '/products/channels': '渠道商品',
      '/orders': '订单管理',
      '/orders/center': '订单中心',
      '/orders/screen': '数据大屏',
      '/apps': '应用中心',
      '/system': '系统管理',
      '/system/enterprise': '企业管理',
      '/system/declaration-template': '申报模板',
      '/system/permission': '权限管理',
      '/system/dict': '字典设置',
      '/system/apikey': 'API密钥',
      '/system/apikey/test': 'API在线测试',
      '/system/log': '日志管理',
      '/system/schedule': '定时任务',
      '/system/download': '下载中心',
      '/system/info': '系统信息'
    };

    // 当前页面标题
    const currentPageTitle = ref('');

    // 监听路由变化
    watch(() => proxy.$route.path, (newPath) => {
      // 更新页面标题
      currentPageTitle.value = pageTitleMap[newPath] || '';
      
      // 更新浏览器标题
      document.title = currentPageTitle.value 
        ? `${currentPageTitle.value} - 高度差进口报关系统` 
        : '高度差进口报关系统';
    }, { immediate: true });

    // 添加语言配置
    const locale = zhCN;

    const userStore = useUserStore();
    
    // 获取用户菜单项
    const selectedKeys = ref([defaultSelectedKey]);
    const iconMap = {
      HomeOutlined: HomeOutlined,
      TeamOutlined: TeamOutlined,
      ShoppingOutlined: ShoppingOutlined,
      OrderedListOutlined: OrderedListOutlined,
      SettingOutlined: SettingOutlined,
      AppstoreOutlined: AppstoreOutlined
    };

    const menuItems = computed(() => {
      return (userStore.permissions || []).map(item => ({
        key: item.id.toString(),
        label: item.name,
        icon: item.icon ? iconMap[item.icon] : null,
        path: item.path
      }));
    });

    // 监听路由变化更新选中菜单项
    watch(() => proxy.$route.path, (newPath) => {
      // 更新页面标题
      currentPageTitle.value = pageTitleMap[newPath] || '';
      
      // 更新浏览器标题
      document.title = currentPageTitle.value 
        ? `${currentPageTitle.value} - 高度差进出口报关系统` 
        : '高度差进出口报关系统';
        
      // 更新选中菜单项
      selectedKeys.value = [getMenuKeyByPath(newPath, menuItems.value)];
    }, { immediate: true });

    return {
      currentPageTitle,
      locale,
      menuItems,
      selectedKeys
    };
  },
  data() {
    return {
      collapsed: false,
      menuConfig,
      userInfo: {
        nickname: '',
        avatar: '',
        role: ''
      },
      showViewMore: false,
      currentYear: new Date().getFullYear(),
      // 当前时间
      currentTime: new Date().toLocaleString()
    };
  },
  async created() {
    await this.getUserInfo();
  },
  mounted() {
    this.checkMenuOverflow();
    window.addEventListener('resize', this.checkMenuOverflow);
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.checkMenuOverflow);
  },
  methods: {
    checkMenuOverflow() {
      const menuContainer = document.querySelector('.menu-container');
      const menu = document.querySelector('.custom-menu');
      if (menuContainer && menu) {
        this.showViewMore = menu.scrollHeight > menuContainer.clientHeight;
      }
    },
    handleViewMore() {
      // 实现查看更多的逻辑
      message.info('查看更多功能待实现');
    },
    handleLogout() {
      const app = this;

      Modal.confirm({
        title: '退出登陆',
        content: '确定要退出登陆账户吗？',
        onOk() {
          return new Promise((resolve, reject) => {
            setTimeout(() => {
              // 退出登陆
              app.logout();
              
              // Modal.destroy();
              resolve();
            }, 1000);
            
          }).catch(() => console.log('Oops errors!'));
        },
        onCancel() {},
      });
      
    },

    // 退出登陆
    logout() {
      // 跳转到登录页
      const userStore = useUserStore();
      userStore.logout();
      
      message.success('已退出登录');
      this.$router.push('/login');
    },

    handleMenuClick(item) {
      if (item.path) {
        this.$router.push(item.path);
      }
    },
    async getUserInfo() {
      try {
        const response = await getUserInfoApi();
        if (response.code === 200) {
          this.userInfo = response.data;
        } else {
          message.error('获取用户信息失败');
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        message.error('获取用户信息失败');
      }
    }
  }
};
</script>

<style scoped>
.site-layout {
  min-height: 100vh;
}

.sidebar {
  background: #f5f5f5;
  box-shadow: none;
}

.system-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  border-bottom: 1px solid #adadad;
  margin-left: 15px;
}

.system-logo {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 1px solid #27234328;
}

.system-name {
  font-size: 18px;
  font-weight: 600;
  color: #272343;
}

.menu-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 文字不允许选择 */
.custom-menu {
  user-select: none;
}

.custom-menu {
  padding: 8px 12px;
  background: transparent;
}

.custom-menu-item {
  height: 44px !important;
  line-height: 44px !important;
  margin: 4px 0;
  padding: 0 12px !important;
  border-radius: 8px;
}

.menu-icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  transition: all 0.3s;
  font-size: 16px;
  color: #595959;
}

.menu-icon-wrapper.active {
  background-color: #272343;
  color: white;
}

.custom-menu-item:hover .menu-icon-wrapper {
  background-color: #e8e8ef;
  color: #272343;
}

.logout-wrapper {
  padding: 18px 12px;
  margin-top: auto;
}

.logout-menu {
  background: transparent;
}

.logout-menu .custom-menu-item {
  color: #595959;
}

.user-profile {
  margin-top: 10px;
  padding: 32px 10px;
  display: flex;
  align-items: center;
  flex-flow: column;
  gap: 16px;
  border-bottom: 1px solid #d3cdcd;
  margin-bottom: 8px;
  margin-left: 10px;
}

.user-info {
  display: flex;
  align-items: center;
  flex-flow: column;
  gap: 16px;
}

.user-name {
  font-weight: 500;
  font-size: 16px;
  color: #1f2937;
}

.user-role {
  font-size: 13px;
  color: #6b7280;
  margin-top: 2px;
}

.header {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1000;
  background: #f5f5f5;
  padding: 0;
  height: 64px;
  margin-left: 200px;
  transition: margin-left 0.2s;
}

.header-content {
  max-width: calc(100% - 48px);
  margin: 0 auto;
  padding: 0 24px;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* background: #fff; */
  /* box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08); */
  border-radius: 0 0 10px 10px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.trigger {
  font-size: 18px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #272343;
}

.header-icon {
  font-size: 16px;
  cursor: pointer;
  padding: 0 12px;
}

.header-icon:hover {
  color: #272343;
}

.main-container {
  margin-top: 64px;
  min-height: calc(100vh - 64px);
  overflow-y: auto;
}

.content {
  margin: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  min-height: calc(100vh - 112px);
}

/* Deep selectors */
:deep(.ant-layout-sider-children) {
  display: flex;
  flex-direction: column;
}

:deep(.ant-menu-item-selected) {
  background-color: rgba(39, 35, 67, 0) !important;
  font-weight: 600 !important;
}

:deep(.ant-menu-item-selected .menu-icon-wrapper) {
  background-color: #272343 !important;
  color: white !important;
}

:deep(.ant-menu-item:hover) {
  background-color: #f5f5f5 !important;
}

:deep(.ant-menu-item) {
  color: #595959 !important;
  font-weight: 500;
}

:deep(.ant-menu-item-selected) {
  color: #272343 !important;
}

:deep(.ant-menu-item .ant-menu-item-icon) {
  margin-right: 12px;
}

:deep(.ant-layout-sider-collapsed) ~ .site-layout .header {
  margin-left: 80px;
}

/* 去除所有边框 */
:deep(.ant-menu),
:deep(.ant-menu-light),
:deep(.ant-menu-inline),
:deep(.ant-menu.ant-menu-light.ant-menu-root.ant-menu-inline),
:deep(.ant-menu.ant-menu-light.ant-menu-root.ant-menu-vertical) {
  border: none !important;
  border-inline-end: none !important;
}

.logout-menu:deep(.ant-menu),
.logout-menu:deep(.ant-menu-light),
.logout-menu:deep(.ant-menu-inline) {
  border: none !important;
  border-inline-end: none !important;
}

:deep(.ant-menu-item)::after,
:deep(.ant-menu-light .ant-menu-item)::after,
:deep(.ant-menu-inline .ant-menu-item)::after {
  border-right: none !important;
  border-inline-end: none !important;
}

.copyright-info {
  border-top: 1px solid #d3cdcd;
  margin-top: 10px;
  padding: 20px;
  margin-left: 10px;
  text-align: center;
}

.website-link {
  display: block;
  color: #272343;
  font-size: 14px;
  text-decoration: none;
  margin-bottom: 4px;
  transition: color 0.3s;
}

.website-link:hover {
  color: #494866;
  text-decoration: underline;
}

.copyright-text {
  color: #6b7280;
  font-size: 12px;
}

/* 折叠时的样式 */
:deep(.ant-layout-sider-collapsed) {
  .menu-icon-wrapper {
    margin: 0 auto;
  }
  
  .ant-menu-item {
    padding: 0 calc(50% - 16px) !important;
  }

  .copyright-info {
    padding: 0 4px;
  }
  
  .website-link {
    font-size: 12px;
    padding: 10px 0;
    font-weight: 600;
  }

  .copyright-text {
    display: none;
  }
}

.page-title {
  margin-left: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #1f2937;
}
</style>
