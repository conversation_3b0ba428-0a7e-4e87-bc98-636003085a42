<?php

namespace app\process;

use Workerman\Timer;
use Workerman\Worker;
use app\model\Schedule;
use app\controller\system\ScheduleController;

class ScheduleProcess
{
    public function onWorkerStart(Worker $worker)
    {
        // 记录进程启动时间
        file_put_contents(runtime_path() . '/schedule.start', date('Y-m-d H:i:s'));
        
        // 添加心跳定时器，每30秒更新一次
        Timer::add(30, function() {
            file_put_contents(runtime_path() . '/schedule.heartbeat', date('Y-m-d H:i:s'));
        });
        
        // 获取所有启用状态的任务
        $schedules = Schedule::where('status', 1)->get();
        
        // 初始化定时任务控制器
        $controller = new ScheduleController();
        
        // 遍历添加到定时任务
        foreach ($schedules as $schedule) {
            $controller->addToCrontab($schedule);
        }
    }
} 