import request from '@/utils/request';

// 获取应用列表
export function getApps(params) {
  return request({
    url: '/system/apps',
    method: 'get',
    params
  });
}

// 安装应用
export function installApp(data) {
  return request({
    url: '/system/apps/install',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 卸载应用
export function uninstallApp(id) {
  return request({
    url: `/system/apps/${id}/uninstall`,
    method: 'post'
  });
}

// 更新应用设置
export function updateAppSettings(id, data) {
  return request({
    url: `/system/apps/${id}/settings`,
    method: 'put',
    data
  });
}

// 获取应用安装日志
export function getInstallLogs(id) {
  return request({
    url: `/system/apps/${id}/logs`,
    method: 'get'
  });
}

// 获取应用详情
export function getAppDetail(id) {
  return request({
    url: `/system/apps/${id}`,
    method: 'get'
  });
}

// 获取应用日志列表
export function getAppLogs(params) {
  return request({
    url: '/system/apps/logs',
    method: 'get',
    params
  });
}

// 获取应用配置模板列表
export function getAppTemplates(appId) {
  return request({
    url: `/system/apps/${appId}/templates`,
    method: 'get'
  });
}

// 保存应用配置模板
export function saveAppTemplate(appId, data) {
  return request({
    url: `/system/apps/${appId}/templates`,
    method: 'post',
    data
  });
}

// 删除应用配置模板
export function deleteAppTemplate(appId, templateId) {
  return request({
    url: `/system/apps/${appId}/templates/${templateId}`,
    method: 'delete'
  });
} 