<?php

namespace app\controller;

use support\Request;
use app\model\Member;
use app\model\LoginLog;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Webman\RateLimiter\Annotation\RateLimiter;

/**
 * 后台登陆控制器
 */
class PassportController extends Controller
{
    /**
     * 用户登录
     * @param Request $request
     * @return \support\Response
     */
    #[RateLimiter(limit: 3, ttl: 60, key: RateLimiter::IP, message: '登录过于频繁，请稍后再试')]
    public function login(Request $request)
    {
        $username = $request->post('username');
        $password = $request->post('password');

        // 参数验证
        if (empty($username) || empty($password)) {
            return json(['code' => 400, 'message' => '用户名和密码不能为空']);
        }

        // 查找用户
        $member = Member::where('username', $username)->first();
        
        // IP地址
        $ip = $request->getRealIp();
        // 如果IP为127.0.0.1，则默认是本地
        if ($ip == '127.0.0.1') {
            $ip = '内网IP';
            $address = '内网';
        }else{
            // 获取IP地址
            $response = get('https://ip9.com.cn/get?ip=' . $ip);
            $response = json_decode($response, true);

            // 如果请求成功，则获取地址
            if ($response['ret'] == 200) {
                $address = "【" . $response['data']['isp'] . "】" . $response['data']['country'] . $response['data']['prov'] . $response['data']['city'];
            }else{
                $address = "【未知】" . $ip;
            }
        }

        // 浏览器
        
        // 准备日志数据
        $logData = [
            'username' => $username,
            'ip' => $ip,
            'location' => $address,
            'browser' => $this->getBrowser($request),
            'os' => $this->getOS($request),
            'status' => 'failed',
            'message' => '',
            'member_id' => $member ? $member->id : 0
        ];

        // 用户不存在
        if (!$member) {
            $logData['message'] = '用户不存在';
            LoginLog::record($logData);
            return json(['code' => 402, 'message' => '用户名或密码错误']);
        }

        // 检查账户状态
        if (!$member->status) {
            $logData['message'] = '账户已禁用';
            LoginLog::record($logData);
            return json(['code' => 403, 'message' => '账户已被禁用']);
        }

        // 检查是否被锁定
        if ($member->isLocked()) {
            $lockTime = strtotime($member->locked_until) - time();
            $minutes = ceil($lockTime / 60);
            $logData['message'] = '账户已锁定';
            LoginLog::record($logData);
            return json(['code' => 403, 'message' => "账户已被锁定，请{$minutes}分钟后再试"]);
        }

        // 验证密码
        if (!$member->verifyPassword($password)) {
            // 记录登录失败
            $member->recordLoginFail();
            
            $logData['message'] = '密码错误';
            LoginLog::record($logData);
            // 如果还未被锁定，显示剩余尝试次数
            if (!$member->isLocked()) {
                $remainingAttempts = 10 - $member->login_fails;
                return json(['code' => 402, 'message' => "密码错误，还剩{$remainingAttempts}次尝试机会"]);
            } else {
                return json(['code' => 403, 'message' => '密码错误次数过多，账户已被锁定1小时']);
            }
            return json(['code' => 402, 'message' => '用户名或密码错误']);
        }

        // 登录成功，重置登录失败次数
        $member->resetLoginFails();
        
        // 更新最后登录信息
        $member->last_login_at = date('Y-m-d H:i:s');
        $member->last_login_ip = $request->getRealIp();
        $member->save();

        // 生成token
        $token = $this->createToken($member);

        // 记录成功登录日志
        $logData['status'] = 'success';
        $logData['message'] = '登录成功';
        LoginLog::record($logData);

        return json([
            'code' => 200,
            'message' => '登录成功',
            'data' => [
                'token' => $token,
                'user' => [
                    'id' => $member->id,
                    'username' => $member->username,
                    'name' => $member->name,
                    'nickname' => $member->nickname
                ]
            ]
        ]);
    }

    /**
     * 用户登出
     * @param Request $request
     * @return \support\Response
     */
    public function logout(Request $request)
    {
        // 这里可以添加token黑名单等逻辑
        return json(['code' => 200, 'message' => '登出成功']);
    }

    /**
     * 创建JWT token
     * @param Member $member
     * @return string
     */
    protected function createToken($member)
    {
        $key = config('app.jwt_key');
        $accessExpire = config('app.jwt_expire', 7200); // access_token 2小时过期
        $refreshExpire = 30 * 24 * 3600;  // refresh_token 30天过期

        // 生成 access_token
        $accessPayload = [
            'iat' => time(),
            'exp' => time() + $accessExpire,
            'data' => [
                'uid' => $member->id,
                'type' => 'access_token'
            ]
        ];
        $accessToken = JWT::encode($accessPayload, $key, 'HS256');

        // 生成 refresh_token
        $refreshPayload = [
            'iat' => time(),
            'exp' => time() + $refreshExpire,
            'data' => [
                'uid' => $member->id,
                'type' => 'refresh_token'
            ]
        ];
        $refreshToken = JWT::encode($refreshPayload, $key, 'HS256');

        return [
            'access_token' => $accessToken,
            'refresh_token' => $refreshToken,
            'expire_in' => $accessExpire
        ];
    }

     /**
     * 刷新访问令牌
     * @param Request $request
     * @return \support\Response
     */
    public function refreshToken(Request $request)
    {
        $refreshToken = $request->post('refresh_token');
        if (!$refreshToken) {
            return json(['code' => 400, 'message' => '缺少refresh_token']);
        }

        try {
            $key = config('app.jwt_key');
            $decoded = JWT::decode($refreshToken, new Key($key, 'HS256'));
            
            // 验证是否是refresh_token
            if (!isset($decoded->data->type) || $decoded->data->type !== 'refresh_token') {
                return json(['code' => 400, 'message' => '无效的refresh_token']);
            }

            // 获取用户信息
            $member = Member::find($decoded->data->uid);
            if (!$member) {
                return json(['code' => 404, 'message' => '用户不存在']);
            }

            // 生成新的token
            $tokens = $this->createToken($member);
            
            return json([
                'code' => 200,
                'message' => '刷新成功',
                'data' => $tokens
            ]);
        } catch (\Exception $e) {
            return json(['code' => 401, 'message' => 'refresh_token已过期或无效']);
        }
    }

    /**
     * 获取浏览器信息
     * @param Request $request
     * @return string
     */
    protected function getBrowser($request)
    {
        $userAgent = $request->header('user-agent');
        if (preg_match('/MSIE\s+([^\s;]+)/i', $userAgent, $matches)) {
            return "Internet Explorer {$matches[1]}";
        }
        if (preg_match('/Chrome\/([^\s;]+)/i', $userAgent, $matches)) {
            return "Chrome {$matches[1]}";
        }
        if (preg_match('/Firefox\/([^\s;]+)/i', $userAgent, $matches)) {
            return "Firefox {$matches[1]}";
        }
        if (preg_match('/Safari\/([^\s;]+)/i', $userAgent, $matches)) {
            return "Safari {$matches[1]}";
        }
        return $userAgent;
    }

    /**
     * 获取操作系统信息
     * @param Request $request
     * @return string
     */
    protected function getOS($request)
    {
        $userAgent = $request->header('user-agent');
        if (preg_match('/windows/i', $userAgent)) {
            return 'Windows';
        }
        if (preg_match('/linux/i', $userAgent)) {
            return 'Linux';
        }
        if (preg_match('/mac/i', $userAgent)) {
            return 'MacOS';
        }
        if (preg_match('/android/i', $userAgent)) {
            return 'Android';
        }
        if (preg_match('/iphone/i', $userAgent)) {
            return 'iOS';
        }
        return 'Unknown';
    }
}