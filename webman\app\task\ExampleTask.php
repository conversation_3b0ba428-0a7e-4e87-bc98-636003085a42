<?php

namespace app\task;

// 示例任务，在webman/app/task目录下创建新的任务类，并实现相应的任务方法
// 任务类名必须以Task结尾，例如：ExampleTask2.php
// 任务方法名必须以task开头，例如：taskSendSystemReport()
class ExampleTask
{
    /**
     * 发送系统状态报告
     */
    public function taskSendSystemReport()
    {
        // 这里实现发送系统状态报告的逻辑
        return true;
    }

    /**
     * 清理临时文件
     */
    public function taskCleanTempFiles()
    {
        // 这里实现清理临时文件的逻辑
        return true;
    }

    /**
     * 生成系统报表
     */
    public function generateReport()
    {
        // 这里实现生成系统报表的逻辑
        return true;
    }
} 