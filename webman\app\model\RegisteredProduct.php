<?php
namespace app\model;

use support\Model;

class RegisteredProduct extends Model
{
    /**
     * 与模型关联的表名
     */
    protected $table = 'registered_products';

    /**
     * 可以批量赋值的属性
     */
    protected $fillable = [
        'goods_type',
        'sequence_no',
        'filing_no',
        'goods_name',
        'goods_no',
        'bar_code',
        'goods_tax',
        'hs_code',
        'goods_model',
        'origin_country_id',
        'unit_id',
        'unit1_id',
        'unit2_id',
        'qty1',
        'qty2',
        'net_weight',
        'gross_weight',
        'category_id',
        'price',
        'status',
        'description',
        'created_by',
        'updated_by'
    ];

    /**
     * 自动维护时间戳
     */
    public $timestamps = true;

    /**
     * 获取商品所属分类
     */
    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    /**
     * 获取商品所属国家 字典项ID dict_id
     */
    public function originCountry()
    {
        return $this->belongsTo(DictionaryItem::class, 'origin_country_id', 'id');
    }

    /**
     * 获取商品所属单位 字典项ID dict_id
     */
    public function unit()
    {
        return $this->belongsTo(DictionaryItem::class, 'unit_id', 'id');
    }

    /**
     * 获取商品所属单位1 字典项ID dict_id
     */
    public function unit1()
    {
        return $this->belongsTo(DictionaryItem::class, 'unit1_id', 'id');
    }

    /**
     * 获取商品所属单位2 字典项ID dict_id
     */
    public function unit2()
    {
        return $this->belongsTo(DictionaryItem::class, 'unit2_id', 'id');
    }

    // /**
    //  * 获取商品图片
    //  */
    // public function images()
    // {
    //     return $this->hasMany(ProductImage::class, 'product_id');
    // }

    // /**
    //  * 获取商品操作日志
    //  */
    // public function operationLogs()
    // {
    //     return $this->hasMany(ProductOperationLog::class, 'product_id');
    // }

    /**
     * 创建人
     */
    public function creator()
    {
        return $this->belongsTo(Member::class, 'created_by', 'id');
    }

    /**
     * 更新人
     */
    public function updater()
    {
        return $this->belongsTo(Member::class, 'updated_by', 'id');
    }
} 