<?php
namespace app\controller\system;

use app\model\Permission;
use support\Request;
use app\controller\Controller;

class PermissionController extends Controller
{
    /**
     * 获取权限列表
     */
    public function getList(Request $request)
    {
        $permissions = Permission::orderBy('sort')->get();
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => $permissions
        ]);
    }

    /**
     * 获取权限树
     */
    public function getTree(Request $request)
    {
        $tree = Permission::getTree();
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => $tree
        ]);
    }

    /**
     * 创建权限
     */
    public function postCreate(Request $request)
    {
        $data = $request->post();
        $permission = Permission::create([
            'name' => $data['name'],
            'path' => $data['path'] ?? null,
            'method' => $data['method'] ?? null,
            'menu_type' => $data['menu_type'],
            'parent_id' => $data['parent_id'] ?? 0,
            'component' => $data['component'] ?? null,
            'permission_code' => $data['permission_code'],
            'icon' => $data['icon'] ?? null,
            'sort' => $data['sort'] ?? 0,
            'status' => $data['status'] ?? 1
        ]);

        return json([
            'code' => 200,
            'message' => '创建成功',
            'data' => $permission
        ]);
    }

    /**
     * 更新权限
     */
    public function putUpdate(Request $request, $id)
    {
        $data = $request->post();
        $permission = Permission::findOrFail($id);
        
        $permission->update([
            'name' => $data['name'],
            'path' => $data['path'] ?? $permission->path,
            'method' => $data['method'] ?? $permission->method,
            'menu_type' => $data['menu_type'],
            'parent_id' => $data['parent_id'] ?? $permission->parent_id,
            'component' => $data['component'] ?? $permission->component,
            'permission_code' => $data['permission_code'],
            'icon' => $data['icon'] ?? $permission->icon,
            'sort' => $data['sort'] ?? $permission->sort,
            'status' => $data['status'] ?? $permission->status
        ]);

        return json([
            'code' => 200,
            'message' => '更新成功',
            'data' => $permission
        ]);
    }

    /**
     * 删除权限
     */
    public function deletePermission(Request $request, $id)
    {
        $permission = Permission::findOrFail($id);
        
        // 检查是否有子权限
        if (Permission::where('parent_id', $id)->exists()) {
            return json([
                'code' => 400,
                'message' => '该权限下有子权限，无法删除'
            ]);
        }
        
        // 删除角色权限关联
        $permission->roles()->detach();
        // 删除权限
        $permission->delete();

        return json([
            'code' => 200,
            'message' => '删除成功'
        ]);
    }

   /**
     * 获取用户权限树
     */
    public function getUserPermissionTree(Request $request)
    {
        $userId = $this->getUserId($request);
        
        // 如果是超级管理员，返回所有权限
        $user = \app\model\Member::find($userId);
        if ($user->is_super == 1) {
            $permissions = Permission::orderBy('sort')->get();
        } else {
            // 获取用户所有权限
            $permissions = Permission::whereHas('roles.members', function($query) use ($userId) {
                $query->where('id', $userId);
            })
            ->orderBy('sort')
            ->get();
        }

        // 构建权限树
        $tree = [];
        $permissionMap = [];
        
        // 创建权限映射
        foreach ($permissions as $permission) {
            $permissionMap[$permission->id] = [
                'id' => $permission->id,
                'name' => $permission->name,
                'path' => $permission->path,
                'component' => $permission->component,
                'icon' => $permission->icon,
                'permission_code' => $permission->permission_code,
                'menu_type' => $permission->menu_type,
                'parent_id' => $permission->parent_id,
                'children' => []
            ];
        }

        // 构建树结构
        foreach ($permissionMap as &$permission) {
            if ($permission['parent_id'] == 0) {
                // 如果是顶级菜单，直接添加到树中
                $tree[$permission['id']] = &$permission; // 使用引用
            } else {
                // 如果是子级菜单，找到父级并添加到父级的 children 数组中
                $parentId = $permission['parent_id'];
                if (isset($permissionMap[$parentId])) {
                    // 初始化父级的 children 数组（如果尚未初始化）
                    if (!isset($permissionMap[$parentId]['children'])) {
                        $permissionMap[$parentId]['children'] = [];
                    }
                    // 将子级菜单添加到父级的 children 数组中
                    $permissionMap[$parentId]['children'][] = &$permission; // 使用引用
                }
            }
        }

        // 将树结构转换为索引数组（如果需要）
        $tree = array_values($tree);
        
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => $tree
        ]);
    }
}
