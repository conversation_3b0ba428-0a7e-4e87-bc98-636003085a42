<?php

namespace app\model;

use support\Model;

class LoginLog extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'login_log';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'member_id',
        'username',
        'ip',
        'location',
        'browser',
        'os',
        'status',
        'message'
    ];

    /**
     * 关联用户
     */
    public function member()
    {
        return $this->belongsTo(Member::class, 'member_id');
    }

    /**
     * 记录登录日志
     * @param array $data
     * @return bool
     */
    public static function record($data)
    {
        return self::create($data);
    }

    /**
     * 获取用户的登录日志
     * @param int $memberId
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getMemberLogs($memberId, $limit = 10)
    {
        return self::with('member')
            ->where('member_id', $memberId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
} 