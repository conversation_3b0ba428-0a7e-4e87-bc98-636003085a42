<?php

namespace app\model;

use support\Model;

class ScheduleLog extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'schedule_log';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'schedule_id',
        'name',
        'type',
        'target',
        'status',
        'result',
        'executed_at'
    ];

    /**
     * 关联定时任务
     */
    public function schedule()
    {
        return $this->belongsTo(Schedule::class, 'schedule_id');
    }
} 