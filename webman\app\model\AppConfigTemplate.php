<?php
namespace app\model;

use support\Model;

class AppConfigTemplate extends Model
{
    protected $table = 'app_config_templates';
    
    protected $fillable = [
        'app_id',
        'name',
        'description',
        'settings',
        'created_at',
        'updated_at'
    ];

    /**
     * 关联应用
     */
    public function app()
    {
        return $this->belongsTo(App::class, 'app_id');
    }

    /**
     * 获取设置
     */
    public function getSettingsAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 设置设置
     */
    public function setSettingsAttribute($value)
    {
        $this->attributes['settings'] = json_encode($value);
    }
} 