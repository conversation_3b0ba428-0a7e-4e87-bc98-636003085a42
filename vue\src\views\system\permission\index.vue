<template>
  <system-page title="权限管理">
    <div class="permission-container">
      <a-tabs type="card" v-model="activeTab">
        <a-tab-pane key="role" tab="角色管理">
          <role-list />
        </a-tab-pane>
        <a-tab-pane key="permission" tab="权限管理">
          <permission-list />
        </a-tab-pane>
      </a-tabs>
    </div>
  </system-page>
</template>

<script>
import { ref } from 'vue';
import SystemPage from '../components/SystemPage.vue';
import RoleList from './components/RoleList.vue';
import PermissionList from './components/PermissionList.vue';

export default {
  name: 'SystemPermission',
  components: {
    SystemPage,
    RoleList,
    PermissionList
  },
  setup() {
    const activeTab = ref('role');

    return {
      activeTab
    };
  }
};
</script>

<style scoped>
.permission-container {
  background: #fff;
  border-radius: 8px;
}
</style> 