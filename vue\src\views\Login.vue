<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <img src="@/assets/logo.png" alt="Logo" class="login-logo" />
        <h2 class="login-title">登陆</h2>
        <p class="login-subtitle">欢迎使用速通关智能通关系统</p>
      </div>

      <a-form
        ref="formRef"
        :model="formState"
        @finish="handleSubmit"
        @finishFailed="onFinishFailed"
        class="login-form"
      >
        <a-form-item
          name="username"
          :rules="[{ required: true, message: '请输入用户名' }]"
        >
          <a-input
            v-model:value="formState.username"
            placeholder="用户名"
            autocomplete="username"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item
          name="password"
          :rules="[{ required: true, message: '请输入密码' }]"
        >
          <a-input-password
            v-model:value="formState.password"
            placeholder="密码"
            autocomplete="current-password"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>

        <a-form-item>
          <a-button type="primary" size="large" html-type="submit" :loading="loading" block>
            登录
          </a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script>
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";
import { UserOutlined, LockOutlined } from "@ant-design/icons-vue";
import { message } from "ant-design-vue";
import { login } from "@/api/passport";
import { useUserStore } from "@/stores/user";

export default {
  components: {
    UserOutlined,
    LockOutlined,
  },

  setup() {
    const router = useRouter();
    const userStore = useUserStore();
    const formRef = ref(null);
    const loading = ref(false);
    const formState = reactive({
      username: "",
      password: "",
    });

    const handleSubmit = async () => {
      try {
        loading.value = true;
        await formRef.value.validateFields(); // 手动触发验证
        const response = await login(formState);

        
        if (response.code === 200) {
          // 保存token和用户信息
          const { token, member } = response.data;
    
          userStore.setToken(token.access_token);  // 使用store的方法保存token
          userStore.setRefreshToken(token.refresh_token);  // 使用store的方法保存refreshToken
          userStore.setUserInfo(member);
          
          // 获取用户权限数据
          await userStore.getPermissions();
          
          message.success("登录成功");
          // 延迟跳转，让用户看到成功提示
          setTimeout(() => {
            router.push("/");
          }, 500);
        } else {
          message.error(response.message || "登录失败");
        }
      } catch (error) {
        if (error.response) {
          message.error(error.response.data?.message || "登录失败，请稍后重试");
        }
      } finally {
        loading.value = false;
      }
    };

    const onFinishFailed = (errorInfo) => {
      message.error("请填写完整的登录信息");
    };

    return {
      formRef,
      formState,
      loading,
      handleSubmit,
      onFinishFailed,
    };
  },
};
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #272343 0%, #494866 100%);
}

.login-box {
  background: white;
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  width: 90%;
  max-width: 420px;
}

.login-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-logo {
  width: 64px;
  height: 64px;
  margin-bottom: 1rem;
}

.login-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.login-subtitle {
  color: #6b7280;
  font-size: 0.875rem;
}

.login-form {
  margin-top: 1.5rem;
}
</style>
