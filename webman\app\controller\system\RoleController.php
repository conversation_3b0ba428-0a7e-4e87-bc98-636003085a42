<?php
namespace app\controller\system;

use app\model\Role;
use support\Request;

class RoleController
{
    /**
     * 获取角色列表
     */
    public function getList(Request $request)
    {
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);
        $keyword = $request->input('keyword', '');

        $query = Role::query();
        if ($keyword) {
            $query->where('name', 'like', "%{$keyword}%")
                  ->orWhere('code', 'like', "%{$keyword}%");
        }

        $total = $query->count();
        $roles = $query->offset(($page - 1) * $pageSize)
                      ->limit($pageSize)
                      ->get();

        return json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'list' => $roles,
                'total' => $total
            ]
        ]);
    }

    /**
     * 创建角色
     */
    public function postCreate(Request $request)
    {
        $data = $request->post();
        $role = Role::create([
            'name' => $data['name'],
            'code' => $data['code'],
            'status' => $data['status'] ?? 1,
            'remark' => $data['remark'] ?? ''
        ]);

        if (isset($data['permissions'])) {
            $role->permissions()->sync($data['permissions']);
        }

        return json([
            'code' => 200,
            'message' => '创建成功',
            'data' => $role
        ]);
    }

    /**
     * 更新角色
     */
    public function putUpdate(Request $request, $id)
    {
        $data = $request->post();
        $role = Role::findOrFail($id);
        
        $role->update([
            'name' => $data['name'],
            'code' => $data['code'],
            'status' => $data['status'] ?? $role->status,
            'remark' => $data['remark'] ?? $role->remark
        ]);

        if (isset($data['permissions'])) {
            $role->permissions()->sync($data['permissions']);
        }

        return json([
            'code' => 200,
            'message' => '更新成功',
            'data' => $role
        ]);
    }

    /**
     * 删除角色
     */
    public function deleteRole(Request $request, $id)
    {
        $role = Role::findOrFail($id);
        
        // 删除角色权限关联
        $role->permissions()->detach();
        // 删除用户角色关联
        $role->members()->detach();
        // 删除角色
        $role->delete();

        return json([
            'code' => 200,
            'message' => '删除成功'
        ]);
    }

    /**
     * 获取角色权限
     */
    public function getPermissions(Request $request, $id)
    {
        $role = Role::with('permissions')->findOrFail($id);
        
        return json([
            'code' => 200,
            'message' => 'success',
            'data' => $role->permissions
        ]);
    }

    /**
     * 分配角色权限
     */
    public function assignPermissions(Request $request, $id)
    {
        $role = Role::findOrFail($id);
        $permissions = $request->post('permissions', []);
        
        $role->permissions()->sync($permissions);

        return json([
            'code' => 200,
            'message' => '权限分配成功'
        ]);
    }
} 