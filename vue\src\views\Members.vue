<template>
  <div class="members-container">
    <a-alert style="margin-bottom: 16px;"
      message="操作提示"
      type="info"
      show-icon
    >
      <template #description>
          <div>用户角色及权限管理，请前往 系统->权限管理 中配置：
            <a target="_blank" href="/system/permission">点击前往</a>
          </div>
      </template>
    </a-alert>
    <!-- 操作按钮 -->
    <div class="table-operations">
      <a-button type="primary" v-permission="'create'" @click="showAddModal">
        <template #icon><PlusOutlined /></template>
        新增成员
      </a-button>
    </div>

    <!-- 成员列表 -->
    <a-table
      :columns="columns"
      :data-source="memberList"
      :loading="loading"
      :pagination="{ 
        total: total,
        current: current,
        pageSize: pageSize,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: total => `共 ${total} 条`
      }"
      :scroll="{ x: 1200 }"
      @change="handleTableChange"
    >
      <!-- 头像列 -->
      <template #bodyCell="{ column, record }">

        <!-- 状态列 -->
        <template v-if="column.key === 'status'">
          <a-tag :color="record.status ? 'success' : 'error'">
            {{ record.status ? '启用' : '禁用' }}
          </a-tag>
        </template>
        
        <!-- 操作列 -->
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" @click="showEditModal(record)" v-permission="'update'">
              <template #icon><EditOutlined /></template>
              编辑
            </a-button>
            <a-dropdown>
              <a-button type="link">
                <template #icon><MoreOutlined /></template>
                更多
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <a-button
                      key="allocation" 
                      @click="showRoleModal(record)" 
                      v-permission="'allocation'"
                      type="link"
                    >
                      <TeamOutlined />
                      分配角色
                    </a-button>
                  </a-menu-item>
                  <a-menu-item>
                    <a-button 
                      key="editPassword" 
                      @click="showPasswordModal(record)" 
                      v-permission="'eidtPassword'"
                      type="link"
                    >
                      <KeyOutlined />
                      修改密码
                    </a-button>
                  </a-menu-item>
                  <a-menu-item>
                    <a-button v-permission="'delete'" type="link" danger @click="handleDelete(record.id)">
                        <DeleteOutlined />
                        删除
                    </a-button>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 成员表单模态框 -->
    <a-modal
      :title="modalTitle"
      :open="modalVisible"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :confirmLoading="modalLoading"
      okText="确定"
      cancelText="取消"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="用户名" name="username">
          <a-input 
            v-model:value="formState.username" 
            placeholder="请输入用户名"
            :disabled="!!editingId"
          />
        </a-form-item>
        <a-form-item 
          label="密码" 
          name="password"
          v-if="!editingId"
          :required="!editingId"
        >
          <a-input-password
            v-model:value="formState.password"
            placeholder="请输入密码"
            :disabled="!!editingId"
          />
        </a-form-item>
        <a-form-item label="姓名" name="name">
          <a-input v-model:value="formState.name" placeholder="请输入姓名" />
        </a-form-item>
        <a-form-item label="昵称" name="nickname">
          <a-input v-model:value="formState.nickname" placeholder="请输入昵称" />
        </a-form-item>
        <a-form-item label="手机号" name="mobile">
          <a-input v-model:value="formState.mobile" placeholder="请输入手机号" />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-switch v-model:checked="formState.status" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea 
            v-model:value="formState.remark" 
            placeholder="请输入备注信息"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 修改密码模态框 -->
    <a-modal
      :title="`修改密码 - ${selectedMember?.name || ''}`"
      :open="passwordModalVisible"
      @ok="handlePasswordModalOk"
      @cancel="handlePasswordModalCancel"
      :confirmLoading="passwordModalLoading"
      okText="确定"
      cancelText="取消"
    >
      <a-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="新密码" name="password">
          <a-input-password v-model:value="passwordForm.password" placeholder="请输入新密码" />
        </a-form-item>
        <a-form-item label="确认密码" name="confirmPassword">
          <a-input-password v-model:value="passwordForm.confirmPassword" placeholder="请再次输入新密码" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 分配角色模态框 -->
    <a-modal
      :title="`分配角色 - ${selectedMember?.name || ''}`"
      :open="roleModalVisible"
      @ok="handleRoleModalOk"
      @cancel="handleRoleModalCancel"
      :confirmLoading="roleModalLoading"
      okText="确定"
      cancelText="取消"
      width="400px"
    >
      <a-transfer
        :data-source="roleList"
        :titles="['未分配', '已分配']"
        :target-keys="selectedRoleIds"
        :render="item => item.title"
        @change="handleRoleChange"
      />
    </a-modal>

  </div>
</template>

<script>
import { ref, reactive, getCurrentInstance } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  TeamOutlined,
  KeyOutlined,
  MoreOutlined
} from '@ant-design/icons-vue';
import {
  getMemberList,
  createMember,
  updateMember,
  deleteMember,
  updatePassword,
  getMemberRoles,
  assignMemberRoles
} from '@/api/member';
import { getRoleList } from '@/api/system/role';

export default {
  name: 'Members',
  components: {
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    TeamOutlined,
    KeyOutlined,
    MoreOutlined
  },
  setup() {
    const { proxy } = getCurrentInstance();

    // 表格列定义
    const columns = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id'
      },
      {
        title: '用户名',
        dataIndex: 'username',
        key: 'username'
      },
      {
        title: '姓名',
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: '昵称',
        dataIndex: 'nickname',
        key: 'nickname'
      },
      {
        title: '手机号',
        dataIndex: 'mobile',
        key: 'mobile'
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100
      },
      {
        title: '操作',
        key: 'action',
        width: 200,
        fixed: 'right'
      }
    ];

    const memberList = ref([]);
    const loading = ref(false);
    const modalVisible = ref(false);
    const modalLoading = ref(false);
    const modalTitle = ref('添加成员');
    const formRef = ref(null);
    const editingId = ref(null);
    const selectedMember = ref(null);

    const formState = reactive({
      username: '',
      password: '',
      name: '',
      nickname: '',
      mobile: '',
      status: true,
      remark: ''
    });

    const rules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, message: '用户名长度不能小于3个字符', trigger: 'blur' }
      ],
      password: [
        { required: !editingId.value, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能小于6个字符', trigger: 'blur' }
      ],
      name: [
        { required: true, message: '请输入姓名', trigger: 'blur' }
      ],
      nickname: [
        { required: true, message: '请输入昵称', trigger: 'blur' }
      ],
      mobile: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ]
    };

    // 分页数据
    const total = ref(0);
    const current = ref(1);
    const pageSize = ref(10);

    // 获取成员列表
    const fetchMembers = async (params = {}) => {
      loading.value = true;
      try {
        const response = await getMemberList({
          page: params.page || current.value,
          pageSize: params.pageSize || pageSize.value,
          ...params
        });
        memberList.value = response.data.data;
        total.value = response.data.total;
      } catch (error) {
        console.error('获取成员列表失败:', error);
        message.error('获取成员列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 显示添加模态框
    const showAddModal = () => {
      modalTitle.value = '添加成员';
      editingId.value = null;
      Object.assign(formState, {
        username: '',
        password: '',
        name: '',
        nickname: '',
        email: '',
        mobile: '',
        status: true,
        remark: ''
      });
      modalVisible.value = true;
    };

    // 显示编辑模态框
    const showEditModal = (record) => {
      modalTitle.value = '编辑成员';
      editingId.value = record.id;
      Object.assign(formState, {
        ...record,
        password: '' // 编辑时不显示密码
      });
      modalVisible.value = true;
    };

    // 处理模态框确认
    const handleModalOk = async () => {
      try {
        await formRef.value.validate();
        modalLoading.value = true;

        if (editingId.value) {
          await updateMember(formState);
          message.success('成员信息已更新');
        } else {
          await createMember(formState);
          message.success('成员已添加');
        }
        
        modalVisible.value = false;
        fetchMembers();
      } catch (error) {
        console.error('操作失败:', error);
        message.error('操作失败');
      } finally {
        modalLoading.value = false;
      }
    };

    // 处理模态框取消
    const handleModalCancel = () => {
      modalVisible.value = false;
    };

    // 处理删除成员
    const handleDelete = async (id) => {
      Modal.confirm({
        title: '删除成员',
        content: '确定要删除该成员吗？',
        onOk: async () => {
          await deleteMember({ id });
          message.success('成员已删除');
          fetchMembers();
        }
      });
    };

    // 密码表单相关
    const passwordFormRef = ref(null);
    const passwordModalVisible = ref(false);
    const passwordModalLoading = ref(false);
    const passwordForm = reactive({
      password: '',
      confirmPassword: ''
    });

    const passwordRules = {
      password: [
        { required: true, message: '请输入新密码' },
        { min: 6, message: '密码长度不能小于6位' }
      ],
      confirmPassword: [
        { required: true, message: '请再次输入新密码' },
        {
          validator: async (rule, value) => {
            if (value !== passwordForm.password) {
              throw new Error('两次输入的密码不一致');
            }
          }
        }
      ]
    };

    // 显示修改密码模态框
    const showPasswordModal = (record) => {
      selectedMember.value = record;
      passwordForm.password = '';
      passwordForm.confirmPassword = '';
      passwordModalVisible.value = true;
    };

    // 处理修改密码
    const handlePasswordModalOk = async () => {
      try {
        await passwordFormRef.value.validate();
        passwordModalLoading.value = true;

        await updatePassword({
          id: selectedMember.value.id,
          password: passwordForm.password
        });
        
        message.success('密码修改成功');
        passwordModalVisible.value = false;
      } catch (error) {
        console.error('密码修改失败:', error);
        message.error('密码修改失败');
      } finally {
        passwordModalLoading.value = false;
      }
    };

    // 角色相关
    const roleModalVisible = ref(false);
    const roleModalLoading = ref(false);
    const roleList = ref([]);
    const selectedRoleIds = ref([]);

    // 获取角色列表
    const fetchRoleList = async () => {
      try {
        const response = await getRoleList();
        if (response.code === 200) {
          roleList.value = response.data.list.map(role => ({
            key: role.id.toString(),
            title: role.name,
            description: role.remark
          }));
        } else {
          message.error(response.message || '获取角色列表失败');
        }
      } catch (error) {
        console.error('获取角色列表失败:', error);
        message.error('获取角色列表失败');
      }
    };

    // 获取成员角色
    const fetchMemberRoles = async (memberId) => {
      try {
        const response = await getMemberRoles(memberId);
        if (response.code === 200) {
          selectedRoleIds.value = response.data.map(role => role.id.toString());
        } else {
          message.error(response.msg || '获取成员角色失败');
        }
      } catch (error) {
        console.error('获取成员角色失败:', error);
        message.error('获取成员角色失败');
      }
    };

    // 显示分配角色模态框
    const showRoleModal = async (record) => {
      selectedMember.value = record;
      await fetchRoleList();
      await fetchMemberRoles(record.id);
      roleModalVisible.value = true;
    };

    // 处理角色选择变化
    const handleRoleChange = (targetKeys) => {
      selectedRoleIds.value = targetKeys;
    };

    // 处理分配角色
    const handleRoleModalOk = async () => {
      if (!selectedMember.value) return;
      
      roleModalLoading.value = true;
      try {
        const response = await assignMemberRoles(selectedMember.value.id, {
          roleIds: selectedRoleIds.value.map(id => parseInt(id))
        });
        
        if (response.code === 200) {
          message.success('角色分配成功');
          roleModalVisible.value = false;
          fetchMembers();
        } else {
          message.error(response.msg || '角色分配失败');
        }
      } catch (error) {
        console.error('分配角色失败:', error);
        message.error('分配角色失败');
      } finally {
        roleModalLoading.value = false;
      }
    };

    // 处理角色模态框取消
    const handleRoleModalCancel = () => {
      selectedMember.value = null;
      selectedRoleIds.value = [];
      roleModalVisible.value = false;
    };

    // 处理表格变化
    const handleTableChange = (pagination, filters, sorter) => {
      current.value = pagination.current;
      pageSize.value = pagination.pageSize;
      fetchMembers();
    };

    // 初始化加载数据
    fetchMembers();

    return {
      columns,
      memberList,
      loading,
      modalVisible,
      modalLoading,
      modalTitle,
      formRef,
      formState,
      rules,
      showAddModal,
      showEditModal,
      handleModalOk,
      handleModalCancel,
      handleDelete,
      passwordFormRef,
      passwordForm,
      passwordRules,
      passwordModalVisible,
      passwordModalLoading,
      selectedMember,
      showPasswordModal,
      handlePasswordModalOk,
      handlePasswordModalCancel: () => passwordModalVisible.value = false,
      total,
      current,
      pageSize,
      handleTableChange,
      editingId,
      roleModalVisible,
      roleModalLoading,
      roleList,
      selectedRoleIds,
      showRoleModal,
      handleRoleChange,
      handleRoleModalOk,
      handleRoleModalCancel,
    };
  }
};
</script>

<style scoped>
.members-container {
  padding: 24px;
}

.table-operations {
  margin-bottom: 16px;
}

:deep(.ant-transfer) {
  justify-content: center;
}
</style>
