<?php

namespace app\model;

use support\Model;

class EmailLog extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'gaodux_email_logs';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'schedule_id',
        'email_config_id',
        'recipients',
        'subject',
        'content',
        'status',
        'error_message',
        'sent_at'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'recipients' => 'array',
        'status' => 'boolean',
        'sent_at' => 'datetime'
    ];

    /**
     * 关联定时任务
     */
    public function schedule()
    {
        return $this->belongsTo(Schedule::class, 'schedule_id');
    }

    /**
     * 关联邮件配置
     */
    public function emailConfig()
    {
        return $this->belongsTo(EmailConfig::class, 'email_config_id');
    }

    /**
     * 记录邮件发送成功
     */
    public static function logSuccess($scheduleId, $emailConfigId, $recipients, $subject, $content)
    {
        return static::create([
            'schedule_id' => $scheduleId,
            'email_config_id' => $emailConfigId,
            'recipients' => is_array($recipients) ? $recipients : [$recipients],
            'subject' => $subject,
            'content' => $content,
            'status' => 1,
            'sent_at' => now()
        ]);
    }

    /**
     * 记录邮件发送失败
     */
    public static function logFailure($scheduleId, $emailConfigId, $recipients, $subject, $content, $errorMessage)
    {
        return static::create([
            'schedule_id' => $scheduleId,
            'email_config_id' => $emailConfigId,
            'recipients' => is_array($recipients) ? $recipients : [$recipients],
            'subject' => $subject,
            'content' => $content,
            'status' => 0,
            'error_message' => $errorMessage,
            'sent_at' => now()
        ]);
    }
}
