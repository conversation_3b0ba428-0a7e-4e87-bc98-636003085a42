import axios from 'axios';
import { message } from 'ant-design-vue';
import { useUserStore } from '@/stores/user';
import md5 from 'crypto-js/md5';

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '', // 从环境变量获取API地址
  timeout: 15000, // 请求超时时间
});

// 是否正在刷新token
let isRefreshing = false;
// 重试队列
let retryQueue = [];

// 生成API签名
const generateApiSign = (params, timestamp, appSecret) => {
  // 对参数按键名排序
  const sortedParams = {};
  Object.keys(params).sort().forEach(key => {
    sortedParams[key] = params[key];
  });

  // 构建签名字符串
  let signStr = '';
  Object.entries(sortedParams).forEach(([key, value]) => {
    if (typeof value === 'object') {
      value = JSON.stringify(value);
    }
    signStr += `${key}=${value}&`;
  });
  signStr += `timestamp=${timestamp}&`;
  signStr += `secret=${appSecret}`;

  // 返回MD5大写
  return md5(signStr).toString().toUpperCase();
};

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    const userStore = useUserStore();
    const token = userStore.token;

    // 如果是开放API请求（URL以/open-api开头）
    if (config.url.startsWith('/open-api')) {
      const timestamp = Math.floor(Date.now() / 1000);
      const appId = ''; // 测试用的AppID
      const appSecret = ''; // 测试用的AppSecret

      // 添加API认证头
      config.headers['x-api-key'] = appId;
      config.headers['x-timestamp'] = timestamp;
      
      // 生成签名
      const params = config.method === 'get' ? config.params || {} : config.data || {};
      const sign = generateApiSign(params, timestamp, appSecret);
      config.headers['x-sign'] = sign;
    } 
    // 其他请求添加JWT认证
    else if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 处理token刷新
const handleTokenRefresh = async (error) => {
  const { config, response } = error;
  const userStore = useUserStore();

  if (response?.data.code === 401) {
    const refreshToken = userStore.getRefreshToken();

    if (!refreshToken) {
      message.error('登录已过期，请重新登录');
      userStore.logout();
      window.location.href = '/login';
      return Promise.reject(error);
    }

    if (!isRefreshing) {
      isRefreshing = true;

      try {
        // 尝试刷新token
        const res = await request.post('/passport/refresh-token', {
          refresh_token: refreshToken,
        });

        if (res.code === 200) {
          // 更新token
          userStore.setToken(res.data.access_token);
          userStore.setRefreshToken(res.data.refresh_token);

          // 更新重试队列中每个请求的 Authorization 头
          retryQueue.forEach((cb) => {
            cb.config.headers['Authorization'] = 'Bearer ' + res.data.access_token;
          });

          // 重试队列中的请求
          retryQueue.forEach((cb) => cb.resolve(request(cb.config)));
          retryQueue = [];

          // 重试当前请求
          config.headers['Authorization'] = 'Bearer ' + res.data.access_token;
          // return request(config);
        } else {
          message.error('登录已过期，请重新登录');
          userStore.logout();
          window.location.href = '/login';
        }
      } catch (refreshError) {
        message.error('登录已过期，请重新登录');
        userStore.logout();
        window.location.href = '/login';
      } finally {
        isRefreshing = false;
      }
    } else {
      // 将请求加入重试队列
      return new Promise((resolve, reject) => {
        retryQueue.push({
          resolve,
          reject,
          config,
        });
      });
    }
  }
};

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    const res = response.data;

    // 跳过对 refresh-token 的处理
    if (response.config.url === '/passport/refresh-token') {
      return res;
    }

    // 如果返回状态码为401，说明登录已过期，尝试刷新token
    if (res.code === 401) {
      // return handleTokenRefresh({ response, config: response.config });
    }

    // 如果返回的状态码不是200，说明出错了
    if (res.code !== 200) {
      // message.error(res.message || '请求失败');
      return Promise.reject(res);
    }

    return res;
  },
  (error) => {
    if (error.response) {
      switch (error.response.status) {
        case 403:
          message.error('没有权限访问');
          break;
        case 404:
          message.error('请求的资源不存在');
          break;
        case 500:
          message.error('服务器错误，请稍后重试');
          break;
        default:
          message.error(error.response.data?.message || '请求失败，请稍后重试');
      }
    } else {
      message.error('网络错误，请检查网络连接');
    }

    return Promise.reject(error);
  }
);

export default request;