<template>
  <div class="app-logs-container">
    <a-card :bordered="false">
      <!-- 搜索表单 -->
      <a-form layout="inline" class="table-search-form">
        <a-row :gutter="[8, 8]">
          <a-col :span="6">
            <a-form-item label="应用">
              <a-select
                v-model:value="searchForm.app_id"
                placeholder="请选择应用"
                allowClear
              >
                <a-select-option
                  v-for="app in appList"
                  :key="app.id"
                  :value="app.id"
                >
                  {{ app.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="级别">
              <a-select
                v-model:value="searchForm.level"
                placeholder="请选择日志级别"
                allowClear
              >
                <a-select-option value="debug">调试</a-select-option>
                <a-select-option value="info">信息</a-select-option>
                <a-select-option value="warning">警告</a-select-option>
                <a-select-option value="error">错误</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="时间范围">
              <a-range-picker
                v-model:value="searchForm.dateRange"
                :show-time="true"
                format="YYYY-MM-DD HH:mm:ss"
              />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-space>
              <a-button type="primary" @click="handleSearch">
                <template #icon><search-outlined /></template>
                查询
              </a-button>
              <a-button @click="handleResetSearch">
                <template #icon><reload-outlined /></template>
                重置
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>

      <!-- 日志列表 -->
      <a-table
        :columns="columns"
        :dataSource="logList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        :rowKey="getRowKey"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'level'">
            <a-tag :color="getLevelColor(record.level)">
              {{ record.level }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'context'">
            <a-button type="link" @click="showContext(record)">
              查看详情
            </a-button>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 日志详情弹窗 -->
    <a-modal
      v-model:open="contextModalVisible"
      title="日志详情"
      :footer="null"
      width="900px"
    >
      <a-descriptions 
        :column="1" 
        bordered 
        :labelStyle="{width: '120px', fontWeight: 'bold', background: '#fafafa'}"
        :contentStyle="{padding: '8px 12px', wordBreak: 'break-all'}"
        style="margin-bottom: 0;"
      >
        <a-descriptions-item label="应用名称">
          <span style="font-size: 15px;">{{ currentLog?.app?.name }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="日志级别">
          <a-tag :color="getLevelColor(currentLog?.level)" style="font-size: 14px;">
            {{ currentLog?.level }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="日志内容">
          <span style="font-size: 15px;">{{ currentLog?.message }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="上下文信息">
          <pre class="log-context-pre">{{ formatContext(currentLog?.context) }}</pre>
        </a-descriptions-item>
        <a-descriptions-item label="记录时间">
          <span style="font-size: 14px;">{{ formatDate(currentLog?.created_at) }}</span>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue';
import {
  SearchOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { getAppLogs, getApps } from '@/api/system/app';
import { formatDate } from '@/utils/utils';

export default defineComponent({
  name: 'AppLogs',
  components: {
    SearchOutlined,
    ReloadOutlined
  },
  setup() {
    // 应用列表
    const appList = ref([]);
    const logList = ref([]);
    const loading = ref(false);
    
    // 搜索表单
    const searchForm = reactive({
      app_id: undefined,
      level: undefined,
      dateRange: []
    });

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showTotal: total => `共 ${total} 条`,
      showSizeChanger: true,
      showQuickJumper: true
    });

    // 表格列定义
    const columns = [
      {
        title: '应用名称',
        dataIndex: ['app', 'name'],
        width: 150
      },
      {
        title: '日志级别',
        dataIndex: 'level',
        key: 'level',
        width: 100
      },
      {
        title: '日志内容',
        dataIndex: 'message',
        ellipsis: true
      },
      {
        title: '上下文',
        key: 'context',
        width: 100
      },
      {
        title: '记录时间',
        dataIndex: 'created_at',
        width: 180,
        customRender: ({ text }) => formatDate(text)
      }
    ];

    // 日志详情弹窗
    const contextModalVisible = ref(false);
    const currentLog = ref(null);

    // 获取表格唯一key
    const getRowKey = record => record.id;

    // 获取日志列表
    const fetchLogs = async () => {
      loading.value = true;
      try {
        const params = {
          page: pagination.current,
          pageSize: pagination.pageSize,
          app_id: searchForm.app_id,
          level: searchForm.level,
          start_time: searchForm.dateRange[0]?.format('YYYY-MM-DD HH:mm:ss'),
          end_time: searchForm.dateRange[1]?.format('YYYY-MM-DD HH:mm:ss')
        };

        const res = await getAppLogs(params);
        if (res.code === 200) {
          logList.value = res.data.list;
          pagination.total = res.data.total;
        } else {
          message.error(res.message || '获取日志列表失败');
        }
      } catch (error) {
        console.error('获取日志列表失败:', error);
        message.error('获取日志列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 搜索
    const handleSearch = () => {
      pagination.current = 1;
      fetchLogs();
    };

    // 重置搜索
    const handleResetSearch = () => {
      searchForm.app_id = undefined;
      searchForm.level = undefined;
      searchForm.dateRange = [];
      handleSearch();
    };

    // 表格变化
    const handleTableChange = (pag) => {
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;
      fetchLogs();
    };

    // 获取日志级别颜色
    const getLevelColor = (level) => {
      const colors = {
        debug: 'blue',
        info: 'green',
        warning: 'orange',
        error: 'red'
      };
      return colors[level] || 'default';
    };

    // 显示日志详情
    const showContext = (record) => {
      currentLog.value = record;
      contextModalVisible.value = true;
    };

    // 格式化上下文信息
    const formatContext = (context) => {
      if (!context) return '';
      return JSON.stringify(context, null, 2);
    };

    onMounted(() => {
      fetchLogs();
      // 获取应用列表
      getApps().then(res => {
        if (res.code === 200) {
          appList.value = res.data.list || res.data || [];
        } else {
          message.error(res.message || '获取应用列表失败');
        }
      }).catch(() => {
        message.error('获取应用列表失败');
      });
    });

    return {
      appList,
      logList,
      loading,
      searchForm,
      pagination,
      columns,
      contextModalVisible,
      currentLog,
      handleSearch,
      handleResetSearch,
      handleTableChange,
      getLevelColor,
      showContext,
      formatContext,
      formatDate,
      getRowKey
    };
  }
});
</script>

<style lang="less" scoped>
.app-logs-container {
  padding: 24px;

  .table-search-form {
    margin-bottom: 24px;

    :deep(.ant-form-item) {
      margin-bottom: 16px;
    }
  }

  pre {
    margin: 0;
    padding: 8px;
    background: #f5f5f5;
    border-radius: 4px;
    max-height: 400px;
    overflow: auto;
  }
}

.log-context-pre {
  margin: 0;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  max-height: 400px;
  max-width: 800px;
  overflow: auto;
  word-break: break-all;
  white-space: pre-wrap;
  font-size: 14px;
}
</style> 