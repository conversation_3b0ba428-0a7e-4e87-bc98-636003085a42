<?php

namespace app\task;

use app\model\App;
use app\model\Order;
use app\model\Schedule;
use app\model\ScheduleLog;

/**
 * 邮件通知任务类
 * 提供各种邮件通知功能的定时任务方法
 */
class EmailNotificationTask
{
    /**
     * 发送系统状态报告邮件
     */
    public function taskSendSystemStatusReport()
    {
        try {
            // 获取邮件应用实例
            $emailService = $this->getEmailService();
            if (!$emailService) {
                throw new \Exception('邮件服务未配置或未启用');
            }
            
            // 收集系统状态信息
            $statusData = $this->collectSystemStatus();
            
            // 构建邮件内容
            $subject = '【报关系统】系统状态日报 - ' . date('Y-m-d');
            $body = $this->buildSystemStatusReport($statusData);
            
            // 发送邮件 - 这里可以配置默认收件人或从配置中读取
            $recipients = $this->getSystemReportRecipients();
            if (empty($recipients)) {
                throw new \Exception('未配置系统报告收件人');
            }
            
            $emailService->sendMail($recipients, $subject, $body);
            
            return '系统状态报告发送成功，收件人：' . implode(', ', $recipients);
            
        } catch (\Exception $e) {
            throw new \Exception('发送系统状态报告失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 发送订单统计报告邮件
     */
    public function taskSendOrderStatisticsReport()
    {
        try {
            $emailService = $this->getEmailService();
            if (!$emailService) {
                throw new \Exception('邮件服务未配置或未启用');
            }
            
            // 收集订单统计数据
            $orderStats = $this->collectOrderStatistics();
            
            // 构建邮件内容
            $subject = '【报关系统】订单统计报告 - ' . date('Y-m-d');
            $body = $this->buildOrderStatisticsReport($orderStats);
            
            $recipients = $this->getOrderReportRecipients();
            if (empty($recipients)) {
                throw new \Exception('未配置订单报告收件人');
            }
            
            $emailService->sendMail($recipients, $subject, $body);
            
            return '订单统计报告发送成功，收件人：' . implode(', ', $recipients);
            
        } catch (\Exception $e) {
            throw new \Exception('发送订单统计报告失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 发送定时任务执行异常报告
     */
    public function taskSendScheduleErrorReport()
    {
        try {
            $emailService = $this->getEmailService();
            if (!$emailService) {
                throw new \Exception('邮件服务未配置或未启用');
            }
            
            // 查询最近24小时内失败的定时任务
            $failedTasks = $this->getRecentFailedTasks();
            
            if (empty($failedTasks)) {
                return '无定时任务执行异常，无需发送报告';
            }
            
            // 构建邮件内容
            $subject = '【报关系统】定时任务异常报告 - ' . date('Y-m-d');
            $body = $this->buildScheduleErrorReport($failedTasks);
            
            $recipients = $this->getSystemReportRecipients();
            if (empty($recipients)) {
                throw new \Exception('未配置系统报告收件人');
            }
            
            $emailService->sendMail($recipients, $subject, $body);
            
            return '定时任务异常报告发送成功，异常任务数：' . count($failedTasks);
            
        } catch (\Exception $e) {
            throw new \Exception('发送定时任务异常报告失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 发送测试邮件
     */
    public function taskSendTestEmail()
    {
        try {
            $emailService = $this->getEmailService();
            if (!$emailService) {
                throw new \Exception('邮件服务未配置或未启用');
            }
            
            $recipients = $this->getSystemReportRecipients();
            if (empty($recipients)) {
                throw new \Exception('未配置测试邮件收件人');
            }
            
            $result = $emailService->sendTestMail($recipients);
            
            return '测试邮件发送成功，收件人：' . implode(', ', $recipients);
            
        } catch (\Exception $e) {
            throw new \Exception('发送测试邮件失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取邮件服务实例
     */
    protected function getEmailService()
    {
        $app = App::where('code', 'email')
                  ->where('status', 1)
                  ->first();
                  
        if (!$app) {
            return null;
        }
        
        return new \plugins\email\Service($app->id, $app->settings);
    }
    
    /**
     * 收集系统状态信息
     */
    protected function collectSystemStatus()
    {
        $data = [];
        
        // 今日订单统计
        $today = date('Y-m-d');
        $data['orders'] = [
            'today_total' => Order::whereDate('created_at', $today)->count(),
            'today_success' => Order::whereDate('created_at', $today)->where('status', 'completed')->count(),
            'today_failed' => Order::whereDate('created_at', $today)->where('status', 'failed')->count(),
        ];
        
        // 定时任务状态
        $data['schedules'] = [
            'total' => Schedule::count(),
            'enabled' => Schedule::where('status', 1)->count(),
            'recent_failed' => ScheduleLog::where('status', 'failed')
                                        ->where('executed_at', '>=', date('Y-m-d H:i:s', strtotime('-24 hours')))
                                        ->count()
        ];
        
        // 系统信息
        $data['system'] = [
            'php_version' => PHP_VERSION,
            'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . 'MB',
            'disk_free' => round(disk_free_space('.') / 1024 / 1024 / 1024, 2) . 'GB'
        ];
        
        return $data;
    }
    
    /**
     * 收集订单统计数据
     */
    protected function collectOrderStatistics()
    {
        $today = date('Y-m-d');
        $yesterday = date('Y-m-d', strtotime('-1 day'));
        $thisMonth = date('Y-m');
        
        return [
            'today' => [
                'total' => Order::whereDate('created_at', $today)->count(),
                'success' => Order::whereDate('created_at', $today)->where('status', 'completed')->count(),
                'failed' => Order::whereDate('created_at', $today)->where('status', 'failed')->count(),
                'amount' => Order::whereDate('created_at', $today)->sum('total_amount') ?: 0
            ],
            'yesterday' => [
                'total' => Order::whereDate('created_at', $yesterday)->count(),
                'success' => Order::whereDate('created_at', $yesterday)->where('status', 'completed')->count(),
                'failed' => Order::whereDate('created_at', $yesterday)->where('status', 'failed')->count(),
                'amount' => Order::whereDate('created_at', $yesterday)->sum('total_amount') ?: 0
            ],
            'this_month' => [
                'total' => Order::where('created_at', 'like', $thisMonth . '%')->count(),
                'success' => Order::where('created_at', 'like', $thisMonth . '%')->where('status', 'completed')->count(),
                'failed' => Order::where('created_at', 'like', $thisMonth . '%')->where('status', 'failed')->count(),
                'amount' => Order::where('created_at', 'like', $thisMonth . '%')->sum('total_amount') ?: 0
            ]
        ];
    }
    
    /**
     * 获取最近失败的定时任务
     */
    protected function getRecentFailedTasks()
    {
        return ScheduleLog::where('status', 'failed')
                         ->where('executed_at', '>=', date('Y-m-d H:i:s', strtotime('-24 hours')))
                         ->orderBy('executed_at', 'desc')
                         ->limit(20)
                         ->get()
                         ->toArray();
    }
    
    /**
     * 构建系统状态报告邮件内容
     */
    protected function buildSystemStatusReport($data)
    {
        $date = date('Y-m-d');
        
        return "
        <div style='font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;'>
            <div style='background: #f5f5f5; padding: 20px; border-radius: 8px;'>
                <h2 style='color: #333; margin-top: 0;'>系统状态日报 - {$date}</h2>
                
                <div style='background: white; padding: 20px; border-radius: 6px; margin: 20px 0;'>
                    <h3 style='color: #1890ff; margin-top: 0;'>📊 订单统计</h3>
                    <table style='width: 100%; border-collapse: collapse;'>
                        <tr>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0;'>今日订单总数：</td>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0; font-weight: bold;'>{$data['orders']['today_total']}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0;'>今日成功订单：</td>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0; color: #52c41a; font-weight: bold;'>{$data['orders']['today_success']}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0;'>今日失败订单：</td>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0; color: #ff4d4f; font-weight: bold;'>{$data['orders']['today_failed']}</td>
                        </tr>
                    </table>
                </div>
                
                <div style='background: white; padding: 20px; border-radius: 6px; margin: 20px 0;'>
                    <h3 style='color: #1890ff; margin-top: 0;'>⚙️ 定时任务状态</h3>
                    <table style='width: 100%; border-collapse: collapse;'>
                        <tr>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0;'>任务总数：</td>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0; font-weight: bold;'>{$data['schedules']['total']}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0;'>启用任务数：</td>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0; color: #52c41a; font-weight: bold;'>{$data['schedules']['enabled']}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0;'>24小时内失败次数：</td>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0; color: #ff4d4f; font-weight: bold;'>{$data['schedules']['recent_failed']}</td>
                        </tr>
                    </table>
                </div>
                
                <div style='background: white; padding: 20px; border-radius: 6px; margin: 20px 0;'>
                    <h3 style='color: #1890ff; margin-top: 0;'>💻 系统信息</h3>
                    <table style='width: 100%; border-collapse: collapse;'>
                        <tr>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0;'>PHP版本：</td>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0; font-weight: bold;'>{$data['system']['php_version']}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0;'>内存使用：</td>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0; font-weight: bold;'>{$data['system']['memory_usage']}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0;'>磁盘剩余：</td>
                            <td style='padding: 8px; border-bottom: 1px solid #f0f0f0; font-weight: bold;'>{$data['system']['disk_free']}</td>
                        </tr>
                    </table>
                </div>
                
                <div style='text-align: center; color: #666; font-size: 12px; margin-top: 20px;'>
                    <p>此邮件由报关系统自动发送，请勿回复</p>
                    <p>生成时间：" . date('Y-m-d H:i:s') . "</p>
                </div>
            </div>
        </div>";
    }
    
    /**
     * 构建订单统计报告邮件内容
     */
    protected function buildOrderStatisticsReport($data)
    {
        // 实现订单统计报告的HTML内容构建
        // 这里简化处理，实际可以根据需要详细实现
        return "订单统计报告内容...";
    }
    
    /**
     * 构建定时任务异常报告邮件内容
     */
    protected function buildScheduleErrorReport($failedTasks)
    {
        // 实现定时任务异常报告的HTML内容构建
        // 这里简化处理，实际可以根据需要详细实现
        return "定时任务异常报告内容...";
    }
    
    /**
     * 获取系统报告收件人列表
     * 这里可以从配置文件或数据库中读取
     */
    protected function getSystemReportRecipients()
    {
        // 这里可以从配置中读取，或者从数据库中获取
        // 暂时返回空数组，需要在实际使用时配置
        return [];
    }
    
    /**
     * 获取订单报告收件人列表
     */
    protected function getOrderReportRecipients()
    {
        // 这里可以从配置中读取，或者从数据库中获取
        return [];
    }
}
