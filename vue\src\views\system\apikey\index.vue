<template>
  <system-page title="API密钥管理">
    <div class="apikey-container">
      <a-card :bordered="false">
        <!-- 搜索表单 -->
        <a-form layout="inline" class="table-search-form">
          <a-row :gutter="[8, 8]">
            <a-col :span="12">
              <a-form-item label="名称">
                <a-input v-model:value="searchForm.name" placeholder="请输入密钥名称" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="状态">
                <a-select v-model:value="searchForm.status" placeholder="请选择状态" allowClear>
                  <a-select-option :value="1">启用</a-select-option>
                  <a-select-option :value="0">禁用</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="4">
              <a-space>
                <a-button type="primary" @click="handleSearch">
                  <template #icon><search-outlined /></template>
                  查询
                </a-button>
                <a-button @click="handleResetSearch">
                  <template #icon><reload-outlined /></template>
                  重置
                </a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-form>

        <!-- 操作按钮 -->
        <div class="table-operations">
          <a-button type="primary" @click="showAddModal">
            <template #icon><plus-outlined /></template>
            新增密钥
          </a-button>
        </div>

        <!-- API密钥列表 -->
        <a-table
          :columns="columns"
          :data-source="apiKeyList"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          :row-key="getRowKey"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'status'">
              <a-tag :color="record.status ? 'success' : 'error'">
                {{ record.status ? '启用' : '禁用' }}
              </a-tag>
            </template>
            <template v-else-if="column.key === 'last_used_at'">
              {{ formatDate(record.last_used_at) || '-' }}
            </template>
            <template v-else-if="column.key === 'created_at'">
              {{ formatDate(record.created_at) || '-' }}
            </template>
            <template v-else-if="column.key === 'action'">

              <a-space>
                <a-button type="link" @click="handleView(record)">
                  <template #icon><eye-outlined /></template>
                  查看
                </a-button>
                <a-button type="link" @click="handleEdit(record)">
                  <template #icon><edit-outlined /></template>
                  编辑
                </a-button>
                <a-popconfirm
                  title="确定要重置这个API密钥吗？"
                  description="重置后，密钥将无法使用，请谨慎操作。"
                  @confirm="handleResetApiKey(record)"
                >
                  <a-button type="link">
                    <template #icon><key-outlined /></template>
                    重置密钥
                  </a-button>
                </a-popconfirm>
                <a-popconfirm
                  title="确定要删除这个API密钥吗？"
                  @confirm="handleDelete(record)"
                >
                  <a-button type="link" danger>
                    <template #icon><delete-outlined /></template>
                    删除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>

        <!-- 新增/编辑弹窗 -->
        <a-modal
          :title="modalTitle"
          v-model:open="modalVisible"
          :maskClosable="false"
          @ok="handleModalOk"
          @cancel="handleModalCancel"
        >
          <a-form
            ref="formRef"
            :model="formState"
            :rules="rules"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item label="密钥名称" name="name">
              <a-input
                v-model:value="formState.name"
                placeholder="请输入密钥名称"
                :maxLength="50"
                showCount
                allowClear
              />
            </a-form-item>
            <a-form-item label="状态" name="status" v-if="currentRecord">
              <a-switch v-model:checked="formState.status" :checked-value="1" :unchecked-value="0" />
            </a-form-item>
            <a-form-item label="备注" name="remark">
              <a-textarea

                v-model:value="formState.remark"
                placeholder="请输入备注信息"
                :maxLength="200"
                :rows="4"
                showCount
                allowClear
              />
            </a-form-item>
          </a-form>
        </a-modal>

        <!-- 查看密钥信息弹窗 -->
        <a-modal
          title="密钥信息"
          v-model:open="viewModalVisible"
          :footer="null"
          @cancel="handleViewModalCancel"
          :destroyOnClose="true"
        >
          <template v-if="currentRecord">
            <a-descriptions :column="1">
              <a-descriptions-item label="密钥名称">{{ currentRecord.name }}</a-descriptions-item>
              <a-descriptions-item label="API Key">
                <a-input-password
                  v-model:value="currentRecord.apikey"
                  readonly
                  :bordered="false"
                >
                  <template #suffix>
                    <CopyOutlined
                      class="copy-icon"
                      @click="copyText(currentRecord.apikey)"
                    />
                  </template>
                </a-input-password>
              </a-descriptions-item>
              <a-descriptions-item label="状态">
                <a-tag :color="currentRecord.status ? 'success' : 'error'">
                  {{ currentRecord.status ? '启用' : '禁用' }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="备注">{{ currentRecord.remark || '-' }}</a-descriptions-item>
              <a-descriptions-item label="最后使用时间">{{ formatDate(currentRecord.last_used_at) || '-' }}</a-descriptions-item>
              <a-descriptions-item label="创建时间">{{ formatDate(currentRecord.created_at) }}</a-descriptions-item>
            </a-descriptions>
          </template>
        </a-modal>
      </a-card>
    </div>
  </system-page>
</template>

<script>
import { formatDate } from '@/utils/utils';
import { defineComponent, ref, reactive, onMounted } from 'vue';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  KeyOutlined,
  CopyOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import SystemPage from '../components/SystemPage.vue';
import { getApiKeys, createApiKey, updateApiKey, deleteApiKey, resetApiKey } from '@/api/system/apikey';

export default defineComponent({
  name: 'ApiKeyList',
  components: {
    SystemPage,
    PlusOutlined,
    SearchOutlined,
    ReloadOutlined,
    EditOutlined,
    DeleteOutlined,
    EyeOutlined,
    KeyOutlined,
    CopyOutlined
  },
  setup() {
    const loading = ref(false);
    const apiKeyList = ref([]);
    const modalVisible = ref(false);
    const viewModalVisible = ref(false);
    const modalTitle = ref('新增密钥');
    const formRef = ref(null);
    const currentRecord = ref(null);

    // 表格列定义
    const columns = [
      {
        title: '密钥名称',
        dataIndex: 'name',
        key: 'name',
        width: 200
      },
      {
        title: 'API Key',
        dataIndex: 'apikey',
        key: 'apikey',
        width: 200,
        customRender: ({ text }) => {
          if (!text) return '-';
          return '••••••••' + text.substr(-8); // 只显示最后8位,其他用点号代替
        }
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100
      },
      {
        title: '备注',
        dataIndex: 'remark',
        key: 'remark',
        width: 200
      },
      {
        title: '最后使用时间',
        dataIndex: 'last_used_at',
        key: 'last_used_at',
        width: 180
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 180
      },
      {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 500
      }
    ];

    const getRowKey = record => record.id;

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showTotal: total => `共 ${total} 条`,
      showSizeChanger: true,
      showQuickJumper: true
    });

    // 搜索表单
    const searchForm = reactive({
      name: '',
      status: undefined
    });

    // 表单状态
    const formState = reactive({
      name: '',
      status: 1,
      remark: ''
    });

    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入密钥名称' },
        { max: 50, message: '密钥名称不能超过50个字符' }
      ]
    };

    // 获取API密钥列表
    const fetchApiKeys = async () => {
      loading.value = true;
      try {
        const res = await getApiKeys({
          page: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm
        });
        if (res.code === 200) {
          // 直接使用返回的数据,因为后端没有分页
          apiKeyList.value = res.data;
          pagination.total = res.data.length;
        }
      } catch (error) {
        console.error('获取API密钥列表失败:', error);
        message.error('获取API密钥列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 搜索
    const handleSearch = () => {
      pagination.current = 1;
      fetchApiKeys();
    };

    // 重置搜索
    const handleResetSearch = () => {
      searchForm.name = '';
      searchForm.status = undefined;
      handleSearch();
    };

    // 表格变化
    const handleTableChange = (pag) => {
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;
      fetchApiKeys();
    };

    // 显示新增弹窗
    const showAddModal = () => {
      modalTitle.value = '新增密钥';
      currentRecord.value = null;
      formState.name = '';
      formState.status = 1;
      formState.remark = '';
      modalVisible.value = true;
    };

    // 显示编辑弹窗
    const handleEdit = (record) => {
      modalTitle.value = '编辑密钥';
      currentRecord.value = record;
      formState.name = record.name;
      formState.status = record.status;
      formState.remark = record.remark;
      modalVisible.value = true;
    };

    // 查看密钥信息
    const handleView = (record) => {
      currentRecord.value = record;
      viewModalVisible.value = true;
    };

    // 重置密钥
    const handleResetApiKey = async (record) => {
      try {
        const res = await resetApiKey(record.id);
        if (res.code === 200) {
          message.success('重置成功');
          currentRecord.value = {
            ...record,
            apikey: res.data.apikey
          };
          viewModalVisible.value = true;
        }
      } catch (error) {
        console.error('重置密钥失败:', error);
        message.error('重置密钥失败');
      }
    };

    // 删除密钥
    const handleDelete = async (record) => {
      try {
        const res = await deleteApiKey(record.id);
        if (res.code === 200) {
          message.success('删除成功');
          if (apiKeyList.value.length === 1 && pagination.current > 1) {
            pagination.current -= 1;
          }
          fetchApiKeys();
        }
      } catch (error) {
        console.error('删除密钥失败:', error);
        message.error('删除密钥失败');
      }
    };

    // 提交表单
    const handleModalOk = () => {
      formRef.value.validate().then(async () => {
        try {
          let res;
          if (currentRecord.value) {
            res = await updateApiKey(currentRecord.value.id, formState);
          } else {
            res = await createApiKey(formState);
          }

          if (res.code === 200) {
            message.success(`${modalTitle.value}成功`);
            modalVisible.value = false;
            // 如果是新增，显示密钥信息
            if (!currentRecord.value) {
              setTimeout(() => {
                currentRecord.value = { ...res.data };
                viewModalVisible.value = true;
              }, 300);
            }
            fetchApiKeys();
          } else {
            message.error(res.message || `${modalTitle.value}失败`);
          }
        } catch (error) {
          console.error(`${modalTitle.value}失败:`, error);
          message.error(error.message || `${modalTitle.value}失败`);
        }
      });
    };

    // 取消表单
    const handleModalCancel = () => {
      formRef.value?.resetFields();
      modalVisible.value = false;
    };

    // 关闭查看弹窗
    const handleViewModalCancel = () => {
      currentRecord.value = null;
      viewModalVisible.value = false;
    };

    // 复制文本
    const copyText = (text) => {
      navigator.clipboard.writeText(text).then(() => {
        message.success('复制成功');
      }).catch(() => {
        message.error('复制失败');
      });
    };

    onMounted(() => {
      fetchApiKeys();
    });

    return {
      loading,
      apiKeyList,
      columns,
      pagination,
      searchForm,
      modalVisible,
      viewModalVisible,
      modalTitle,
      formRef,
      formState,
      rules,
      currentRecord,
      handleSearch,
      handleResetSearch,
      handleTableChange,
      showAddModal,
      handleEdit,
      handleView,
      handleResetApiKey,
      handleDelete,
      handleModalOk,
      handleModalCancel,
      handleViewModalCancel,
      copyText,
      formatDate
    };
  }
});

</script>

<style lang="less" scoped>
.apikey-container {
  padding: 24px;

  .table-operations {
    margin-bottom: 16px;
  }

  .table-search-form {
    margin-bottom: 24px;

    :deep(.ant-form-item) {
      margin-bottom: 16px;
    }
  }

  .copy-icon {
    cursor: pointer;
    color: #1890ff;

    &:hover {
      color: #40a9ff;
    }
  }
}
</style> 