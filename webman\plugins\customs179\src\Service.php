<?php
namespace plugins\customs179;

use app\model\AppLog;
use app\model\AppConfigTemplate;
use app\model\Order;

class Service
{
    protected $config;
    protected $appId;
    
    public function __construct($appId, array $config)
    {
        $this->appId = $appId;
        $this->config = $config;
    }
    
    /**
     * 生成6位随机接口key
     */
    public static function generateKey(): string
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $key = '';
        
        do {
            $key = '';
            for ($i = 0; $i < 6; $i++) {
                $key .= $characters[random_int(0, strlen($characters) - 1)];
            }
            // 检查key是否已存在
            $exists = AppConfigTemplate::where('app_id', function($query) {
                $query->select('id')
                    ->from('apps')
                    ->where('code', 'customs179')
                    ->where('status', 1)
                    ->limit(1);
            })->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(settings, '$.key')) = ?", [$key])
              ->exists();
        } while ($exists); // 如果key已存在，重新生成

        return $key;
    }

    /**
     * 验证接口key
     */
    public function validateKey($key): bool
    {
        if (empty($key) || empty($this->config['key'])) {
            return false;
        }
        return $key === $this->config['key'];
    }

    /**
     * 验证渠道唯一性
     */
    public static function validateChannelUnique($channelId, $excludeTemplateId = null): bool
    {
        $query = AppConfigTemplate::where('app_id', function($query) {
            $query->select('id')
                ->from('apps')
                ->where('code', 'customs179')
                ->where('status', 1)
                ->limit(1);
        })->where('settings->channel_id', $channelId);

        if ($excludeTemplateId) {
            $query->where('id', '!=', $excludeTemplateId);
        }

        return !$query->exists();
    }

    /**
     * 处理179公告数据
     */
    public function handle179Data($data)
    {
        try {
            // 将$data['openReq'] json数据转数组
            $data['openReq'] = json_decode($data['openReq'], true);
            // 记录操作日志
            $this->log('info', '接收到179公告数据', [
                'channel_id' => $this->config['channel_id'],
                'data' => $data
            ]);
            
            // TODO: 实现具体的179公告数据处理逻辑
            // 查询该渠道下是否存在该订单
            $order = Order::where('channel_id', $this->config['channel_id'])
                ->where('order_no', $data['openReq']['orderNo'])
                ->first();

            if ($order) {
                // 记录179请求
                $result = $order->fill([
                    'is_spot_check' => 1,
                    'sessionID' => $data['openReq']['sessionID'],
                    'serviceTime' => date('Y-m-d H:i:s', time()),
                    'updated_at' => date('Y-m-d H:i:s')
                ])->save();

                if (!$result) {
                    $this->log('error', '更新订单数据失败', [
                        'order_id' => $order->id,
                        'order_no' => $order->order_no
                    ]);
                    return false;
                }

                $this->log('info', '更新订单数据成功', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'is_spot_check' => 1
                ]);

                return true;
            } else {
                // 找不到该订单
                $this->log('error', '未找到对应订单', [
                    'channel_id' => $this->config['channel_id'],
                    'order_no' => $data['openReq']['orderNo']
                ]);
                return false;
            }
            
        } catch (\Exception $e) {
            // 记录错误日志
            $this->log('error', '处理179公告数据失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
    
    /**
     * 记录日志
     */
    protected function log($level, $message, array $context = [])
    {
        AppLog::create([
            'app_id' => $this->appId,
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
} 