import request from '@/utils/request'

// 获取订单列表
export function getOrders(params) {
  return request({
    url: '/orders',
    method: 'get',
    params
  })
}

// 获取订单详情
export function getOrder(orderNo) {
  return request({
    url: `/orders/${orderNo}`,
    method: 'get'
  })
}

// 更新订单状态
export function updateStatus(data) {
  return request({
    url: '/orders/status',
    method: 'post',
    data
  })
}

// 创建订单
export function createOrder(data) {
  return request({
    url: '/open-api/order/create',
    method: 'post',
    data
  })
}

// 更新订单状态
export function updateOrderStatus(data) {
  return request({
    url: '/open-api/order/update',
    method: 'post',
    data
  })
}

// 导出订单
export function exportOrders(params) {
  return request({
    url: '/orders/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}

// 获取订单统计数据
export function getOrderStats() {
  return request({
    url: '/orders/stats',
    method: 'get'
  })
}

// 获取订单趋势数据
export function getOrderTrend(params) {
  return request({
    url: '/orders/trend',
    method: 'get',
    params
  })
}

// 获取渠道订单占比
export function getOrderChannelStats() {
  return request({
    url: '/orders/channel-stats',
    method: 'get'
  })
}

// 获取热销商品排行
export function getHotProducts() {
  return request({
    url: '/orders/hot-products',
    method: 'get'
  })
} 

export function getRealTimeData() {
  return request({
    url: '/orders/realtime-data',
    method: 'get'
  });
}

export function getLatestOrders() {
  return request({
    url: '/orders/latest-orders',
    method: 'get'
  });
}

export function getHotProductsWithTrend() {
  return request({
    url: '/orders/hot-products-trend',
    method: 'get'
  });
}

// 获取订单趋势数据
export function getTrend(params) {
  return request({
    url: '/orders/trend',
    method: 'get',
    params
  });
}

// 获取渠道订单占比
export function getChannelStats() {
  return request({
    url: '/orders/channel-stats',
    method: 'get'
  });
}

// 删除订单
export function deleteOrder(orderNo) {
  return request({
    url: `/orders/${orderNo}`,
    method: 'delete'
  })
}