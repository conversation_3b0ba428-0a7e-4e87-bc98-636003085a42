<?php

namespace app\model;

use support\Model;

class DictionaryItem extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'dictionary_item';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'dict_id',
        'label',
        'value',
        'sort',
        'status',
        'remark'
    ];

    /**
     * 关联字典
     */
    public function dictionary()
    {
        return $this->belongsTo(Dictionary::class, 'dict_id');
    }
} 