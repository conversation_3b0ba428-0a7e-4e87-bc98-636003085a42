<?php
namespace app\model;

use support\Model;

class Api<PERSON>ey extends Model
{
    protected $table = 'api_keys';
    
    protected $fillable = [
        'name',
        'apikey',
        'status',
        'remark',
        'last_used_at',
        'created_at',
        'updated_at'
    ];

    /**
     * 生成 API Key
     */
    public static function generateApiKey(): string
    {
        return bin2hex(random_bytes(16));
    }

    /**
     * 验证API密钥
     */
    public static function validateApiKey($appId, $appSecret)
    {
        $apiKey = self::where('app_id', $appId)
            ->where('status', 1)
            ->first();

        if (!$apiKey) {
            return false;
        }

        if ($apiKey->apikey !== $appSecret) {
            return false;
        }

        // 更新最后使用时间
        $apiKey->update([
            'last_used_at' => date('Y-m-d H:i:s')
        ]);

        return true;
    }
} 