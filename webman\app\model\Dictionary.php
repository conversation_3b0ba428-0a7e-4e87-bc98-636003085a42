<?php

namespace app\model;

use support\Model;

class Dictionary extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'dictionary';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'code',
        'status',
        'remark'
    ];

    /**
     * 关联字典项
     */
    public function items()
    {
        return $this->hasMany(DictionaryItem::class, 'dict_id');
    }
} 