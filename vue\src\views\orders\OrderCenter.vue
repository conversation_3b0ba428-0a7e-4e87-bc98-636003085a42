<template>
  <div class="order-center">
    <a-card :bordered="false">
      <!-- 搜索表单 -->
      <a-form layout="inline" class="table-search-form">
        <a-row :gutter="[16, 0]">
          <a-col :span="6">
            <a-form-item label="订单号">
              <a-input v-model:value="searchForm.order_no" placeholder="请输入订单号" allowClear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="运单号">
              <a-input v-model:value="searchForm.express_no" placeholder="请输入快递单号" allowClear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="申报模板">
              <a-select v-model:value="searchForm.template_id" placeholder="请选择申报模板" allowClear>
                <a-select-option v-for="item in templateOptions" :key="item.id" :value="item.id">
                  {{ item.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="订单状态">
              <a-select v-model:value="searchForm.order_status" placeholder="请选择状态" allowClear>
                <a-select-option value="10">已支付</a-select-option>
                <a-select-option value="15">已发货</a-select-option>
                <a-select-option value="20">已完成</a-select-option>
                <a-select-option value="5">已取消</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="下单时间" style="width: 100%;">
              <a-range-picker v-model:value="searchForm.order_time" style="width: 100%;"/>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="支付方式">
              <a-select v-model:value="searchForm.payment_method" placeholder="请选择支付方式" allowClear>
                <a-select-option value="alipay">支付宝</a-select-option>
                <a-select-option value="wechat">微信支付</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-space>
              <a-button type="primary" @click="handleSearch">
                <template #icon><search-outlined /></template>
                查询
              </a-button>
              <a-button @click="handleReset">
                <template #icon><reload-outlined /></template>
                重置
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>

      <!-- 操作按钮 -->
      <div class="table-operations">
        <a-space>
          <a-button type="primary" @click="handleExport">
            <template #icon><download-outlined /></template>
            导出
          </a-button>
          <a-button @click="handleRefresh">
            <template #icon><sync-outlined /></template>
            刷新
          </a-button>
        </a-space>
      </div>

      <!-- 订单列表 -->
      <a-table
        :columns="columns"
        :data-source="orderList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        :row-key="record => record.id"
        :scroll="{ x: 1500 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'order_no'">
            <template v-if="record.is_spot_check">
              <a-tooltip>
                <template #title>海关抽查：{{ record.serviceTime }}</template>
                <AlertOutlined :style="{fontSize: '16px', color: '#f00'}"/>
              </a-tooltip>
            </template>
            <template v-if="record.is_test">
                <a-tag color="orange">测</a-tag> {{ record.order_no }}
              </template>
              <template v-else>
                {{ record.order_no }}
              </template>
          </template>
          <template v-else-if="column.key === 'template_name'">
            
            <a-tag color="red">{{ record.template?.name }}</a-tag>
          </template>
          <template v-else-if="column.key === 'order_status'">
            <a-tag :color="getStatusColor(record.order_status)">
              {{ getStatusText(record.order_status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'payment_method'">
            {{ record.payment_method === 'alipay' ? '支付宝' : '微信支付' }}
          </template>
          <template v-else-if="column.key === 'amount'">
            ¥{{ record.pay_amount }}
          </template>
          <template v-else-if="column.key === 'shipping_info'">
            <div>
              <div v-if="record.express_name">快递公司：{{ record.express_name }}</div>
              <div v-if="record.express_no">快递单号：{{ record.express_no }}</div>
              <div v-if="record.shipping_time">发货时间：{{ formatDate(record.shipping_time) }}</div>
              <div v-if="!record.express_name && !record.express_no && !record.shipping_time">--</div>
            </div>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="handleView(record)">
                <template #icon><eye-outlined /></template>
                查看
              </a-button>
              <template v-if="record.order_status === 10">
                <!-- <a-popconfirm
                  title="确定要发货吗？"
                  @confirm="handleShip(record)"
                >
                  <a-button type="link">
                    <template #icon><car-outlined /></template>
                    发货
                  </a-button>
                </a-popconfirm> -->
                <a-popconfirm
                  title="确定要取消订单吗？"
                  @confirm="() => handleCancel(record)"
                >
                  <a-button type="link" danger :loading="cancellingOrderNos.includes(record.order_no)" :disabled="cancellingOrderNos.includes(record.order_no)">
                    <template #icon><close-outlined /></template>
                    取消
                  </a-button>
                </a-popconfirm>
              </template>
              <template v-else-if="record.order_status === 5">
                <a-popconfirm
                  title="确定要删除该订单吗？删除后无法恢复！"
                  @confirm="handleDelete(record)"
                >
                  <a-button type="link" danger>
                    <template #icon><DeleteOutlined /></template>
                    删除
                  </a-button>
                </a-popconfirm>
              </template>
            </a-space>
          </template>
          <template v-else-if="column.key === 'receiver_info'">
            <div class="receiver-info">
              <div>{{ record.address?.receiver_name }} {{ record.address?.receiver_phone }}</div>
              <div class="address">{{ record.address?.receiver_address }}</div>
            </div>
          </template>
        </template>
      </a-table>

      <!-- 订单详情抽屉 -->
      <a-drawer
        v-model:open="drawerVisible"
        :title="'订单详情 - ' + currentOrder?.order_no"
        width="800"
        :footer-style="{ textAlign: 'right' }"
        @close="handleDrawerClose"
      >
        <template v-if="currentOrder">
          <a-descriptions title="基本信息" :column="2" bordered>
            <a-descriptions-item label="订单编号">
              <template v-if="currentOrder.is_spot_check">
                <a-tooltip>
                  <template #title>海关抽查：{{ currentOrder.serviceTime }}</template>
                  <AlertOutlined :style="{fontSize: '16px', color: '#f00'}"/>
                </a-tooltip>
              </template>
              <template v-if="currentOrder.is_test">
                <a-tag color="orange">测</a-tag> {{ currentOrder.order_no }}
              </template>
              <template v-else>
                {{ currentOrder.order_no }}
              </template>
            </a-descriptions-item>
            <a-descriptions-item label="下单时间">{{ formatDate(currentOrder.order_time) }}</a-descriptions-item>
            <a-descriptions-item label="订单状态">
              <a-tag :color="getStatusColor(currentOrder.order_status)">
                {{ getStatusText(currentOrder.order_status) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="税费">¥{{ currentOrder.tax_amount }}</a-descriptions-item>
            <a-descriptions-item label="运杂费">¥{{ currentOrder.freight_amount }}</a-descriptions-item>
            <a-descriptions-item label="优惠金额">¥{{ currentOrder.discount_amount }}</a-descriptions-item>
            <a-descriptions-item label="订单金额">¥{{ currentOrder.pay_amount }}</a-descriptions-item>
            <a-descriptions-item label="支付方式">
              {{ currentOrder.payment_method === 'alipay' ? '支付宝' : '微信支付' }}
            </a-descriptions-item>
            <a-descriptions-item label="支付流水号">{{ currentOrder.transaction_id }}</a-descriptions-item>
            <a-descriptions-item label="申报模板" :span="2">{{ currentOrder.template?.name }}</a-descriptions-item>
          </a-descriptions>

          <a-divider />

          <a-descriptions title="商品信息" :column="1" bordered>
            <a-descriptions-item>
              <a-table
                :columns="itemColumns"
                :data-source="currentOrder.items"
                :pagination="false"
                size="small"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'amount'">
                    ¥{{ record.total_price }}
                  </template>
                </template>
              </a-table>
            </a-descriptions-item>
          </a-descriptions>

          <a-divider />

          <a-descriptions title="收货信息" :column="2" bordered>
            <a-descriptions-item label="收货人">{{ currentOrder.address?.receiver_name }}</a-descriptions-item>
            <a-descriptions-item label="联系电话">{{ currentOrder.address?.receiver_phone }}</a-descriptions-item>
            <a-descriptions-item label="收货地址" :span="2">{{ currentOrder.address?.receiver_address }}</a-descriptions-item>
            <a-descriptions-item label="订购人">{{ currentOrder.address?.buyer_name }}</a-descriptions-item>
            <a-descriptions-item label="订购人电话">{{ currentOrder.address?.buyer_phone }}</a-descriptions-item>
            <a-descriptions-item label="订购人身份证" :span="2">{{ currentOrder.address?.buyer_id_number }}</a-descriptions-item>
          </a-descriptions>

          <template v-if="currentOrder.order_status === 15">
            <a-divider />
            <a-descriptions title="物流信息" :column="2" bordered>
              <a-descriptions-item label="快递公司">{{ currentOrder.express_name }}</a-descriptions-item>
              <a-descriptions-item label="快递单号">{{ currentOrder.express_no }}</a-descriptions-item>
              <a-descriptions-item label="发货时间">{{ formatDate(currentOrder.shipping_time) }}</a-descriptions-item>
            </a-descriptions>
          </template>
        </template>
      </a-drawer>

      <!-- 发货弹窗 -->
      <a-modal
        v-model:open="shipModalVisible"
        title="订单发货"
        @ok="handleShipOk"
        @cancel="handleShipCancel"
      >
        <a-form
          ref="shipFormRef"
          :model="shipForm"
          :rules="shipRules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="快递公司" name="express_name">
            <a-input v-model="shipForm.express_name" placeholder="请输入快递公司" allowClear />
          </a-form-item>
          <a-form-item label="快递单号" name="express_no">
            <a-input v-model="shipForm.express_no" placeholder="请输入快递单号" allowClear />
          </a-form-item>
        </a-form>
      </a-modal>
    </a-card>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, h } from 'vue';
import {
  SearchOutlined,
  ReloadOutlined,
  DownloadOutlined,
  SyncOutlined,
  EyeOutlined,
  CarOutlined,
  CloseOutlined,
  AlertOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';
import { formatDate } from '@/utils/utils';
import { getOrders, updateStatus, exportOrders, deleteOrder } from '@/api/order';
import { getDeclarationTemplates } from '@/api/system/declaration-template';

export default defineComponent({
  name: 'OrderCenter',
  components: {
    SearchOutlined,
    ReloadOutlined,
    DownloadOutlined,
    SyncOutlined,
    EyeOutlined,
    CarOutlined,
    CloseOutlined,
    AlertOutlined,
    DeleteOutlined
  },
  setup() {
    const loading = ref(false);
    const orderList = ref([]);
    const drawerVisible = ref(false);
    const currentOrder = ref(null);
    const shipModalVisible = ref(false);
    const shipFormRef = ref(null);
    const templateOptions = ref([]);
    const cancellingOrderNos = ref([]);

    // 发货表单
    const shipForm = reactive({
      express_name: '',
      express_no: ''
    });

    // 发货表单验证规则
    const shipRules = {
      express_name: [
        { required: true, message: '请输入快递公司' }
      ],
      express_no: [
        { required: true, message: '请输入快递单号' }
      ]
    };

    // 表格列定义
    const columns = [
      {
        title: '订单编号',
        dataIndex: 'order_no',
        key: 'order_no',
        width: 120,
        fixed: 'left',
      },
      {
        title: '申报模板',
        dataIndex: 'template_name',
        key: 'template_name',
        width: 80
      },
      {
        title: '订单金额',
        key: 'amount',
        width: 60
      },
      {
        title: '支付方式',
        dataIndex: 'payment_method',
        key: 'payment_method',
        width: 60
      },
      {
        title: '订单状态',
        dataIndex: 'order_status',
        key: 'order_status',
        width: 60
      },
      {
        title: '收件信息',
        key: 'receiver_info',
        width: 150,
        customRender: ({ record }) => {
          const address = record.address || {};
          return h('div', { class: 'receiver-info' }, [
            h('div', null, [address.receiver_name, ' ', address.receiver_phone]),
            h('div', { class: 'address' }, address.receiver_address)
          ]);
        }
      },
      {
        title: '发货信息',
        key: 'shipping_info',
        width: 150
      },
      {
        title: '下单时间',
        dataIndex: 'order_time',
        key: 'order_time',
        width: 100
      },
      {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 150
      }
    ];

    // 商品表格列定义
    const itemColumns = [
      {
        title: '商品名称',
        dataIndex: 'channel_product_name',
        key: 'name'
      },
      {
        title: '商品编码',
        dataIndex: 'channel_product_no',
        key: 'code',
        width: 150
      },
      {
        title: '数量',
        dataIndex: 'quantity',
        key: 'quantity',
        width: 80
      },
      {
        title: '单价',
        dataIndex: 'unit_price',
        key: 'price',
        width: 100,
        render: (text) => `¥${text.toFixed(2)}`
      },

      {
        title: '金额',
        key: 'amount',
        width: 100
      }
    ];

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showTotal: total => `共 ${total} 条`,
      showSizeChanger: true,
      showQuickJumper: true
    });

    // 搜索表单
    const searchForm = reactive({
      order_no: '',
      template_id: undefined,
      order_status: undefined,
      order_time: [],
      payment_method: undefined,
      express_no: ''
    });

    // 获取申报模板选项
    const fetchTemplateOptions = async () => {
      try {
        const res = await getDeclarationTemplates({ pageSize: 1000 });
        if (res.code === 200) {
          templateOptions.value = res.data.list;
        }
      } catch (error) {
        console.error('获取申报模板失败:', error);
        message.error('获取申报模板失败');
      }
    };

    // 获取订单列表
    const fetchOrders = async () => {
      loading.value = true;
      try {
        const params = {
          page: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm
        };
        // 处理日期范围
        if (searchForm.order_time?.length === 2) {
          params.start_time = formatDate(searchForm.order_time[0]);
          params.end_time = formatDate(searchForm.order_time[1]);
        }
        delete params.order_time;

        const res = await getOrders(params);
        if (res.code === 200) {
          orderList.value = res.data.list;
          pagination.total = res.data.total;
        }
      } catch (error) {
        console.error('获取订单列表失败:', error);
        message.error('获取订单列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 获取状态文本
    const getStatusText = (status) => {
      const statusMap = {
        10: '已支付',
        15: '已发货',
        20: '已完成',
        5: '已取消'
      };
      return statusMap[status] || status;
    };

    // 获取状态颜色
    const getStatusColor = (status) => {
      const colorMap = {
        10: 'blue',
        15: 'purple',
        20: 'success',
        5: 'error'
      };
      return colorMap[status] || 'default';
    };

    // 搜索
    const handleSearch = () => {
      pagination.current = 1;
      fetchOrders();
    };

    // 重置搜索
    const handleReset = () => {
      searchForm.order_no = '';
      searchForm.template_id = undefined;
      searchForm.order_status = undefined;
      searchForm.order_time = [];
      searchForm.payment_method = undefined;
      searchForm.express_no = '';
      handleSearch();
    };

    // 导出
    const handleExport = async () => {
      try {
        const params = { ...searchForm };
        if (params.order_time?.length === 2) {
          params.start_time = formatDate(params.order_time[0]);
          params.end_time = formatDate(params.order_time[1]);
        }
        delete params.order_time;

        const res = await exportOrders(params);
        const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `订单列表_${formatDate(new Date())}.xlsx`;
        link.click();
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error('导出失败:', error);
        message.error('导出失败');
      }
    };

    // 刷新
    const handleRefresh = () => {
      fetchOrders();
    };

    // 表格变化
    const handleTableChange = (pag) => {
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;
      fetchOrders();
    };

    // 查看订单
    const handleView = (record) => {
      currentOrder.value = record;
      drawerVisible.value = true;
    };

    // 关闭抽屉
    const handleDrawerClose = () => {
      currentOrder.value = null;
      drawerVisible.value = false;
    };

    // 发货
    const handleShip = (record) => {
      currentOrder.value = record;
      shipForm.express_name = '';
      shipForm.express_no = '';
      shipModalVisible.value = true;
    };

    // 确认发货
    const handleShipOk = () => {
      shipFormRef.value.validate().then(async () => {
        try {
          const res = await updateStatus(currentOrder.value.order_no, {
            status: 'shipped',
            ...shipForm
          });
          if (res.code === 200) {
            message.success('发货成功');
            shipModalVisible.value = false;
            fetchOrders();
          }
        } catch (error) {
          console.error('发货失败:', error);
          message.error('发货失败');
        }
      });
    };

    // 取消发货
    const handleShipCancel = () => {
      shipFormRef.value?.resetFields();
      shipModalVisible.value = false;
    };

    // 取消订单
    const handleCancel = async (record) => {
      try {
        const res = await updateStatus({
          order_no: record.order_no,
          status: 'cancelled'
        });
        if (res.code === 200) {
          message.success('取消成功');
          fetchOrders();
        }
      } catch (error) {
        console.error('取消订单失败:', error);
        message.error('取消订单失败');
      }
    };

    // 删除订单
    const handleDelete = async (record) => {
      Modal.confirm({
        title: '再次确认',
        content: '确定要删除该订单吗？删除后订单信息将无法恢复！',
        okText: '确认删除',
        cancelText: '取消',
        onOk: async () => {
          try {
            const res = await deleteOrder(record.order_no);
            if (res.code === 200) {
              message.success('删除成功');
              fetchOrders();
            } else {
              message.error(res.message || '删除失败');
            }
          } catch (error) {
            console.error('删除订单失败:', error);
            message.error('删除订单失败');
          }
        }
      });
    };

    onMounted(() => {
      fetchTemplateOptions();
      fetchOrders();
    });

    return {
      loading,
      orderList,
      columns,
      itemColumns,
      pagination,
      searchForm,
      templateOptions,
      drawerVisible,
      currentOrder,
      shipModalVisible,
      shipFormRef,
      shipForm,
      shipRules,
      getStatusText,
      getStatusColor,
      handleSearch,
      handleReset,
      handleExport,
      handleRefresh,
      handleTableChange,
      handleView,
      handleDrawerClose,
      handleShip,
      handleShipOk,
      handleShipCancel,
      handleCancel,
      handleDelete,
      formatDate,
      cancellingOrderNos
    };
  }
});
</script>

<style lang="less" scoped>
.order-center {
  padding: 24px;

  .table-operations {
    margin-bottom: 16px;
  }

  .table-search-form {
    margin-bottom: 24px;

    :deep(.ant-form-item) {
      margin-bottom: 16px;
    }
  }

  .receiver-info {
    .address {
      color: #666;
      font-size: 12px;
      margin-top: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }

  .entrance-card {
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-2px);
    }

    .entrance-icon {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 60px;
      background: #f5f5f5;
      font-size: 24px;
      color: #272343;
    }

    :deep(.ant-card-meta-title) {
      font-size: 14px;
      margin-bottom: 4px !important;
    }

    :deep(.ant-card-meta-description) {
      font-size: 12px;
      color: #999;
    }
  }
}
</style> 