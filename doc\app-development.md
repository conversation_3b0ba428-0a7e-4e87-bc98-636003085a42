# 应用开发指南

## 一、概述

本文档介绍如何开发一个新的应用并集成到系统中。应用是一个独立的功能模块，可以是第三方服务的集成(如支付接口、快递接口等)，也可以是独立的业务功能。

## 二、目录结构

```bash
webman/plugins/
└── your-app/                # 你的应用目录
    ├── config.json         # 应用配置文件(必需)
    ├── icon.png           # 应用图标(可选)
    └── src/               # 源代码目录
        └── Service.php    # 服务类(必需)
```

## 三、开发步骤

### 1. 创建应用目录
```bash
mkdir -p webman/plugins/your-app/src
```

### 2. 创建配置文件
在 `config.json` 中定义应用的基本信息和配置项：

```json
{
    "name": "应用名称",
    "code": "your-app",
    "version": "1.0.0",
    "description": "应用描述",
    "picture": "https://gaodux.oss-cn-qingdao.aliyuncs.com/gaodux_customs/wechat.jpg",
    "author": {
        "name": "开发者",
        "email": "<EMAIL>"
    },
    "settings_schema": {
        "fields": [
            {
                "field": "api_key",        // 配置项字段名
                "label": "API密钥",        // 显示名称
                "type": "password",       // 字段类型
                "required": true,         // 是否必填
                "default": "",           // 默认值
                "placeholder": "请输入API密钥", // 占位文本
                "description": "用于接口认证的密钥", // 字段说明
                "encrypt": true,         // 是否加密存储
                "rules": {              // 验证规则(可选)
                    "pattern": "^[A-Za-z0-9]{32}$",
                    "message": "密钥格式不正确"
                }
            }
        ],
        "groups": [                // 配置分组(可选)
            {
                "name": "basic",
                "label": "基础设置",
                "fields": ["api_key"]
            }
        ]
    }
}
```

### 3. 创建服务类
在 `src/Service.php` 中实现应用的具体功能：

```php
<?php
namespace plugins\your_app;

use app\model\AppLog;

class Service
{
    protected $config;
    protected $appId;
    
    public function __construct($appId, array $config)
    {
        $this->appId = $appId;
        $this->config = $config;
    }
    
    /**
     * 示例方法
     */
    public function doSomething($data)
    {
        try {
            // 记录操作日志
            $this->log('info', '开始处理', [
                'input' => $data
            ]);
            
            // 获取配置
            $apiKey = $this->config['api_key'] ?? '';
            
            // 实现具体业务逻辑
            $result = $this->callExternalApi($apiKey, $data);
            
            // 记录成功日志
            $this->log('info', '处理成功', [
                'result' => $result
            ]);
            
            return $result;
            
        } catch (\Exception $e) {
            // 记录错误日志
            $this->log('error', '处理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }
    
    /**
     * 记录日志
     */
    protected function log($level, $message, array $context = [])
    {
        AppLog::create([
            'app_id' => $this->appId,
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
}
```

### 4. 注册应用
在数据库中注册应用信息：

```sql
INSERT INTO `gaodux_apps` (
    `name`, 
    `code`, 
    `description`, 
    `version`, 
    `status`, 
    `settings`, 
    `created_at`, 
    `updated_at`
) VALUES (
    '你的应用名称',
    'your-app',
    '应用描述',
    '1.0.0',
    1,
    '{}',
    NOW(),
    NOW()
);
```

## 四、配置项说明

### 支持的字段类型
- `text`: 文本输入框
- `password`: 密码输入框
- `switch`: 开关
- `select`: 下拉选择框
- `checkbox`: 多选框

### 字段属性
| 属性 | 类型 | 说明 | 是否必需 |
|------|------|------|----------|
| field | string | 字段名 | 是 |
| label | string | 显示名称 | 是 |
| type | string | 字段类型 | 是 |
| required | boolean | 是否必填 | 否 |
| default | mixed | 默认值 | 否 |
| placeholder | string | 占位文本 | 否 |
| description | string | 字段说明 | 否 |
| encrypt | boolean | 是否加密 | 否 |
| rules | object | 验证规则 | 否 |
| options | array | 选项列表 | 否 |

## 五、使用方法

### 1. 在控制器中使用

```php
<?php
namespace app\controller;

use app\model\App;
use support\Request;

class YourController
{
    public function someAction(Request $request)
    {
        try {
            // 获取应用实例
            $app = App::where('code', 'your-app')
                     ->where('status', 1)
                     ->first();
                     
            if (!$app) {
                throw new \Exception('应用未启用');
            }

            // 创建服务实例
            $service = new \plugins\your_app\Service($app->id, $app->settings);
            
            // 调用服务方法
            $result = $service->doSomething([
                'param1' => 'value1',
                'param2' => 'value2'
            ]);
            
            return json(['code' => 200, 'data' => $result]);
            
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => $e->getMessage()]);
        }
    }
}
```

### 2. 在其他服务中使用

```php
<?php
namespace app\service;

use app\model\App;

class YourService
{
    public function someMethod()
    {
        // 获取应用实例
        $app = App::where('code', 'your-app')
                 ->where('status', 1)
                 ->first();
                 
        if (!$app) {
            throw new \Exception('应用未启用');
        }

        // 创建服务实例
        $service = new \plugins\your_app\Service($app->id, $app->settings);
        
        // 调用服务方法
        return $service->doSomething([...]);
    }
}
```

## 六、最佳实践

### 1. 配置管理
- 所有敏感配置都应该设置 `encrypt: true`
- 为所有必填字段添加适当的验证规则
- 使用配置分组提高可维护性

### 2. 错误处理
- 捕获并记录所有可能的异常
- 提供清晰的错误信息
- 使用日志记录详细的错误上下文

### 3. 日志记录
- 记录所有关键操作
- 记录输入和输出数据
- 使用适当的日志级别
- 包含足够的上下文信息

### 4. 安全性
- 加密存储敏感信息
- 验证所有输入数据
- 不在日志中记录敏感信息
- 使用 HTTPS 进行 API 调用

### 5. 性能
- 缓存频繁使用的数据
- 避免重复的 API 调用
- 使用异步处理耗时操作

## 七、常见问题

### 1. 配置不生效
- 检查数据库中的 settings 字段是否正确
- 确认配置项名称与代码中使用的一致
- 查看日志中是否有相关错误信息

### 2. 日志不显示
- 确认应用ID是否正确
- 检查日志写入权限
- 验证日志相关的数据库表是否存在

### 3. 服务调用失败
- 检查应用是否已启用
- 确认配置信息是否完整
- 查看具体的错误日志

## 八、调试技巧

### 1. 查看应用配置
```php
$app = App::where('code', 'your-app')->first();
var_dump($app->settings);
```

### 2. 测试服务方法
```php
$service = new \plugins\your_app\Service($appId, $config);
$result = $service->doSomething([
    'test' => true,
    'debug' => true
]);
var_dump($result);
```

### 3. 查看日志记录
```sql
SELECT * FROM gaodux_app_logs 
WHERE app_id = ? 
ORDER BY id DESC 
LIMIT 10;
``` 