<?php
namespace app\model;

use support\Model;

class OrderAddress extends Model
{
    protected $table = 'order_addresses';
    
    protected $fillable = [
        'order_id',
        'order_no',
        'receiver_name',
        'receiver_phone',
        'receiver_address',
        'buyer_name',
        'buyer_phone',
        'buyer_id_number',
        'created_at',
        'updated_at'
    ];

    /**
     * 关联订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }
} 