<?php
namespace app\model;

use support\Model;

class App extends Model
{
    protected $table = 'apps';
    
    protected $fillable = [
        'name',
        'code',
        'description',
        'icon',
        'status',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'status' => 'boolean'
    ];

    /**
     * 关联配置模板
     */
    public function configTemplates()
    {
        return $this->hasMany(AppConfigTemplate::class, 'app_id');
    }
}