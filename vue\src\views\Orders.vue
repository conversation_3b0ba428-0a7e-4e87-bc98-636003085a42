<template>
  <div class="orders-container">
    <!-- 数据概览卡片 -->
    <a-row :gutter="[16, 16]" class="data-overview">
      <a-col :span="6">
        <a-card>
          <statistic
            title="今日订单数"
            :value="todayStats.orderCount"
            :precision="0"
            style="margin-right: 16px"
          >
            <template #suffix>
              <span class="stat-suffix">单</span>
            </template>
          </statistic>
          <div class="stat-trend">
            <span :class="['trend-value', todayStats.orderTrend > 0 ? 'up' : 'down']">
              {{ Math.abs(todayStats.orderTrend) }}%
            </span>
            <span class="trend-label">较昨日</span>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <statistic
            title="今日销售额"
            :value="todayStats.salesAmount"
            :precision="2"
            :value-style="{ color: '#272343' }"
          >
            <template #prefix>
              <span class="stat-prefix">¥</span>
            </template>
          </statistic>
          <div class="stat-trend">
            <span :class="['trend-value', todayStats.salesTrend > 0 ? 'up' : 'down']">
              {{ Math.abs(todayStats.salesTrend) }}%
            </span>
            <span class="trend-label">较昨日</span>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <statistic
            title="本月订单数"
            :value="monthStats.orderCount"
            :precision="0"
          >
            <template #suffix>
              <span class="stat-suffix">单</span>
            </template>
          </statistic>
          <div class="stat-trend">
            <span :class="['trend-value', monthStats.orderTrend > 0 ? 'up' : 'down']">
              {{ Math.abs(monthStats.orderTrend) }}%
            </span>
            <span class="trend-label">较上月</span>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <statistic
            title="本月销售额"
            :value="monthStats.salesAmount"
            :precision="2"
            :value-style="{ color: '#272343' }"
          >
            <template #prefix>
              <span class="stat-prefix">¥</span>
            </template>
          </statistic>
          <div class="stat-trend">
            <span :class="['trend-value', monthStats.salesTrend > 0 ? 'up' : 'down']">
              {{ Math.abs(monthStats.salesTrend) }}%
            </span>
            <span class="trend-label">较上月</span>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 图表区域 -->
    <a-row :gutter="[16, 16]" class="chart-section">
      <a-col :span="16">
        <a-card title="渠道订单趋势">
          <div id="orderTrendChart" ref="orderTrendChartRef" style="width: 100%; height: 300px"></div>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card title="渠道订单占比">
          <div id="orderPieChart" ref="orderPieChartRef" style="width: 100%; height: 300px"></div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 热销商品排行 -->
    <a-card title="热销商品排行" class="hot-products">
      <a-row :gutter="[16, 16]">
        <a-col :span="8" v-for="channel in hotProducts" :key="channel.name">
          <a-card :title="channel.name" size="small">
            <a-list
              size="small"
              :data-source="channel.products"
              :pagination="false"
            >
              <template #renderItem="{ item, index }">
                <a-list-item>
                  <div class="hot-product-item">
                    <span class="rank" :class="{ 'top-3': index < 3 }">{{ index + 1 }}</span>
                    <span class="name">{{ item.name }}</span>
                    <span class="sales">{{ item.sales }}件</span>
                  </div>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </a-card>

    <!-- 功能入口 -->
    <a-row :gutter="[16, 16]" class="feature-entrance">
      <a-col :span="6">
        <a-card hoverable class="entrance-card" @click="goToOrderCenter">
          <template #cover>
            <div class="entrance-icon">
              <OrderedListOutlined />
            </div>
          </template>
          <a-card-meta title="订单中心" description="订单管理与处理中心" />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card hoverable class="entrance-card" @click="goToDataScreen">
          <template #cover>
            <div class="entrance-icon">
              <FundOutlined />
            </div>
          </template>
          <a-card-meta title="数据大屏" description="实时订单数据可视化" />
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { defineComponent, onMounted, onUnmounted, ref, nextTick, watch } from 'vue';
import { Statistic } from 'ant-design-vue';
import { OrderedListOutlined, FundOutlined } from '@ant-design/icons-vue';
import * as echarts from 'echarts/core';
import { LineChart, PieChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { useRouter } from 'vue-router';
import { getOrderStats, getOrderTrend, getOrderChannelStats, getHotProducts } from '@/api/order';

// 注册必需的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  LineChart,
  PieChart,
  CanvasRenderer
]);

export default defineComponent({
  name: 'Orders',
  components: {
    Statistic,
    OrderedListOutlined,
    FundOutlined
  },
  setup() {
    // 图表实例
    let orderTrendChart = null;
    let orderPieChart = null;
    const orderTrendChartRef = ref(null);
    const orderPieChartRef = ref(null);

    // 路由实例
    const router = useRouter();
    
    // 页面跳转方法
    const goToOrderCenter = () => {
      router.push('/orders/center');
    };
    
    const goToDataScreen = () => {
      router.push('/orders/screen');
    };

    // 统计数据
    const todayStats = ref({
      orderCount: 0,
      orderTrend: 0,
      salesAmount: 0,
      salesTrend: 0
    });

    const monthStats = ref({
      orderCount: 0,
      orderTrend: 0,
      salesAmount: 0,
      salesTrend: 0
    });

    const hotProducts = ref([]);
    const timeRange = ref('hour');

    // 获取统计数据
    const fetchStats = async () => {
      try {
        const res = await getOrderStats();
        if (res.code === 200) {
          todayStats.value = res.data.today;
          monthStats.value = res.data.month;
        }
      } catch (error) {
        console.error('获取统计数据失败:', error);
      }
    };

    // 获取热销商品数据
    const fetchHotProducts = async () => {
      try {
        const res = await getHotProducts();
        if (res.code === 200) {
          hotProducts.value = res.data;
        }
      } catch (error) {
        console.error('获取热销商品数据失败:', error);
      }
    };

    // 初始化订单趋势图
    const initOrderTrendChart = async () => {
      try {
        await nextTick();
        if (orderTrendChart) {
          orderTrendChart.dispose();
        }
        const chartDom = document.getElementById('orderTrendChart');
        if (!chartDom) {
          console.error('找不到趋势图表容器');
          return;
        }
        orderTrendChart = echarts.init(chartDom);
        
        // 获取趋势数据
        const res = await getOrderTrend({ type: timeRange.value });
        if (res.code !== 200) return;

        const option = {
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: res.data.channels
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: res.data.times
          },
          yAxis: {
            type: 'value'
          },
          series: res.data.series
        };

        orderTrendChart.setOption(option);
      } catch (error) {
        console.error('趋势图表初始化失败:', error);
      }
    };

    // 初始化订单占比图
    const initOrderPieChart = async () => {
      try {
        await nextTick();
        if (orderPieChart) {
          orderPieChart.dispose();
        }
        const chartDom = document.getElementById('orderPieChart');
        if (!chartDom) {
          console.error('找不到占比图表容器');
          return;
        }
        orderPieChart = echarts.init(chartDom);
        
        // 获取渠道占比数据
        const res = await getOrderChannelStats();
        if (res.code !== 200) return;

        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 'left',
            padding: 5
          },
          series: [
            {
              name: '订单占比',
              type: 'pie',
              radius: '60%',
              center: ['60%', '50%'],
              data: res.data,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              },
              label: {
                show: true,
                formatter: '{b}: {d}%'
              }
            }
          ]
        };

        orderPieChart.setOption(option);
      } catch (error) {
        console.error('占比图表初始化失败:', error);
      }
    };

    // 监听时间范围变化
    watch(timeRange, () => {
      initOrderTrendChart();
    });

    // 处理窗口大小变化
    const handleResize = () => {
      try {
        orderTrendChart?.resize();
        orderPieChart?.resize();
      } catch (error) {
        console.error('图表重绘失败:', error);
      }
    };

    // 定时刷新数据
    let refreshTimer = null;
    const startRefreshTimer = () => {
      refreshTimer = setInterval(async () => {
        await Promise.all([
          fetchStats(),
          fetchHotProducts(),
          initOrderTrendChart(),
          initOrderPieChart()
        ]);
      }, 30000); // 每30秒刷新一次
    };

    onMounted(async () => {
      try {
        await Promise.all([
          fetchStats(),
          fetchHotProducts(),
          initOrderTrendChart(),
          initOrderPieChart()
        ]);
        window.addEventListener('resize', handleResize);
        startRefreshTimer();
      } catch (error) {
        console.error('初始化失败:', error);
      }
    });

    onUnmounted(() => {
      if (refreshTimer) {
        clearInterval(refreshTimer);
      }
      window.removeEventListener('resize', handleResize);
      orderTrendChart?.dispose();
      orderPieChart?.dispose();
    });

    return {
      todayStats,
      monthStats,
      hotProducts,
      timeRange,
      orderTrendChartRef,
      orderPieChartRef,
      goToOrderCenter,
      goToDataScreen
    };
  }
});
</script>

<style lang="less" scoped>
.orders-container {
  padding: 24px;

  .data-overview {
    margin-bottom: 24px;
  }

  .chart-section {
    margin-bottom: 24px;
  }

  .hot-products {
    margin-bottom: 24px;
  }

  .stat-trend {
    margin-top: 8px;
    font-size: 13px;

    .trend-value {
      &.up {
        color: #52c41a;
        &::before {
          content: '↑';
          margin-right: 4px;
        }
      }
      &.down {
        color: #ff4d4f;
        &::before {
          content: '↓';
          margin-right: 4px;
        }
      }
    }

    .trend-label {
      margin-left: 8px;
      color: #8c8c8c;
    }
  }

  .hot-product-item {
    display: flex;
    align-items: center;
    width: 100%;

    .rank {
      width: 24px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      background: #f0f0f0;
      border-radius: 12px;
      margin-right: 8px;
      font-size: 12px;

      &.top-3 {
        background: #272343;
        color: white;
      }
    }

    .name {
      flex: 1;
    }

    .sales {
      color: #8c8c8c;
    }
  }

  .feature-entrance {
    .entrance-card {
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
      }

      .entrance-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 50px;
        background: #f5f5f5;
        font-size: 20px;
        color: #272343;
      }

      :deep(.ant-card-meta-title) {
        font-size: 14px;
        margin-bottom: 4px !important;
      }

      :deep(.ant-card-meta-description) {
        font-size: 12px;
        color: #999;
      }
    }
  }
}
</style> 