import request from '@/utils/request';

// 获取商品总数
export function getProductCount() {
  return request({
    url: '/products/count',
    method: 'get'
  });
}

// 获取商品列表
export function getProducts(params) {
  return request({
    url: '/products',
    method: 'get',
    params
  });
}

// 创建商品
export function createProduct(data) {
  return request({
    url: '/products',
    method: 'post',
    data
  });
}

// 更新商品
export function updateProduct(id, data) {
  return request({
    url: `/products/${id}`,
    method: 'put',
    data
  });
}

// 删除商品
export function deleteProduct(id) {
  return request({
    url: `/products/${id}`,
    method: 'delete'
  });
}

// 获取商品分类列表
export function getProductCategories() {
  return request({
    url: '/product-categories',
    method: 'get'
  });
}

// 创建商品分类
export function createProductCategory(data) {
  return request({
    url: '/product-categories',
    method: 'post',
    data
  });
}

// 更新商品分类
export function updateProductCategory(id, data) {
  return request({
    url: `/product-categories/${id}`,
    method: 'put',
    data
  });
}

// 删除商品分类
export function deleteProductCategory(id) {
  return request({
    url: `/product-categories/${id}`,
    method: 'delete'
  });
}

// 创建渠道
export function createChannel(data) {
  return request({
    url: '/product-channels',
    method: 'post',
    data
  });
}

// 获取渠道列表
export function getChannels() {
  return request({
    url: '/product-channels',
    method: 'get'
  });
}

// 删除渠道
export function deleteChannel(id) {
  return request({
    url: `/product-channels/${id}`,
    method: 'delete'
  });
}

// 搜索商品
export function searchProducts(params) {
    return request({
        url: '/products/search',
        method: 'get',
        params
    });
} 