<template>
  <a-modal
    :title="title"
    :open="modelVisible"
    @update:open="val => modelVisible = val"
    :maskClosable="false"
    :footer="null"
    @ok="handleOk"
    @cancel="handleCancel"
    width="1000px"
  >
    <a-steps
      :current="currentStep"
      class="steps-wrapper"
    >
      <a-step title="基本信息" />
      <a-step title="企业信息" />
      <a-step title="海关信息" />
    </a-steps>

    <div class="steps-content">
      <!-- 步骤一：基本信息 -->
      <div v-show="currentStep === 0">
        <a-form
          ref="formRef"
          :model="formState"
          :rules="rules"
          layout="vertical"
        >
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="模板名称" name="name">
                <a-input
                  :value="formState.name"
                  @update:value="val => formState.name = val"
                  placeholder="请输入模板名称（10字以内）"
                  :maxLength="10"
                  showCount
                  allowClear
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="渠道" name="channel_id">
                <a-select
                  :getPopupContainer="getPopupContainer"
                  :value="formState.channel_id"
                  @update:value="val => formState.channel_id = val"
                  placeholder="请选择渠道"
                  :options="options.channels"
                  style="width: 100%"
                  allowClear
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="贸易方式" name="trade_mode">
                <a-select
                  :getPopupContainer="getPopupContainer"
                  :value="formState.trade_mode"
                  @update:value="val => {
                    formState.trade_mode = val;
                    handleTradeModeChange(val);
                  }"
                  placeholder="请选择贸易方式"
                  :options="options.tradeModes"
                  style="width: 100%"
                  allowClear
                />
              </a-form-item>
            </a-col>
            
            <a-col :span="12" v-if="formState.trade_mode === '1210'">
              <a-form-item label="账册编号" name="account_book_no">
                <a-input
                  :value="formState.account_book_no"
                  @update:value="val => formState.account_book_no = val"
                  placeholder="请输入账册编号"
                  allowClear
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 步骤二：企业信息 -->
      <div v-show="currentStep === 1">
        <a-form
          ref="formRef"
          :model="formState"
          :rules="rules"
          layout="vertical"
        >
          <a-row :gutter="16">
            <a-col :span="12">
              <a-card title="电商信息" :bordered="false" class="custom-card">
                <a-form-item label="电商平台" name="platform_enterprise_id">
                  <a-select
                    :getPopupContainer="getPopupContainer"
                    :value="formState.platform_enterprise_id"
                    @update:value="val => formState.platform_enterprise_id = val"
                    placeholder="请选择电商平台"
                    :options="formatEnterpriseOptions(options.platformEnterprises)"
                    style="width: 100%"
                    allowClear
                  />
                </a-form-item>
                <a-form-item label="电商企业" name="ecommerce_enterprise_id">
                  <a-select
                    :getPopupContainer="getPopupContainer"
                    :value="formState.ecommerce_enterprise_id"
                    @update:value="val => {
                      formState.ecommerce_enterprise_id = val;
                      handleEcommerceChange(val);
                    }"
                    placeholder="请选择电商企业"
                    :options="formatEnterpriseOptions(options.ecommerceEnterprises)"
                    style="width: 100%"
                    allowClear
                  />
                </a-form-item>
              </a-card>
            </a-col>
            <a-col :span="12">
              <a-card title="物流信息" :bordered="false" class="custom-card">
                <a-form-item label="物流企业" name="logistics_enterprise_id">
                  <a-select
                    :getPopupContainer="getPopupContainer"
                    :value="formState.logistics_enterprise_id"
                    @update:value="val => formState.logistics_enterprise_id = val"
                    placeholder="请选择物流企业"
                    :options="formatEnterpriseOptions(options.logisticsEnterprises)"
                    style="width: 100%"
                    allowClear
                  />
                </a-form-item>
                <a-form-item label="物流应用" name="logistics_app_id">
                  <a-select
                    :getPopupContainer="getPopupContainer"
                    :value="formState.logistics_app_id"
                    @update:value="val => {
                      formState.logistics_app_id = val;
                      handleLogisticsAppChange(val);
                    }"
                    placeholder="请选择物流应用"
                    :options="formatAppOptions(options.logisticsApps)"
                    style="width: 100%"
                    allowClear
                  />
                </a-form-item>
                <a-form-item 
                  v-if="formState.logistics_app_id"
                  label="配置模板" 
                  name="logistics_template_id"
                >
                  <a-select
                    :getPopupContainer="getPopupContainer"
                    :value="formState.logistics_template_id"
                    @update:value="val => formState.logistics_template_id = val"
                    placeholder="请选择配置模板"
                    :options="formatTemplateOptions(selectedLogisticsApp?.configTemplates)"
                    style="width: 100%"
                    allowClear
                  />
                </a-form-item>
                <a-form-item v-if="formState.trade_mode === '1210'" label="区内企业" name="bonded_enterprise_id">
                  <a-select
                    :value="formState.bonded_enterprise_id"
                    @update:value="val => formState.bonded_enterprise_id = val"
                    placeholder="请选择区内企业"
                    :options="formatEnterpriseOptions(options.bondedEnterprises)"
                    style="width: 100%"
                    allowClear
                  />
                </a-form-item>
              </a-card>
            </a-col>
          </a-row>

          <a-card title="支付与申报信息" :bordered="false" class="custom-card">
            <a-row :gutter="16">
              <a-col :span="24">
                <a-form-item label="支付企业" name="payment_enterprise_ids">
                  <a-select
                    :getPopupContainer="getPopupContainer"
                    :value="formState.payment_enterprise_ids"
                    @update:value="val => {
                      formState.payment_enterprise_ids = val;
                      handlePaymentEnterpriseChange(val);
                    }"
                    placeholder="请选择支付企业"
                    :options="formatEnterpriseOptions(options.paymentEnterprises)"
                    mode="multiple"
                    style="width: 100%"
                    allowClear
                  />
                </a-form-item>
                <!-- 支付企业对应的应用和配置模板 -->
                <template v-if="formState.payment_enterprise_ids?.length">
                  <a-divider>支付应用配置 <small style="color: #999;">（若无需推送支付单则可不配置）</small></a-divider>
                  <div v-for="enterpriseId in formState.payment_enterprise_ids" :key="enterpriseId">
                    <a-row :gutter="16">
                      <a-col :span="8">
                        <a-form-item 
                          :label="getEnterpriseName(enterpriseId, options.paymentEnterprises)"
                          :name="['payment_apps', enterpriseId, 'app_id']"
                          :rules="[{ required: false, message: '请选择支付应用' }]"
                        >
                          <a-select
                            :getPopupContainer="getPopupContainer"
                            :value="formState.payment_apps[enterpriseId]?.app_id"
                            @update:value="val => handlePaymentAppChange(val, enterpriseId)"
                            placeholder="请选择支付应用"
                            :options="formatAppOptions(options.paymentApps)"
                            style="width: 100%"
                            allowClear
                          />
                        </a-form-item>
                      </a-col>
                      <a-col :span="8">
                        <a-form-item 
                          label="配置模板"
                          :name="['payment_apps', enterpriseId, 'template_id']"
                          :rules="[{ required: false, message: '请选择配置模板' }]"
                        >
                          <a-select
                            :getPopupContainer="getPopupContainer"
                            :value="formState.payment_apps[enterpriseId]?.template_id"
                            @update:value="val => handlePaymentTemplateChange(val, enterpriseId)"
                            placeholder="请选择配置模板"
                            :options="formatTemplateOptions(getPaymentAppTemplates(enterpriseId))"
                            style="width: 100%"
                            allowClear
                          />
                        </a-form-item>
                      </a-col>
                    </a-row>
                  </div>
                </template>
              </a-col>
              <a-col :span="12">
                <a-form-item label="申报企业" name="declare_enterprise_id">
                  <a-select
                    :getPopupContainer="getPopupContainer"
                    :value="formState.declare_enterprise_id"
                    @update:value="val => {
                      formState.declare_enterprise_id = val;
                      handleDeclareChange(val);
                    }"
                    placeholder="请选择申报企业"
                    :options="formatEnterpriseOptions(options.declareEnterprises)"
                    style="width: 100%"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="传输企业" name="transmission_enterprise_id">
                  <a-select
                    :getPopupContainer="getPopupContainer"
                    :value="formState.transmission_enterprise_id"
                    @update:value="val => formState.transmission_enterprise_id = val"
                    placeholder="请选择传输企业"
                    :options="formatEnterpriseOptions(options.transmissionEnterprises)"
                    style="width: 100%"
                    allowClear
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </a-form>
      </div>

      <!-- 步骤三：海关信息 -->
      <div v-show="currentStep === 2">
        <a-form
          ref="formRef"
          :model="formState"
          :rules="rules"
          layout="vertical"
        >
          <a-card title="海关申报信息" :bordered="false" class="custom-card">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="申报海关" name="declare_customs">
                  <a-select
                    :getPopupContainer="getPopupContainer"
                    :value="formState.declare_customs"
                    @update:value="val => formState.declare_customs = val"
                    placeholder="请选择申报海关"
                    :options="options.customs"
                    style="width: 100%"
                    show-search
                    :filter-option="filterOption"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="口岸海关" name="port_customs">
                  <a-select
                    :getPopupContainer="getPopupContainer"
                    :value="formState.port_customs"
                    @update:value="val => formState.port_customs = val"
                    placeholder="请选择口岸海关"
                    :options="options.customs"
                    style="width: 100%"
                    show-search
                    :filter-option="filterOption"
                    allowClear
                  />
                </a-form-item>
              </a-col>
            </a-row>

            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="运输方式" name="transport_mode">
                  <a-select
                    :getPopupContainer="getPopupContainer"
                    :value="formState.transport_mode"
                    @update:value="val => formState.transport_mode = val"
                    placeholder="请选择运输方式"
                    :options="options.transportModes"
                    style="width: 100%"
                    allowClear
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="起运国（地区）" name="departure_country">
                  <a-select
                    :getPopupContainer="getPopupContainer"
                    :value="formState.departure_country"
                    @update:value="val => formState.departure_country = val"
                    placeholder="请选择起运国（地区）"
                    :options="options.countries"
                    style="width: 100%"
                    show-search
                    :filter-option="filterOption"
                    :disabled="formState.trade_mode === '1210'"
                    allowClear
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-card>
        </a-form>
      </div>
    </div>

    <div class="steps-action">
      <a-space>
        <a-button
          v-if="currentStep > 0"
          @click="prevStep"
        >
          上一步
        </a-button>
        <a-button
          v-if="currentStep < 2"
          type="primary"
          @click="nextStep"
        >
          下一步
        </a-button>
        <a-button
          v-if="currentStep === 2"
          type="primary"
          @click="handleOk"
        >
          完成
        </a-button>
      </a-space>
    </div>
  </a-modal>
</template>

<script>
import { defineComponent, ref, reactive, watch, onMounted, computed, h } from 'vue';
import { message } from 'ant-design-vue';
import { getFormOptions, createDeclarationTemplate, updateDeclarationTemplate } from '@/api/system/declaration-template';

export default defineComponent({
  name: 'TemplateFormModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    record: {
      type: Object,
      default: null
    }
  },
  emits: ['update:visible', 'success'],
  setup(props, { emit }) {
    const formRef = ref(null);
    const modelVisible = ref(false);
    const options = reactive({
      channels: [],
      tradeModes: [],
      customs: [],
      transportModes: [],
      countries: [],
      platformEnterprises: [],
      ecommerceEnterprises: [],
      paymentEnterprises: [],
      logisticsEnterprises: [],
      bondedEnterprises: [],
      declareEnterprises: [],
      transmissionEnterprises: [],
      logisticsApps: [],
      paymentApps: []
    });

    // 表单状态
    const formState = reactive({
      name: '',
      channel_id: undefined,
      trade_mode: undefined,
      platform_enterprise_id: undefined,
      ecommerce_enterprise_id: undefined,
      logistics_enterprise_id: undefined,
      logistics_app_id: undefined,
      logistics_template_id: undefined,
      bonded_enterprise_id: undefined,
      declare_enterprise_id: undefined,
      transmission_enterprise_id: undefined,
      payment_enterprise_ids: [],
      payment_apps: {},
      declare_customs: undefined,
      port_customs: undefined,
      transport_mode: undefined,
      departure_country: undefined,
      account_book_no: undefined
    });

    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入模板名称' },
        { max: 10, message: '模板名称不能超过10个字' }
      ],
      channel_id: [{ required: true, message: '请选择渠道' }],
      trade_mode: [{ required: true, message: '请选择贸易方式' }],
      platform_enterprise_id: [{ required: true, message: '请选择电商平台' }],
      ecommerce_enterprise_id: [{ required: true, message: '请选择电商企业' }],
      payment_enterprise_ids: [{ required: true, type: 'array', message: '请选择支付企业' }],
      logistics_enterprise_id: [{ required: true, message: '请选择物流企业' }],
      bonded_enterprise_id: [{ 
        required: true, 
        message: '请选择区内企业',
        trigger: 'change',
        validator: (rule, value) => {
          if (formState.trade_mode === '1210' && !value) {
            return Promise.reject('保税进口时必须选择区内企业');
          }
          return Promise.resolve();
        }
      }],
      declare_enterprise_id: [{ required: true, message: '请选择申报企业' }],
      transmission_enterprise_id: [{ required: true, message: '请选择传输企业' }],
      declare_customs: [{ required: true, message: '请选择申报海关' }],
      port_customs: [{ required: true, message: '请选择口岸海关' }],
      transport_mode: [{ required: true, message: '请选择运输方式' }],
      departure_country: [{ 
        required: true, 
        message: '请选择起运国（地区）',
        validator: (rule, value) => {
          if (formState.trade_mode !== '1210' && !value) {
            return Promise.reject('请选择起运国（地区）');
          }
          return Promise.resolve();
        }
      }],
      account_book_no: [{ 
        required: false, 
        message: '请输入账册编号',
        trigger: 'change',
      }]
    };

    const getPopupContainer = (triggerNode) => {
        return triggerNode.parentElement;
    };

    // 获取表单选项数据
    const fetchOptions = async () => {
      try {
        const res = await getFormOptions();
        if (res.code === 200) {
          // 确保数据格式正确
          if (res.data.channels) {
            options.channels = res.data.channels.map(item => ({
              value: parseInt(item.id),  // 确保 channel_id 是整数
              label: item.label || item.name
            }));
          }
          if (res.data.customs) {
            options.customs = res.data.customs.map(item => ({
              value: item.value,
              label: item.label || item.name
            }));
          }
          if (res.data.countries) {
            options.countries = res.data.countries.map(item => ({
              value: item.value,
              label: item.label || item.name
            }));
          }
          if (res.data.transportModes) {
            options.transportModes = res.data.transportModes.map(item => ({
              value: item.value,
              label: item.label || item.name
            }));
          }
          // 处理应用数据，确保包含配置模板
          if (res.data.logisticsApps) {
            options.logisticsApps = res.data.logisticsApps.map(app => ({
              ...app,
              configTemplates: app.config_templates || app.configTemplates || []
            }));
          }
          if (res.data.paymentApps) {
            options.paymentApps = res.data.paymentApps.map(app => ({
              ...app,
              configTemplates: app.config_templates || app.configTemplates || []
            }));
          }
          // 其他选项保持不变
          const { channels, logisticsApps, paymentApps, ...restData } = res.data;
          Object.assign(options, {
            ...restData,
            customs: options.customs,
            countries: options.countries,
            transportModes: options.transportModes,
            logisticsApps: options.logisticsApps,
            paymentApps: options.paymentApps
          });
        }
      } catch (error) {
        console.error('获取表单选项失败:', error);
        message.error('获取表单选项失败');
      }
    };

    // 监听贸易方式变化
    const handleTradeModeChange = (value) => {
      // 保税进口时，自动选择中国为起运国
      if (value === '1210') {
        const china = options.countries.find(item => item.value === 'CHN');
        if (china) {
          formState.departure_country = china.value;
        }
      }
      // 非保税进口时，不修改起运国的值

      // 直购进口时，默认选择邮件运输
      if (value === '9610') {
        formState.transport_mode = '6';
      }
      // 保税进口时，默认选择保税仓库
      else if (value === '1210') {
        formState.transport_mode = '8';
      }
    };

    // 监听电商企业变化
    const handleEcommerceChange = (value) => {
      // 如果选择的电商企业在申报企业列表中存在，则自动选中
      const enterprise = options.ecommerceEnterprises.find(item => item.id === value);
      if (enterprise) {
        const declareEnterprise = options.declareEnterprises.find(item => item.customs_code === enterprise.customs_code);
        if (declareEnterprise) {
          formState.declare_enterprise_id = declareEnterprise.id;
        }

        // 如果在传输企业列表中也存在，则自动选中
        const transmissionEnterprise = options.transmissionEnterprises.find(item => item.customs_code === enterprise.customs_code);
        if (transmissionEnterprise) {
          formState.transmission_enterprise_id = transmissionEnterprise.id;
        }
      }
    };

    // 监听申报企业变化
    const handleDeclareChange = (value) => {
      // 如果选择的申报企业在传输企业列表中存在，则自动选中
      const enterprise = options.declareEnterprises.find(item => item.id === value);
      if (enterprise) {
        const transmissionEnterprise = options.transmissionEnterprises.find(item => item.customs_code === enterprise.customs_code);
        if (transmissionEnterprise) {
          formState.transmission_enterprise_id = transmissionEnterprise.id;
        }
      }
    };

    // 下拉框搜索过滤
    const filterOption = (input, option) => {
      if (!input || !option?.label) {
        return true;
      }
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    // 提交表单
    const handleOk = async () => {
      try {
        // 验证所有字段
        await formRef.value.validate();
        
        // 构建提交数据
        const submitData = {
          ...formState,
          // 如果不是保税进口，将 bonded_enterprise_id 和 account_book_no 设置为 null
          bonded_enterprise_id: formState.trade_mode === '1210' ? formState.bonded_enterprise_id : null,
          account_book_no: formState.trade_mode === '1210' ? formState.account_book_no : null
        };
        // 提交数据
        let res;
        if (props.record) {
          res = await updateDeclarationTemplate(props.record.id, submitData);
        } else {
          res = await createDeclarationTemplate(submitData);
        }

        if (res.code === 200) {
          message.success(`${props.title}成功`);
          emit('success');
          handleCancel();
        } else {
          message.error(res.message || `${props.title}失败`);
        }
      } catch (error) {
        console.error(`${props.title}失败:`, error);
        message.error(error.message || `${props.title}失败`);
      }
    };

    // 监听 visible prop 变化
    watch(() => props.visible, (val) => {
      modelVisible.value = val;
    });

    // 监听 modelVisible 变化
    watch(() => modelVisible.value, (val) => {
      emit('update:visible', val);
    });

    watch(() => props.record, async (newVal) => {
      if (newVal) {
        try {
          // 重置表单
          formRef.value?.resetFields();

          // 确保选项数据已加载
          if (options.countries.length === 0) {
            await fetchOptions();
          }

          // 处理基本字段
          const {
            payment_enterprises,
            platform_enterprise,
            ecommerce_enterprise,
            logistics_enterprise,
            bondedEnterprise,
            declareEnterprise,
            transmission_enterprise,
            ...basicData
          } = newVal;

          // 设置贸易方式
          formState.trade_mode = basicData.trade_mode;

          // 处理支付企业
          if (Array.isArray(payment_enterprises)) {
            formState.payment_enterprise_ids = payment_enterprises.map(item => item.id);
            // 初始化 payment_apps
            payment_enterprises.forEach(enterprise => {
              const { id, pivot } = enterprise;
              if (pivot) {
                formState.payment_apps[id] = {
                  app_id: pivot.app_id,
                  template_id: pivot.app_template_id
                };
              }
            });
          } else {
            formState.payment_enterprise_ids = [];
          }

          // 处理其他字段
          Object.assign(formState, basicData);
        } catch (error) {
          console.error('处理编辑数据失败:', error);
          message.error('处理编辑数据失败');
        }
      } else {
        formRef.value?.resetFields();
      }
    }, { immediate: true });

    // 取消
    const handleCancel = () => {
      formRef.value?.resetFields();
      currentStep.value = 0;
      modelVisible.value = false;
    };

    // 监听编辑记录变化
    watch(() => props.record, async (newVal) => {
      if (newVal) {
        try {
          
          // 先重置表单
          formRef.value?.resetFields();

          // 确保选项数据已加载
          if (options.countries.length === 0) {
            await fetchOptions();
          }
          
          // 处理基本字段
          const {
            payment_enterprises,
            platform_enterprise,
            ecommerce_enterprise,
            logistics_enterprise,
            bondedEnterprise,
            declareEnterprise,
            transmission_enterprise,
            ...basicData
          } = newVal;

          // 先设置贸易方式，因为它会影响其他字段的处理
          formState.trade_mode = basicData.trade_mode;
          // 特殊处理支付企业
          if (Array.isArray(payment_enterprises)) {
            formState.payment_enterprise_ids = payment_enterprises.map(item => item.id);
          } else {
            formState.payment_enterprise_ids = [];
          }

          // 处理起运国
          if (basicData.trade_mode === '1210') {
            // 保税进口时，自动选择中国为起运国
            const china = options.countries.find(item => item.value === 'CHN');
            if (china) {
              formState.departure_country = china.value;
            }
          } else {
            // 非保税进口时，使用原始值
            formState.departure_country = basicData.departure_country;
          }

          // 赋值其他基本数据
          Object.assign(formState, basicData);

        } catch (error) {
          console.error('处理编辑数据失败:', error);
          message.error('处理编辑数据失败');
        }
      } else {
        formRef.value?.resetFields();
      }
    }, { immediate: true });

    // 当前步骤
    const currentStep = ref(0);

    // 格式化企业选项
    const formatEnterpriseOptions = (enterprises) => {
      return enterprises?.map(item => ({
        label: item.customs_name,
        value: item.id
      })) || [];
    };

    // 步骤控制
    const nextStep = async () => {
      try {
        // 根据当前步骤验证相应的字段
        if (currentStep.value === 0) {
          await formRef.value.validateFields(['name', 'channel_id', 'trade_mode', 'account_book_no']);
        } else if (currentStep.value === 1) {
          await formRef.value.validateFields([
            'platform_enterprise_id',
            'ecommerce_enterprise_id',
            'payment_enterprise_ids',
            'logistics_enterprise_id',
            'bonded_enterprise_id',
            'declare_enterprise_id',
            'transmission_enterprise_id'
          ]);
        }
        currentStep.value++;
      } catch (error) {
        // 验证失败时不进行跳转
        console.error('表单验证失败:', error);
      }
    };

    const prevStep = () => {
      currentStep.value--;
    };

    // 计算属性：当前选中的物流应用
    const selectedLogisticsApp = computed(() => {
      if (!formState.logistics_app_id || !options.logisticsApps?.length) return null;
      const app = options.logisticsApps.find(app => app.id === formState.logistics_app_id);
      // 确保使用 config_templates 或 configTemplates
      if (app) {
        app.configTemplates = app.config_templates || app.configTemplates || [];
      }
      return app;
    });

    // 格式化应用选项
    const formatAppOptions = (apps) => {
      return apps?.map(app => ({
        label: app.name,
        value: app.id
      })) || [];
    };

    // 格式化配置模板选项
    const formatTemplateOptions = (templates) => {
      if (!templates?.length) {
        return [];
      }
      return templates.map(template => ({
        label: template.name,
        value: template.id
      }));
    };

    // 获取企业名称
    const getEnterpriseName = (enterpriseId, enterprises) => {
      const enterprise = enterprises?.find(e => e.id === enterpriseId);
      return enterprise ? enterprise.customs_name : '';
    };

    // 获取支付应用的配置模板
    const getPaymentAppTemplates = (enterpriseId) => {
      const appId = formState.payment_apps[enterpriseId]?.app_id;
      if (!appId || !options.paymentApps?.length) return [];
      const app = options.paymentApps.find(a => a.id === appId);// 添加日志
      return app?.configTemplates || [];
    };

    // 处理物流应用变化
    const handleLogisticsAppChange = (value) => {
      formState.logistics_template_id = undefined;
    };

    // 处理支付应用变化
    const handlePaymentAppChange = (value, enterpriseId) => {
      if (!formState.payment_apps[enterpriseId]) {
        formState.payment_apps[enterpriseId] = {
          app_id: value,
          template_id: undefined
        };
      } else {
        formState.payment_apps[enterpriseId].app_id = value;
        formState.payment_apps[enterpriseId].template_id = undefined;
      }
    };

    // 处理支付应用配置模板变化
    const handlePaymentTemplateChange = (value, enterpriseId) => {
      if (!formState.payment_apps[enterpriseId]) {
        formState.payment_apps[enterpriseId] = {
          app_id: undefined,
          template_id: value
        };
      } else {
        formState.payment_apps[enterpriseId].template_id = value;
      }
    };

    // 处理支付企业变化
    const handlePaymentEnterpriseChange = (values) => {
      // 移除不再选中的企业
      Object.keys(formState.payment_apps).forEach(enterpriseId => {
        if (!values.includes(Number(enterpriseId))) {
          delete formState.payment_apps[enterpriseId];
        }
      });

      // 为新选中的企业初始化数据
      values.forEach(enterpriseId => {
        if (!formState.payment_apps[enterpriseId]) {
          formState.payment_apps[enterpriseId] = {
            app_id: undefined,
            template_id: undefined
          };
        }
      });
    };

    // 添加渲染函数
    const renderFormItem = (field) => {
      return h('div', {}, [
        h('a-form-item', {
          label: field.label,
          name: ['settings', field.field],
          rules: [{ required: field.required, message: '请输入' + field.label }],
          extra: field.description
        }, [
          // 根据字段类型渲染不同的输入组件
          field.type === 'text' && h('a-input', {
            value: formState.settings[field.field],
            'onUpdate:value': val => formState.settings[field.field] = val,
            placeholder: field.placeholder
          }),
          field.type === 'password' && h('a-input-password', {
            value: formState.settings[field.field],
            'onUpdate:value': val => formState.settings[field.field] = val,
            placeholder: field.placeholder
          }),
          field.type === 'switch' && h('a-switch', {
            checked: formState.settings[field.field],
            'onUpdate:checked': val => formState.settings[field.field] = val
          }),
          field.type === 'select' && h('a-select', {
            value: formState.settings[field.field],
            'onUpdate:value': val => formState.settings[field.field] = val,
            options: field.options,
            placeholder: field.placeholder,
            getPopupContainer: getPopupContainer
          }),
          field.type === 'checkbox' && h('a-checkbox-group', {
            value: formState.settings[field.field],
            'onUpdate:value': val => formState.settings[field.field] = val,
            options: field.options
          })
        ])
      ]);
    };

    onMounted(() => {
      fetchOptions();
    });

    const setupResult = {
      formRef,
      formState,
      rules,
      options,
      modelVisible,
      getPopupContainer,
      filterOption,
      handleTradeModeChange,
      handleEcommerceChange,
      handleDeclareChange,
      handleOk,
      handleCancel,
      currentStep,
      formatEnterpriseOptions,
      nextStep,
      prevStep,
      selectedLogisticsApp,
      formatAppOptions,
      formatTemplateOptions,
      getEnterpriseName,
      getPaymentAppTemplates,
      handlePaymentEnterpriseChange,
      handlePaymentAppChange,
      handlePaymentTemplateChange,
      handleLogisticsAppChange,
      renderFormItem
    };

    return setupResult;
  }
});
</script>

<style lang="less" scoped>
.steps-wrapper {
  margin-bottom: 24px;
}

.steps-content {
  min-height: 400px;
  padding: 24px;
  background-color: #fafafa;
  border-radius: 2px;
}

.steps-action {
  margin-top: 24px;
  text-align: center;
}

.custom-card {
  margin-bottom: 16px;
  
  :deep(.ant-card-head) {
    min-height: 48px;
    padding: 0 12px;
    
    .ant-card-head-title {
      padding: 8px 0;
    }
  }
  
  :deep(.ant-card-body) {
    padding: 16px;
  }
}

:deep(.ant-form-item) {
  margin-bottom: 16px;
}
</style> 