<template>
  <system-page title="企业管理">
    <div class="enterprise-container">

      <a-card :bordered="false">
        <!-- 搜索表单 -->
        <a-form layout="inline" class="table-search-form">
          <a-row :gutter="[8,8]">
            <a-col :span="8">
              <a-form-item label="企业名称">
                <a-input v-model:value="searchForm.customs_name" placeholder="请输入海关备案名称" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-form-item label="企业编码">
                <a-input v-model="searchForm.customs_code" placeholder="请输入海关备案编码" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-form-item label="企业类型">
                <a-select
                  v-model="searchForm.type_id"
                  placeholder="请选择企业类型"
                  allowClear
                  style="width: 100%"
                >
                  <a-select-option
                    v-for="item in typeOptions"
                    :key="item.id"
                    :value="item.id"
                  >
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="2">
              <a-space>
                <a-button type="primary" @click="handleSearch">
                  <template #icon><search-outlined /></template>
                  查询
                </a-button>
                <a-button @click="handleReset">
                  <template #icon><reload-outlined /></template>
                  重置
                </a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-form>

        <!-- 操作按钮 -->
        <div class="table-operations">
          <a-button type="primary" @click="showAddModal">
            <template #icon><plus-outlined /></template>
            新增企业
          </a-button>
        </div>

        <!-- 企业列表 -->
        <a-table
          :columns="columns"
          :data-source="enterpriseList"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          :row-key="record => record.id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'types'">
              <a-space>
                <a-tag v-for="type in record.types" :key="type.id">
                  {{ type.type.label }}
                  <template v-if="type.dxp_id">
                    ({{ type.dxp_id }})
                  </template>
                </a-tag>
              </a-space>
            </template>
            <template v-else-if="column.key === 'created_at'">
              {{ formatDate(record.created_at) }}
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" @click="handleEdit(record)">
                  <template #icon><edit-outlined /></template>
                  编辑
                </a-button>
                <a-popconfirm
                  title="确定要删除这个企业吗？"
                  @confirm="handleDelete(record)"
                >
                  <a-button type="link" danger>
                    <template #icon><delete-outlined /></template>
                    删除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>

        <!-- 新增/编辑企业弹窗 -->
        <a-modal
          :title="modalTitle"
          v-model:open="modalVisible"
          :maskClosable="false"
          @ok="handleModalOk"
          @cancel="handleModalCancel"
          width="720px"
        >
          <a-form
            ref="formRef"
            :model="formState"
            :rules="rules"
            :label-col="{ span: 6 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-form-item label="海关备案名称" name="customs_name">
              <a-input
                v-model:value="formState.customs_name"
                placeholder="请输入海关备案名称"
                :maxLength="100"
                showCount
                allowClear
              />
            </a-form-item>

            <a-form-item label="海关备案编码" name="customs_code">
              <a-input
                v-model:value="formState.customs_code"
                placeholder="请输入海关备案编码"
                :maxLength="18"
                showCount
                allowClear
              />
            </a-form-item>

            <a-form-item label="企业类型" name="types" required>
              <a-checkbox-group v-model:value="selectedTypes">
                <div class="type-checkbox-list">
                  <div v-for="type in typeOptions" :key="type.id" class="type-checkbox-item">
                    <a-checkbox :value="type.id">{{ type.label }}</a-checkbox>
                    <a-input
                      v-if="isTransmissionType(type.id) && selectedTypes.includes(type.id)"
                      v-model:value="dxpIds[type.id]"
                      placeholder="请输入报文传输编号(DXPID)"
                      :maxLength="30"
                      style="width: 200px; margin-left: 8px;"
                    />
                  </div>
                </div>
              </a-checkbox-group>
            </a-form-item>
          </a-form>
        </a-modal>
      </a-card>
    </div>
  </system-page>
</template>

<script>
import SystemPage from '../components/SystemPage.vue';
import { defineComponent, ref, reactive, onMounted, watch } from 'vue';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { getEnterprises, createEnterprise, updateEnterprise, deleteEnterprise } from '@/api/system/enterprise';
import { getDictItems } from '@/api/system/dict';
import { formatDate } from '@/utils/utils';

export default defineComponent({
  name: 'EnterpriseList',
  components: {
    PlusOutlined,
    SearchOutlined,
    ReloadOutlined,
    EditOutlined,
    DeleteOutlined,
    SystemPage
  },
  setup() {
    const loading = ref(false);
    const enterpriseList = ref([]);
    const typeOptions = ref([]);
    const modalVisible = ref(false);
    const modalTitle = ref('新增企业');
    const formRef = ref(null);
    const selectedTypes = ref([]);
    const dxpIds = ref({});

    // 表格列定义
    const columns = [
      {
        title: '海关备案名称',
        dataIndex: 'customs_name',
        key: 'customs_name',
        width: 200
      },
      {
        title: '海关备案编码',
        dataIndex: 'customs_code',
        key: 'customs_code',
        width: 150
      },
      {
        title: '企业类型',
        dataIndex: 'types',
        key: 'types',
        width: 300
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 180
      },
      {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 150
      }
    ];

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showTotal: total => `共 ${total} 条`,
      showSizeChanger: true,
      showQuickJumper: true
    });

    // 搜索表单
    const searchForm = reactive({
      customs_name: '',
      customs_code: '',
      type_id: undefined
    });

    // 表单状态
    const formState = reactive({
      id: null,
      customs_name: '',
      customs_code: '',
      types: []
    });

    // 监听 selectedTypes 变化，同步到 formState.types
    watch(selectedTypes, (newVal) => {
      formState.types = newVal;
    });

    // 表单验证规则
    const rules = {
      customs_name: [
        { required: true, message: '请输入海关备案名称' },
        { max: 100, message: '长度不能超过100个字符' }
      ],
      customs_code: [
        { required: true, message: '请输入海关备案编码' },
        { max: 18, message: '长度不能超过18个字符' }
      ],
      types: [
        { required: true, type: 'array', min: 1, message: '请选择企业类型' }
      ]
    };

    // 判断是否为传输企业类型
    const isTransmissionType = (typeId) => {
      const type = typeOptions.value.find(t => t.id === typeId);
      return type && type.value === 'transmission';
    };

    // 获取企业类型选项
    const fetchTypeOptions = async () => {
      try {
        const res = await getDictItems(null, {
          code: 'responsible_party',
          pageSize: 1000
        });
        if (res.code === 200 && res.data?.list) {
          typeOptions.value = res.data.list;
        }
      } catch (error) {
        console.error('获取企业类型选项失败:', error);
        message.error('获取企业类型选项失败');
      }
    };

    // 获取企业列表
    const fetchEnterprises = async () => {
      loading.value = true;
      try {
        const res = await getEnterprises({
          page: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm
        });
        if (res.code === 200) {
          enterpriseList.value = res.data.list;
          pagination.total = res.data.total;
        }
      } catch (error) {
        console.error('获取企业列表失败:', error);
        message.error('获取企业列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 搜索
    const handleSearch = () => {
      pagination.current = 1;
      fetchEnterprises();
    };

    // 重置搜索
    const handleReset = () => {
      searchForm.customs_name = '';
      searchForm.customs_code = '';
      searchForm.type_id = undefined;
      handleSearch();
    };

    // 表格变化
    const handleTableChange = (pag) => {
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;
      fetchEnterprises();
    };

    // 显示新增弹窗
    const showAddModal = () => {
      modalTitle.value = '新增企业';
      formState.id = null;
      formState.customs_name = '';
      formState.customs_code = '';
      formState.types = [];
      selectedTypes.value = [];
      dxpIds.value = {};
      modalVisible.value = true;
    };

    // 显示编辑弹窗
    const handleEdit = (record) => {
      modalTitle.value = '编辑企业';
      const types = record.types.map(t => t.type_id);
      Object.assign(formState, {
        id: record.id,
        customs_name: record.customs_name,
        customs_code: record.customs_code,
        types: types
      });
      selectedTypes.value = types;
      dxpIds.value = {};
      record.types.forEach(t => {
        if (t.dxp_id) {
          dxpIds.value[t.type_id] = t.dxp_id;
        }
      });
      modalVisible.value = true;
    };

    // 删除企业
    const handleDelete = async (record) => {
      try {
        const res = await deleteEnterprise(record.id);
        if (res.code === 200) {
          message.success('删除成功');
          if (enterpriseList.value.length === 1 && pagination.current > 1) {
            pagination.current -= 1;
          }
          fetchEnterprises();
        }
      } catch (error) {
        console.error('删除企业失败:', error);
        message.error('删除企业失败');
      }
    };

    // 提交表单
    const handleModalOk = () => {
      formRef.value.validate().then(async () => {
        try {
          // 检查是否选择了企业类型
          if (!selectedTypes.value || selectedTypes.value.length === 0) {
            message.error('请至少选择一个企业类型');
            return;
          }

          // 构建类型数据
          const types = selectedTypes.value.map(typeId => {
            const typeData = {
              id: typeId
            };
            
            // 如果是传输企业类型且有DXPID，则添加
            if (isTransmissionType(typeId) && dxpIds.value[typeId]) {
              typeData.dxp_id = dxpIds.value[typeId];
            }
            
            return typeData;
          });

          const data = {
            customs_name: formState.customs_name.trim(),
            customs_code: formState.customs_code.trim(),
            types: types
          };

          console.log('提交的数据:', data);

          let res;
          if (formState.id) {
            res = await updateEnterprise(formState.id, data);
          } else {
            res = await createEnterprise(data);
          }

          if (res.code === 200) {
            message.success(`${modalTitle.value}成功`);
            modalVisible.value = false;
            // 重新获取列表数据
            await fetchEnterprises();
          } else {
            message.error(res.message || `${modalTitle.value}失败`);
          }
        } catch (error) {
          console.error(`${modalTitle.value}失败:`, error);
          message.error(error.message || `${modalTitle.value}失败，请检查数据是否正确`);
        }
      }).catch(errors => {
        console.error('表单验证错误:', errors);
        message.error('请检查表单填写是否正确');
      });
    };

    // 取消表单
    const handleModalCancel = () => {
      formRef.value?.resetFields();
      modalVisible.value = false;
    };

    onMounted(() => {
      fetchTypeOptions();
      fetchEnterprises();
    });

    return {
      loading,
      enterpriseList,
      columns,
      pagination,
      searchForm,
      modalVisible,
      modalTitle,
      formRef,
      formState,
      rules,
      typeOptions,
      selectedTypes,
      dxpIds,
      handleSearch,
      handleReset,
      handleTableChange,
      showAddModal,
      handleEdit,
      handleDelete,
      handleModalOk,
      handleModalCancel,
      formatDate,
      isTransmissionType
    };
  }
});
</script>

<style lang="less" scoped>
.enterprise-container {
  padding: 24px;

  .page-header {
    margin-bottom: 24px;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
      color: #272343;
    }

    .subtitle {
      margin-top: 8px;
      color: #666;
      font-size: 14px;
    }
  }

  .table-operations {
    margin-bottom: 16px;
  }

  .table-search-form {
    margin-bottom: 24px;

    :deep(.ant-form-item) {
      margin-bottom: 16px;
    }
  }

  .type-checkbox-list {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .type-checkbox-item {
      display: flex;
      align-items: center;
    }
  }
}
</style> 