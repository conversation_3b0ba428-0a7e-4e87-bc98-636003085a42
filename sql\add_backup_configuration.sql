-- 为数据库备份功能添加配置表
-- 执行时间：2025-07-04

-- 创建备份配置表
CREATE TABLE IF NOT EXISTS `gaodux_backup_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '配置名称',
  `type` enum('database','file') NOT NULL DEFAULT 'database' COMMENT '备份类型：database-数据库，file-文件',
  `enabled` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用：0-否，1-是',
  `schedule_cron` varchar(50) DEFAULT '0 2 * * *' COMMENT '备份计划CRON表达式',
  `retention_days` int(11) NOT NULL DEFAULT 30 COMMENT '备份保留天数',
  `email_notification` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否发送邮件通知：0-否，1-是',
  `email_recipients` text COMMENT '邮件接收人列表，多个邮箱用逗号分隔',
  `backup_path` varchar(255) DEFAULT NULL COMMENT '备份存储路径',
  `compression` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否压缩：0-否，1-是',
  `settings` json DEFAULT NULL COMMENT '其他配置选项',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type_enabled` (`type`, `enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='备份配置表';

-- 插入默认的数据库备份配置
INSERT INTO `gaodux_backup_config` (`name`, `type`, `enabled`, `schedule_cron`, `retention_days`, `email_notification`, `email_recipients`, `backup_path`, `compression`, `settings`) VALUES
('默认数据库备份', 'database', 1, '0 2 * * *', 30, 0, NULL, NULL, 1, JSON_OBJECT(
    'include_routines', true,
    'include_triggers', true,
    'single_transaction', true,
    'compress_level', 9
));

-- 创建备份历史记录表
CREATE TABLE IF NOT EXISTS `gaodux_backup_history` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `config_id` int(11) NOT NULL COMMENT '备份配置ID',
  `backup_name` varchar(255) NOT NULL COMMENT '备份名称',
  `file_path` varchar(500) NOT NULL COMMENT '备份文件路径',
  `file_size` bigint(20) NOT NULL DEFAULT 0 COMMENT '文件大小（字节）',
  `backup_type` enum('database','file') NOT NULL COMMENT '备份类型',
  `status` enum('running','success','failed') NOT NULL DEFAULT 'running' COMMENT '备份状态',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `duration` int(11) DEFAULT NULL COMMENT '耗时（秒）',
  `error_message` text COMMENT '错误信息',
  `email_sent` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已发送邮件：0-否，1-是',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_status_created` (`status`, `created_at`),
  KEY `idx_backup_type` (`backup_type`),
  CONSTRAINT `fk_backup_history_config` FOREIGN KEY (`config_id`) REFERENCES `gaodux_backup_config` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='备份历史记录表';

-- 为现有的定时任务表添加备份相关字段（如果不存在）
ALTER TABLE `gaodux_schedule` 
ADD COLUMN IF NOT EXISTS `backup_config_id` int(11) DEFAULT NULL COMMENT '关联的备份配置ID' AFTER `email_on_failure`,
ADD KEY IF NOT EXISTS `idx_backup_config` (`backup_config_id`);

-- 添加外键约束（如果不存在）
-- ALTER TABLE `gaodux_schedule` 
-- ADD CONSTRAINT `fk_schedule_backup_config` FOREIGN KEY (`backup_config_id`) REFERENCES `gaodux_backup_config` (`id`) ON DELETE SET NULL;
