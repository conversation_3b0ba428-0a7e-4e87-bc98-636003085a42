/**
 * 常用工具函数集合
 */

/**
 * 判断是否为对象
 * @param {any} obj 
 * @returns {boolean}
 */
export function isObject(obj) {
  return obj !== null && typeof obj === 'object' && !Array.isArray(obj)
}

/**
 * 深拷贝对象
 * @template T
 * @param {T} obj 
 * @returns {T}
 */
export function deepClone(obj) {
  if (!isObject(obj)) return obj
  if (obj instanceof Date) return new Date(obj)
  if (obj instanceof RegExp) return new RegExp(obj)
  
  const clone = Array.isArray(obj) ? [] : {}
  for (const key in obj) {
    if (Object.hasOwnProperty.call(obj, key)) {
      clone[key] = deepClone(obj[key])
    }
  }
  return clone
}

/**
 * 日期格式化
 * @param {Date|string|number} date 
 * @param {string} format 
 * @returns {string}
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''
  const d = new Date(date)
  const pad = (n) => String(n).padStart(2, '0')
  
  return format
    .replace(/YYYY/g, d.getFullYear())
    .replace(/MM/g, pad(d.getMonth() + 1))
    .replace(/DD/g, pad(d.getDate()))
    .replace(/HH/g, pad(d.getHours()))
    .replace(/mm/g, pad(d.getMinutes()))
    .replace(/ss/g, pad(d.getSeconds()))
}

/**
 * 合并对象
 * @param {Object} target 
 * @param {...Object} sources 
 * @returns {Object}
 */
export function merge(target, ...sources) {
  if (!isObject(target)) return target
  
  sources.forEach(source => {
    if (isObject(source)) {
      Object.keys(source).forEach(key => {
        if (isObject(source[key])) {
          if (!target[key]) Object.assign(target, { [key]: {} })
          merge(target[key], source[key])
        } else {
          Object.assign(target, { [key]: source[key] })
        }
      })
    }
  })
  
  return target
}

/**
 * 判断是否有权限
 * @param {string[]} permissions 用户权限列表
 * @param {string} permission 需要校验的权限
 * @returns {boolean}
 */
export function hasPermission(permissions, permission) {
  return permissions.includes(permission)
}

/**
 * 生成随机字符串
 * @param {number} length 
 * @returns {string}
 */
export function randomString(length = 8) {
  const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
  const maxPos = chars.length
  let str = ''
  for (let i = 0; i < length; i++) {
    str += chars.charAt(Math.floor(Math.random() * maxPos))
  }
  return str
}

/**
 * 防抖函数
 * @param {Function} fn 
 * @param {number} delay 
 * @returns {Function}
 */
export function debounce(fn, delay = 300) {
  let timer = null
  return function(...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

/**
 * 节流函数
 * @param {Function} fn 
 * @param {number} delay 
 * @returns {Function}
 */
export function throttle(fn, delay = 300) {
  let lastTime = 0
  return function(...args) {
    const now = Date.now()
    if (now - lastTime > delay) {
      fn.apply(this, args)
      lastTime = now
    }
  }
}
