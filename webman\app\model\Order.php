<?php
namespace app\model;

use support\Model;
use support\exception\BusinessException;
use Illuminate\Support\Facades\Validator;
use Illuminate\Translation\ArrayLoader;
use Illuminate\Translation\Translator;
use Illuminate\Validation\Factory;
use Illuminate\Support\Facades\DB;

class Order extends Model
{
    protected $table = 'orders';
    
    protected $fillable = [
        'order_no',
        'template_id',
        'channel_id',
        'order_time',
        'goods_amount',
        'freight_amount',
        'discount_amount',
        'tax_amount',
        'pay_amount',
        'payment_method',
        'transaction_id',
        'order_status',
        'express_name',
        'express_no',
        'shipping_time',
        'shipping_push_status',
        'shipping_push_error',
        'customs_status',
        'payment_push_status',
        'logistics_push_status',
        'manifest_push_status',
        'note_manifest_push_status',
        'is_test',
        'is_spot_check',
        'sessionID',
        'serviceTime',
        'created_at',
        'updated_at'
    ];

    // 订单状态常量
    const STATUS_PAID = 10;
    const STATUS_SHIPPED = 15;
    const STATUS_COMPLETED = 20;
    const STATUS_CANCELLED = 5;

    // 推送状态常量
    const PUSH_STATUS_UNPUSHED = 'unpushed';
    const PUSH_STATUS_PUSHED = 'pushed';
    const PUSH_STATUS_SUCCESS = 'success';
    const PUSH_STATUS_FAILED = 'failed';
    const PUSH_STATUS_UNNECESSARY = 'unnecessary';

    /**
     * 关联申报模板
     */
    public function template()
    {
        return $this->belongsTo(DeclarationTemplate::class, 'template_id');
    }

    /**
     * 关联渠道
     */
    public function channel()
    {
        return $this->belongsTo(DictionaryItem::class, 'channel_id');
    }

    /**
     * 关联订单商品
     */
    public function items()
    {
        return $this->hasMany(OrderItem::class, 'order_id');
    }

    /**
     * 关联订单地址
     */
    public function address()
    {
        return $this->hasOne(OrderAddress::class, 'order_id');
    }

    /**
     * 关联跨境申报信息
     */
    public function declaration()
    {
        return $this->hasOne(OrderCrossBorderDeclaration::class, 'order_id');
    }

    /**
     * 关联报文信息
     */
    public function messages()
    {
        return $this->hasMany(OrderMessage::class, 'order_id');
    }

    /**
     * 生成订单号
     */
    public static function generateOrderNo()
    {
        $prefix = date('ymd');
        $suffix = substr(microtime(), 2, 6) . mt_rand(100, 999);
        return $prefix . $suffix;
    }

    /**
     * 获取订单状态文本
     */
    public function getStatusText()
    {
        $statusMap = [
            self::STATUS_PAID => '已支付',
            self::STATUS_SHIPPED => '已发货',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_CANCELLED => '已取消'
        ];
        return $statusMap[$this->order_status] ?? '未知状态';
    }

    /**
     * 获取海关状态文本
     */
    public function getCustomsStatusText()
    {
        $statusMap = [
            2 => '电子口岸申报中',
            3 => '发送海关成功',
            4 => '发送海关失败',
            100 => '海关退单',
            120 => '海关入库'
        ];
        return $statusMap[$this->customs_status] ?? '未知状态';
    }

    /**
     * 获取清单状态文本
     */
    public function getManifestStatusText()
    {
        $statusMap = [
            1 => '电子口岸已暂存',
            2 => '电子口岸申报中',
            3 => '发送海关成功',
            4 => '发送海关失败',
            100 => '海关退单',
            120 => '海关入库',
            300 => '人工审核',
            399 => '海关审结',
            800 => '放行',
            899 => '结关',
            500 => '查验',
            501 => '扣留移送通关',
            502 => '扣留移送缉私',
            503 => '扣留移送法规',
            599 => '其它扣留',
            700 => '退运'
        ];
        return $statusMap[$this->manifest_push_status] ?? '未知状态';
    }

    public static function validate($data)
    {
        // 创建验证器工厂实例
        $translator = new Translator(new ArrayLoader(), 'zh_CN');
        $validation = new Factory($translator);

        $validator = $validation->make($data, [
            'order_no' => 'required|string|max:64',
            'channel_id' => 'required|integer',
            'template_id' => 'required|integer',
            'order_time' => 'required|date',
            'payment_method' => 'required|in:alipay,wechat',
            'transaction_id' => 'required|string|max:64',
            'goods_amount' => 'required|numeric|min:0',
            'freight_amount' => 'required|numeric|min:0',
            'discount_amount' => 'required|numeric|min:0',
            'tax_amount' => 'required|numeric|min:0',
            'pay_amount' => 'required|numeric|min:0',
        ], [
            'order_no.required' => ['code' => 10003, 'message' => '订单号不能为空'],
            'order_no.max' => ['code' => 10003, 'message' => '订单号长度不能超过64个字符'],
            'channel_id.required' => ['code' => 10001, 'message' => '渠道ID不能为空'],
            'channel_id.integer' => ['code' => 10001, 'message' => '渠道ID必须是数字'],
            'template_id.required' => ['code' => 10001, 'message' => '模板ID不能为空'],
            'template_id.integer' => ['code' => 10001, 'message' => '模板ID必须是数字'],
            'order_time.required' => ['code' => 10004, 'message' => '下单时间不能为空'],
            'order_time.date' => ['code' => 10004, 'message' => '下单时间格式不正确'],
            'payment_method.required' => ['code' => 10005, 'message' => '支付方式不能为空'],
            'payment_method.in' => ['code' => 10005, 'message' => '支付方式只能是支付宝或微信'],
            'transaction_id.required' => ['code' => 10006, 'message' => '支付流水号不能为空'],
            'transaction_id.max' => ['code' => 10006, 'message' => '支付流水号长度不能超过64个字符'],
            'goods_amount.required' => ['code' => 10012, 'message' => '商品金额不能为空'],
            'goods_amount.min' => ['code' => 10012, 'message' => '商品金额不能小于0'],
            'freight_amount.required' => ['code' => 10012, 'message' => '运费不能为空'],
            'freight_amount.min' => ['code' => 10012, 'message' => '运费不能小于0'],
            'discount_amount.required' => ['code' => 10012, 'message' => '优惠金额不能为空'],
            'discount_amount.min' => ['code' => 10012, 'message' => '优惠金额不能小于0'],
            'tax_amount.required' => ['code' => 10012, 'message' => '税费不能为空'],
            'tax_amount.min' => ['code' => 10012, 'message' => '税费不能小于0'],
            'pay_amount.required' => ['code' => 10012, 'message' => '支付金额不能为空'],
            'pay_amount.min' => ['code' => 10012, 'message' => '支付金额不能小于0'],
        ]);

        if ($validator->fails()) {
            $error = $validator->errors()->first();
            throw new BusinessException($error['code'], $error['message']);
        }

        // 检查订单号是否已存在（基于渠道ID和订单号的组合）
        $exists = self::where('channel_id', $data['channel_id'])
            ->where('order_no', $data['order_no'])
            ->exists();
        
        if ($exists) {
            throw new BusinessException(10009, '该渠道下的订单号已存在');
        }

        return true;
    }
} 