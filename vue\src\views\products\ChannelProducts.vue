<template>
    <div class="registered-products">
        <!-- 页面头部 -->
        <div class="page-header">
            <a-space>
                <a-button @click="goBack">
                    <template #icon><left-outlined /></template>
                    返回
                </a-button>
                <a-divider type="vertical" />
                <h2>渠道商品</h2>
            </a-space>
        </div>

        <a-layout>
            <!-- 左侧渠道列表 -->
            <a-layout-sider width="220" theme="light" class="channel-sider">
                <a-card title="渠道列表" :bordered="false">
                    <a-spin :spinning="channelLoading">
                        <a-list :data-source="channelOptions" :row-key="item => item.value" :pagination="false">
                            <template #renderItem="{ item }">
                                <a-list-item @click="handleChannelSelect(item.value)"
                                    :class="{ 'selected-channel': selectedChannel === item.value }">
                                    {{ item.label }}
                                    <span class="product-count">({{ channelProductCount[item.value] || 0 }})</span>
                                </a-list-item>
                            </template>
                        </a-list>
                    </a-spin>
                </a-card>
            </a-layout-sider>

            <!-- 右侧内容 -->
            <a-layout-content>
                <a-card :bordered="false">
                    <a-form layout="inline" class="table-search-form">
                        <a-row :gutter="24">
                            <a-col :span="8">
                                <a-form-item>
                                    <a-input v-model="searchForm.channel_product_name" placeholder="请输入渠道商品名称" allowClear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="8">
                                <a-form-item >
                                    <a-input v-model="searchForm.channel_product_no" placeholder="请输入渠道商品编码" allowClear />
                                </a-form-item>
                            </a-col>
                            <a-col :span="6">
                                <a-form-item style="width: 100%;">
                                    <a-space>
                                        <a-button type="primary" @click="handleSearch">
                                            <template #icon><search-outlined /></template>
                                            查询
                                        </a-button>
                                        <a-button @click="handleReset">
                                            <template #icon><reload-outlined /></template>
                                            重置
                                        </a-button>
                                    </a-space>
                                </a-form-item>
                            </a-col>
                        </a-row>
                    </a-form>

                    <!-- 操作按钮 -->
                    <div class="table-operations">
                        <a-button type="primary" @click="showAddModal">
                            <template #icon><plus-outlined /></template>
                            添加商品
                        </a-button>
                    </div>

                    <!-- 商品列表表格 -->
                    <a-table :columns="columns" :data-source="productList" :loading="loading" :pagination="pagination"
                        @change="handleTableChange" :scroll="{ x: 1200 }" size="middle" :row-key="record => record.id">
                        <template #bodyCell="{ column, record }">
                            <template v-if="column.key === 'created_at'">
                                {{ formatDate(record.created_at) }}
                            </template>

                            <template v-if="column.key === 'product'">
                                <a-popover placement="bottom">
                                    <template #content>
                                        <a-descriptions :column="2" size="small" bordered>
                                            <a-descriptions-item label="商品类型">
                                                <a-tag :color="record.product.goods_type === 1 ? 'blue' : 'green'">
                                                    {{ record.product.goods_type === 1 ? '直邮' : '保税仓' }}
                                                </a-tag>
                                            </a-descriptions-item>
                                            <a-descriptions-item label="商品编码">{{ record.product.goods_no }}</a-descriptions-item>
                                            <a-descriptions-item label="商品条码">{{ record.product.bar_code }}</a-descriptions-item>
                                            <a-descriptions-item label="HS编码">{{ record.product.hs_code }}</a-descriptions-item>
                                            <a-descriptions-item label="商品税率">{{ record.product.goods_tax }}%</a-descriptions-item>
                                            <a-descriptions-item label="商品价格">¥{{ record.product.price }}</a-descriptions-item>
                                            <a-descriptions-item label="净重">{{ record.product.net_weight }}KG</a-descriptions-item>
                                            <a-descriptions-item label="毛重">{{ record.product.gross_weight }}KG</a-descriptions-item>
                                            <a-descriptions-item label="规格型号" :span="2">{{ record.product.goods_model }}</a-descriptions-item>
                                            <a-descriptions-item label="创建时间" :span="2">{{ formatDate(record.product.created_at) }}</a-descriptions-item>
                                        </a-descriptions>
                                    </template>
                                    <template #title>
                                        <span>备案商品详情</span>
                                    </template>
                                    <a-tag class="clickable" :color="record.product.status === 1 ? 'blue' : 'orange'">
                                        {{ record.product.goods_name }}
                                        <template v-if="record.product.status !== 1">
                                            (已下架)
                                        </template>
                                    </a-tag>
                                </a-popover>
                            </template>

                            <template v-else-if="column.key === 'action'">
                                <a-space>
                                    <a-button type="link" @click="handleEdit(record)" v-permission="'update'">
                                        <template #icon><EditOutlined /></template>
                                        编辑
                                    </a-button>
                                    <a-popconfirm title="确定要删除这个商品吗？" @confirm="handleDelete(record)">
                                        <a-button v-permission="'delete'" type="link" danger>
                                            <DeleteOutlined />
                                            删除
                                        </a-button>
                                    </a-popconfirm>
                                </a-space>
                            </template>
                        </template>
                    </a-table>
                </a-card>

            </a-layout-content>
        </a-layout>

        <!-- 添加/编辑商品模态框 -->
        <a-modal :title="modalTitle" v-model:open="modalVisible" @ok="handleModalOk" @cancel="handleModalCancel"
            :maskClosable="false" width="720px">
            <a-form ref="formRef" :model="formState" :rules="rules" :label-col="{ span: 6 }"
                :wrapper-col="{ span: 16 }">
                
                <a-form-item label="备案商品" name="product_id">
                    <a-select
                        :getPopupContainer="getPopupContainer"
                        v-model:value="formState.product_id"
                        placeholder="请输入已备案商品的名称/条码/编码搜索"
                        :loading="productLoading"
                        show-search
                        :filter-option="false"
                        :not-found-content="productLoading ? '加载中...' : '暂无数据'"
                        @search="handleProductSearch"
                        @change="handleProductSelect"
                        style="width: 100%"
                    >
                        <a-select-option v-for="option in productOptions" :key="option.value" :value="option.value">
                            {{ option.label }}
                            <span style="float: right; color: #999;">{{ option.item.goods_no }}</span>
                        </a-select-option>
                    </a-select>
                </a-form-item>

                <!-- 商品信息展示 -->
                <template v-if="selectedProduct">
                    <div class="product-info" style="padding: 0 60px;">
                        <a-descriptions :column="3" size="small" bordered layout="vertical">
                            <a-descriptions-item label="商品编码">{{ selectedProduct.code }}</a-descriptions-item>
                            <a-descriptions-item label="商品条码">{{ selectedProduct.bar_code }}</a-descriptions-item>
                            <a-descriptions-item label="商品分类">{{ selectedProduct.category?.name }}</a-descriptions-item>
                        </a-descriptions>
                    </div>
                </template>

                <a-divider />

                <a-form-item label="渠道商品名称" name="channel_product_name">
                    <a-input v-model:value="formState.channel_product_name" placeholder="请输入渠道商品名称" allowClear/>
                </a-form-item>

                <a-form-item label="渠道商品编码" name="channel_product_no">
                    <a-input v-model:value="formState.channel_product_no" placeholder="请输入渠道商品编码" allowClear/>
                </a-form-item>

            </a-form>
        </a-modal>
    </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, h } from 'vue';
import {
    PlusOutlined,
    SearchOutlined,
    ReloadOutlined,
    LeftOutlined,
    LinkOutlined,
    CopyOutlined,
    DeleteOutlined,
    EditOutlined
} from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { getDictItems } from '@/api/system/dict';
import { formatDate } from '@/utils/utils';
import { getChannelProducts, createChannelProduct, updateChannelProduct, deleteChannelProduct, getChannelProductCount } from '@/api/products/channel';
import { getProducts, searchProducts } from '@/api/products/product';

export default defineComponent({
    name: 'ChannelProducts',
    components: {
        PlusOutlined,
        SearchOutlined,
        ReloadOutlined,
        LeftOutlined,
        LinkOutlined,
        CopyOutlined,
        DeleteOutlined,
        EditOutlined
    },
    setup() {
        const router = useRouter();
        const loading = ref(false);
        const productList = ref([]);
        const modalVisible = ref(false);
        const modalTitle = ref('新增关联商品');
        const formRef = ref(null);
        const selectedChannel = ref(null);
        const channelLoading = ref(false);
        const channelProductCount = ref({});

        // 表格列定义
        const columns = [
            {
                title: '渠道商品名称',
                dataIndex: 'channel_product_name',
                key: 'channel_product_name',
                width: 200,
            },
            {
                title: '渠道商品编码',
                dataIndex: 'channel_product_no',
                key: 'channel_product_no',
                width: 120,
            },
            {
                title: '关联商品',
                dataIndex: 'product',
                key: 'product',
                width: 150,
            },
            {
                title: '关联时间',
                dataIndex: 'created_at',
                key: 'created_at',
                width: 180,
            },
            {
                title: '操作',
                key: 'action',
                fixed: 'right',
                width: 150,
            },
        ];

        // 分页配置
        const pagination = reactive({
            current: 1,
            pageSize: 10,
            total: 0,
            showTotal: total => `共 ${total} 条`,
            showSizeChanger: true,
            showQuickJumper: true,
        });

        // 搜索表单
        const searchForm = reactive({
            channel_id: undefined,
            channel_product_name: '',
            channel_product_no: '',
        });

        // 表单状态
        const formState = reactive({
            channel_id: undefined,
            channel_product_name: '',
            channel_product_no: '',
            product_id: undefined
        });

        // 表单验证规则
        const rules = {
            channel_id: [
                { required: true, message: '请选择渠道' }
            ],
            channel_product_name: [
                { required: true, message: '请输入渠道商品名称' },
                { min: 2, max: 100, message: '长度在 2 到 100 个字符' }
            ],
            channel_product_no: [
                { required: true, message: '请输入渠道商品编码' },
                { min: 2, max: 50, message: '长度在 2 到 50 个字符' }
            ],
            product_id: [
                { required: true, message: '请选择关联商品' }
            ]
        };

        const getPopupContainer = (triggerNode) => {
            return triggerNode.parentElement;
        };

        // 获取渠道选项
        const channelOptions = ref([]);

        // 下拉框搜索过滤
        const filterOption = (input, option) => {
            return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        };

        // 获取渠道商品数量
        const fetchChannelProductCount = async (channelId) => {
            try {
                const res = await getChannelProductCount({ channel_id: channelId });
                if (res.code === 200) {
                    channelProductCount.value[channelId] = res.data;
                }
            } catch (error) {
                console.error('获取渠道商品数量失败:', error);
            }
        };

        // 获取所有渠道的商品数量
        const fetchAllChannelProductCounts = async () => {
            if (channelOptions.value.length > 0) {
                for (const channel of channelOptions.value) {
                    await fetchChannelProductCount(channel.value);
                }
            }
        };

        // 渠道选择处理
        const handleChannelSelect = (channelId) => {
            selectedChannel.value = channelId;
            searchForm.channel_id = channelId;
            fetchProducts();
        };

        // 获取渠道选项
        const fetchChannelOptions = async () => {
            try {
                channelLoading.value = true;
                const res = await getDictItems(null, {
                    code: 'channel',
                    pageSize: 10000
                });
                if (res.code === 200 && res.data?.list) {
                    channelOptions.value = res.data.list.map(item => ({
                        value: item.id,
                        label: item.label
                    }));
                    // 获取所有渠道的商品数量
                    await fetchAllChannelProductCounts();
                } else {
                    message.error(res.message || '获取渠道选项失败');
                }
            } catch (error) {
                console.error('获取渠道选项失败:', error);
                message.error('获取渠道选项失败');
            } finally {
                channelLoading.value = false;
            }
        };

        // 获取商品列表
        const fetchProducts = async () => {
            loading.value = true;
            try {
                const res = await getChannelProducts({
                    page: pagination.current,
                    pageSize: pagination.pageSize,
                    ...searchForm
                });
                if (res.code === 200) {
                    productList.value = res.data.list || [];
                    pagination.total = res.data.total || 0;
                    // 更新当前选中渠道的商品数量
                    if (selectedChannel.value) {
                        await fetchChannelProductCount(selectedChannel.value);
                    }
                }
            } catch (error) {
                console.error('获取商品列表失败:', error);
                message.error('获取商品列表失败');
            } finally {
                loading.value = false;
            }
        };

        // 搜索
        const handleSearch = () => {
            pagination.current = 1;
            fetchProducts();
        };

        // 重置搜索
        const handleReset = () => {
            searchForm.channel_product_name = '';
            searchForm.channel_product_no = '';
            fetchProducts();
        };

        // 表格变化
        const handleTableChange = (pag) => {
            pagination.current = pag.current;
            pagination.pageSize = pag.pageSize;
            fetchProducts();
        };

        // 商品搜索相关
        const productLoading = ref(false);
        const productOptions = ref([]);
        const selectedProduct = ref(null);

        // 搜索商品
        const handleProductSearch = async (value) => {
            if (!value || value.length < 2) {
                productOptions.value = [];
                return;
            }

            productLoading.value = true;
            try {
                const res = await searchProducts(value);
                if (res.code === 200) {
                    productOptions.value = res.data.map(item => ({
                        value: item.id,
                        label: `${item.goods_name} (${item.bar_code || '无条码'})`,
                        item: {
                            id: item.id,
                            name: item.goods_name,
                            code: item.goods_no,
                            bar_code: item.bar_code,
                            category: item.category
                        }
                    }));
                }
            } catch (error) {
                console.error('搜索商品失败:', error);
                message.error('搜索商品失败');
            } finally {
                productLoading.value = false;
            }
        };

        // 选择商品
        const handleProductSelect = (value, option) => {
            const selected = productOptions.value.find(item => item.value === value);
            if (selected) {
                selectedProduct.value = selected.item;
                // 自动填充渠道商品名称
                formState.channel_product_name = selected.item.name;
                formState.channel_product_no = selected.item.code;
            }
        };

        // 显示添加模态框
        const showAddModal = () => {
            if (!selectedChannel.value) {
                message.warning('请先选择渠道');
                return;
            }
            modalTitle.value = '新增渠道商品';
            formState.channel_id = selectedChannel.value;
            formState.channel_product_name = '';
            formState.channel_product_no = '';
            formState.product_id = undefined;
            modalVisible.value = true;
        };

        // 显示编辑模态框
        const handleEdit = (record) => {
            modalTitle.value = '编辑渠道商品';
            Object.assign(formState, {
                id: record.id,
                channel_id: record.channel_id,
                channel_product_name: record.channel_product_name,
                channel_product_no: record.channel_product_no,
                product_id: record.product_id
            });
            modalVisible.value = true;
        };

        // 删除商品
        const handleDelete = async (record) => {
            try {
                const res = await deleteChannelProduct(record.id);
                if (res.code === 200) {
                    message.success('删除成功');
                    if (productList.value.length === 1 && pagination.current > 1) {
                        pagination.current -= 1;
                    }
                    fetchProducts();
                } else {
                    message.error(res.message || '删除失败');
                }
            } catch (error) {
                console.error('删除商品失败:', error);
                message.error('删除商品失败');
            }
        };

        // 提交表单
        const handleModalOk = () => {
            formRef.value.validate().then(async () => {
                try {
                    let res;
                    if (formState.id) {
                        res = await updateChannelProduct(formState.id, formState);
                    } else {
                        res = await createChannelProduct(formState);
                    }

                    if (res.code === 200) {
                        message.success(`${modalTitle.value}成功`);
                        modalVisible.value = false;
                        fetchProducts();
                    } else {
                        message.error(res.message || `${modalTitle.value}失败`);
                    }
                } catch (error) {
                    console.error(`${modalTitle.value}失败:`, error);
                    message.error(`${modalTitle.value}失败`);
                }
            });
        };

        // 取消表单
        const handleModalCancel = () => {
            formRef.value?.resetFields();
            modalVisible.value = false;
        };

        // 返回上一页
        const goBack = () => {
            router.push('/products');
        };

        onMounted(() => {
            fetchChannelOptions();
        });

        return {
            loading,
            productList,
            columns,
            pagination,
            searchForm,
            modalVisible,
            modalTitle,
            formRef,
            formState,
            rules,
            handleSearch,
            handleReset,
            handleTableChange,
            showAddModal,
            handleEdit,
            handleDelete,
            handleModalOk,
            handleModalCancel,
            getPopupContainer,
            goBack,
            channelOptions,
            filterOption,
            channelLoading,
            formatDate,
            selectedChannel,
            handleChannelSelect,
            channelProductCount,
            productOptions,
            productLoading,
            selectedProduct,
            handleProductSearch,
            handleProductSelect,
        };
    }
});
</script>

<style lang="less" scoped>
.registered-products {
    .page-header {
        margin-bottom: 16px;
        display: flex;
        align-items: center;

        h2 {
            margin: 0;
            font-size: 20px;
            font-weight: 500;
            color: #272343;
        }
    }

    .table-operations {
        margin-bottom: 16px;
    }

    .table-search-form {
        :deep(.ant-form-item) {
            margin-bottom: 16px;

            .ant-form-item-label {
                width: 80px;
                text-align: right;
            }
        }
    }

    .text-danger {
        color: #ff4d4f;
    }

    .product-info {
        margin: 16px 24px;
        padding: 16px;
        background-color: #fafafa;
        border-radius: 4px;
        border: 1px solid #f0f0f0;

        :deep(.ant-descriptions) {
            margin-bottom: 8px;

            .ant-descriptions-item {
                padding: 12px 16px;
            }

            .ant-descriptions-item-label {
                width: 100px;
                color: #666;
                background-color: #f5f5f5;
            }
        }

        .use-product-name {
            padding: 0;
            height: auto;
            line-height: 1.5;
            margin-top: 8px;
            display: flex;
            align-items: center;
            gap: 4px;

            &:hover {
                color: #1890ff;
            }
        }
    }

    .channel-sider {
        background: #fff;
        border-right: 1px solid #f0f0f0;
        border-radius: 5px;
        
        :deep(.ant-list-item) {
            padding: 12px 24px;
            cursor: pointer;
            transition: all 0.3s;
            border-radius: 5px;

            &:hover {
                background-color: #dbdbdb;

            }

            &.selected-channel {
                background-color: #272343;
                color: #fff;
            }

            .product-count {
                color: #999;
                margin-left: 8px;
            }
        }
    }
}
</style>
