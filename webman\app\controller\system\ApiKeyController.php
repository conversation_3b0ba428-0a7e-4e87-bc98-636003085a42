<?php
namespace app\controller\system;

use support\Request;
use app\model\ApiKey;
use support\Response;
use Respect\Validation\Validator as v;

class ApiKeyController
{
    /**
     * 获取API密钥列表
     */
    public function index(Request $request)
    {
        $list = ApiKey::orderBy('id', 'desc')->get();
        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $list
        ]);
    }

    /**
     * 创建API密钥
     */
    public function store(Request $request)
    {
        $data = $request->post();
        
        if (empty($data['name'])) {
            return json(['code' => 400, 'message' => '请提供密钥名称']);
        }

        $apiKey = ApiKey::create([
            'name' => $data['name'],
            'apikey' => ApiKey::generateApiKey(),
            'status' => $data['status'] ?? 1,
            'remark' => $data['remark'] ?? '',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        return json([
            'code' => 200,
            'message' => '创建成功',
            'data' => $apiKey
        ]);
    }

    /**
     * 更新API密钥
     */
    public function update(Request $request, $id)
    {
        $data = $request->post();
        $apiKey = ApiKey::find($id);
        
        if (!$apiKey) {
            return json(['code' => 404, 'message' => 'API密钥不存在']);
        }

        $apiKey->update([
            'name' => $data['name'] ?? $apiKey->name,
            'status' => $data['status'] ?? $apiKey->status,
            'remark' => $data['remark'] ?? $apiKey->remark,
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        return json([
            'code' => 200,
            'message' => '更新成功'
        ]);
    }

    /**
     * 删除API密钥
     */
    public function destroy(Request $request, $id)
    {
        $apiKey = ApiKey::find($id);
        
        if (!$apiKey) {
            return json(['code' => 404, 'message' => 'API密钥不存在']);
        }

        $apiKey->delete();

        return json([
            'code' => 200,
            'message' => '删除成功'
        ]);
    }

    /**
     * 重置API密钥
     */
    public function resetSecret(Request $request, $id)
    {
        $apiKey = ApiKey::find($id);
        
        if (!$apiKey) {
            return json(['code' => 404, 'message' => 'API密钥不存在']);
        }

        $apiKey->update([
            'apikey' => ApiKey::generateApiKey(),
            'updated_at' => date('Y-m-d H:i:s')
        ]);

        return json([
            'code' => 200,
            'message' => '重置成功',
            'data' => [
                'apikey' => $apiKey->apikey
            ]
        ]);
    }
} 