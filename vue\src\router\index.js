import { createRouter, createWebHistory } from 'vue-router';
import { useUserStore } from '@/store/modules/user';

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/403',
    name: '403',
    component: () => import('../views/403.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: () => import('../components/BasicLayout.vue'),
    children: [
      {
        path: '',
        redirect: '/home'
      },
      {
        path: '/home',
        name: 'Home',
        component: () => import('../views/Home.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: '/members',
        name: 'Members',
        component: () => import('../views/Members.vue'),
        meta: { 
          requiresAuth: true,
          permission: 'members'
        }
      },
      {
        path: '/products',
        component: () => import('@/views/products/Index.vue'),
        meta: {
          title: '商品管理',
          icon: 'ShoppingOutlined',
          requiresAuth: true
        },
      },
      {
        path: '/products/registered',
        component: () => import('@/views/products/RegisteredProducts.vue'),
        meta: {
          requiresAuth: true,
          permission: 'products:registered'
        },
      },
      {
        path: '/products/channel',
        component: () => import('@/views/products/ChannelProducts.vue'),
        meta: {
          requiresAuth: true,
          permission: 'products:channel'
        },
      },
      {
        path: '/products/categories',
        component: () => import('@/views/products/Categories.vue'),
        meta: {
          requiresAuth: true,
          permission: 'products:categories'
        },
      },
      {
        path: '/orders',
        name: 'Orders',
        component: () => import('../views/Orders.vue'),
        meta: { requiresAuth: true }
      },
      {
        path: '/orders/center',
        name: 'OrderCenter',
        component: () => import('../views/orders/OrderCenter.vue'),
        meta: { 
          requiresAuth: true,
          permission: 'orders:manage'
        }
      },
      {
        path: '/orders/screen',
        name: 'DataScreen',
        component: () => import('../views/orders/DataScreen.vue'),
        meta: { 
          requiresAuth: true,
          permission: 'orders:view'
        }
      },
      {
        path: '/system',
        name: 'System',
        component: () => import('../views/system/index.vue'),
        meta: { 
          requiresAuth: true,
          permission: 'system'
        }
      },
      {
        path: '/system/template',
        name: 'SystemTemplate',
        component: () => import('../views/system/declaration-template/index.vue'),
        meta: { 
          requiresAuth: true,
          permission: 'system:template'
        }
      },
      {
        path: '/system/enterprise',
        name: 'SystemEnterprise',
        component: () => import('../views/system/enterprise/index.vue'),
        meta: { 
          requiresAuth: true,
          permission: 'system:enterprise'
        }
      },
      {
        path: '/system/permission',
        name: 'SystemPermission',
        component: () => import('../views/system/permission/index.vue'),
        meta: { 
          requiresAuth: true,
          permission: 'system:permission'
        }
      },
      {
        path: '/system/dict',
        name: 'SystemDict',
        component: () => import('../views/system/dict/index.vue'),
        meta: { 
          requiresAuth: true,
          permission: 'system:dict'
        }
      },
      {
        path: '/system/log',
        name: 'SystemLog',
        component: () => import('../views/system/log/index.vue'),
        meta: { 
          requiresAuth: true,
          permission: 'system:log'
        }
      },
      {
        path: '/system/schedule',
        name: 'SystemSchedule',
        component: () => import('../views/system/schedule/index.vue'),
        meta: { 
          requiresAuth: true,
          permission: 'system:schedule'
        }
      },
      {
        path: '/system/download',
        name: 'SystemDownload',
        component: () => import('../views/system/download/index.vue'),
        meta: { 
          requiresAuth: true,
          permission: 'system:download'
        }
      },
      {
        path: '/system/info',
        name: 'SystemInfo',
        component: () => import('../views/system/info/index.vue'),
        meta: { 
          requiresAuth: true,
          permission: 'system:info'
        }
      },
      {
        path: '/system/apikey',
        name: 'SystemApiKey',
        component: () => import('../views/system/apikey/index.vue'),
        meta: { 
          requiresAuth: true,
          permission: 'system:apikey'
        }
      },
      {
        path: '/system/apikey/test',
        name: 'SystemApiKeyTest',
        component: () => import('../views/system/apikey/test.vue'),
        meta: { 
          requiresAuth: true,
          permission: 'system:apikey'
        }
      },
      {
        path: 'enterprise',
        name: 'Enterprise',
        component: () => import('@/views/system/enterprise/index.vue'),
        meta: { title: '企业管理', requiresAuth: true, permission: 'system:enterprise' }
      },
      {
        path: 'declaration-template',
        name: 'DeclarationTemplate',
        component: () => import('@/views/system/declaration-template/index.vue'),
        meta: { title: '申报模板', requiresAuth: true, permission: 'system:template' }
      },
      {
        path: 'apps',
        name: 'Apps',
        component: () => import('@/views/apps/Index.vue'),
        meta: { title: '应用中心', requiresAuth: true, permission: 'apps:manage' }
      },
      {
        path: 'apps/logs',
        name: 'AppsLogs',
        component: () => import('@/views/apps/Logs.vue'),
        meta: { title: '应用日志', requiresAuth: true, permission: 'apps:logs' }
      },
    ]
  }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

// 防止重复获取用户信息
let isGettingUserInfo = false;

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 如果是访问登录页或403页，直接放行
  if (to.name === 'Login' || to.name === '403') {
    next();
    return;
  }

  const userStore = useUserStore();
  const token = userStore.token;

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    if (!token) {
      // 如果需要认证但没有token，重定向到登录页
      next({ 
        name: 'Login',
        query: { redirect: to.fullPath }
      });
      return;
    }

    // 如果有token但没有用户信息，先获取用户信息
    if (!userStore.userInfo && !isGettingUserInfo) {
      isGettingUserInfo = true;
      try {
        const userResult = await userStore.getUserInfo();
        if (!userResult) {
          // 如果获取用户信息失败，清除token并跳转到登录页
          userStore.token = '';
          localStorage.removeItem('token');
          next({ 
            name: 'Login',
            query: { redirect: to.fullPath }
          });
          return;
        }
        
        // 获取用户权限
        const permissionResult = await userStore.getPermissions();
        if (!permissionResult) {
          console.error('获取权限失败');
          userStore.token = '';
          localStorage.removeItem('token');
          next({ 
            name: 'Login',
            query: { redirect: to.fullPath }
          });
          return;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        // 发生错误时，清除token并跳转到登录页
        userStore.token = '';
        localStorage.removeItem('token');
        next({ 
          name: 'Login',
          query: { redirect: to.fullPath }
        });
        return;
      } finally {
        isGettingUserInfo = false;
      }
    }

    // 检查是否有权限访问
    if (to.meta.permission && !userStore.hasPermission(to.meta.permission)) {
      next({ name: '403' });
      return;
    }
  }
  
  next();
});

export default router;
