import request from '@/utils/request';

/**
 * 获取字典列表
 * @param {Object} params
 * @returns {Promise}
 */
export function getDictList(params) {
  return request({
    url: '/system/dict/list',
    method: 'get',
    params
  });
}

/**
 * 创建字典
 * @param {Object} data
 * @returns {Promise}
 */
export function createDict(data) {
  return request({
    url: '/system/dict',
    method: 'post',
    data
  });
}

/**
 * 更新字典
 * @param {number} id
 * @param {Object} data
 * @returns {Promise}
 */
export function updateDict(id, data) {
  return request({
    url: `/system/dict/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除字典
 * @param {number} id
 * @returns {Promise}
 */
export function deleteDict(id) {
  return request({
    url: `/system/dict/${id}`,
    method: 'delete'
  });
}

/**
 * 获取字典项列表
 * @param {number} dictId
 * @param {Object} params
 * @returns {Promise}
 */
export function getDictItems(dictId, params) {
  return request({
    url: dictId ? `/system/dict/${dictId}/items` : '/system/dict/items',
    method: 'get',
    params
  });
}

/**
 * 创建字典项
 * @param {Object} data
 * @returns {Promise}
 */
export function createDictItem(data) {
  return request({
    url: '/system/dict/item',
    method: 'post',
    data
  });
}

/**
 * 更新字典项
 * @param {number} id
 * @param {Object} data
 * @returns {Promise}
 */
export function updateDictItem(id, data) {
  return request({
    url: `/system/dict/item/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除字典项
 * @param {number} id
 * @returns {Promise}
 */
export function deleteDictItem(id) {
  return request({
    url: `/system/dict/item/${id}`,
    method: 'delete'
  });
} 