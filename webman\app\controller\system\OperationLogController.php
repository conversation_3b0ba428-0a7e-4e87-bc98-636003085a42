<?php

namespace app\controller\system;

use support\Request;
use app\controller\Controller;
use app\model\OperationLog;

class OperationLogController extends Controller
{
    /**
     * 获取操作日志列表
     * @param Request $request
     * @return \support\Response
     */
    public function list(Request $request)
    {
        $page = $request->get('page', 1);
        $pageSize = $request->get('pageSize', 10);
        $username = $request->get('username', '');
        $module = $request->get('module', '');
        $action = $request->get('action', '');
        $startTime = $request->get('startTime', '');
        $endTime = $request->get('endTime', '');

        $query = OperationLog::with('member')
            ->when($username, function ($query) use ($username) {
                return $query->where('username', 'like', "%{$username}%");
            })
            ->when($module, function ($query) use ($module) {
                return $query->where('module', $module);
            })
            ->when($action, function ($query) use ($action) {
                return $query->where('action', $action);
            })
            ->when($startTime && $endTime, function ($query) use ($startTime, $endTime) {
                return $query->whereBetween('created_at', [$startTime, $endTime]);
            });

        $total = $query->count();
        $logs = $query->orderBy('created_at', 'desc')
            ->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->get();

        return json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'list' => $logs,
                'total' => $total,
                'page' => $page,
                'pageSize' => $pageSize
            ]
        ]);
    }

    /**
     * 清空操作日志
     * @return \support\Response
     */
    public function clear()
    {
        try {
            OperationLog::truncate();
            return json(['code' => 200, 'message' => '清空成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '清空失败：' . $e->getMessage()]);
        }
    }
} 