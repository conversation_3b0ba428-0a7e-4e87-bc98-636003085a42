<?php
namespace app\controller;

use support\Request;
use app\model\App;
use app\model\AppLog;
use app\model\AppConfigTemplate;
use plugins\customs179\Service as Customs179Service;

class AppController
{
    /**
     * 获取应用列表
     */
    public function index(Request $request)
    {
        $apps = App::orderBy('id', 'desc')->get();
        
        // 加载每个应用的配置模式
        foreach ($apps as $app) {
            $configPath = base_path() . "/plugins/{$app->code}/config.json";
            if (file_exists($configPath)) {
                $schema = json_decode(file_get_contents($configPath), true);
                $app->settings_schema = $schema['settings_schema'] ?? null;
                $app->settings = $app->settings == null || $app->settings == '' ? null : json_decode($app->settings, true);
                
                $app->picture = $schema['picture'] ?? '';
                $app->docUrl = $schema['docUrl'] ?? '';
                // 加载版本等信息
                $app->version = $schema['version'] ?? '';
                $app->description = $schema['description'] ?? '';
                $app->author = $schema['author'] ?? null;
            }
        }

        return json(['code' => 200, 'message' => '获取成功', 'data' => $apps]);
    }

    /**
     * 获取应用详情
     */
    public function detail(Request $request, $id)
    {
        $app = App::find($id);
        if (!$app) {
            return json(['code' => 404, 'message' => '应用不存在']);
        }

        // 加载配置模式
        $configPath = base_path() . "/plugins/{$app->code}/config.json";
        if (file_exists($configPath)) {
            $schema = json_decode(file_get_contents($configPath), true);
            $app->settings_schema = $schema['settings_schema'] ?? null;
            $app->settings = json_decode($app->settings, true);
        }

        return json(['code' => 200, 'message' => '获取成功', 'data' => $app]);
    }

    /**
     * 获取应用日志
     */
    public function logs(Request $request)
    {
        $appId = $request->get('app_id');
        $level = $request->get('level');
        $startTime = $request->get('start_time');
        $endTime = $request->get('end_time');
        $page = $request->get('page', 1);
        $pageSize = $request->get('pageSize', 10);

        $query = AppLog::with('app')
            ->orderBy('id', 'desc');

        if ($appId) {
            $query->where('app_id', $appId);
        }
        if ($level) {
            $query->where('level', $level);
        }
        if ($startTime) {
            $query->where('created_at', '>=', $startTime);
        }
        if ($endTime) {
            $query->where('created_at', '<=', $endTime);
        }

        $total = $query->count();
        $list = $query->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->get();

        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'list' => $list,
                'total' => $total
            ]
        ]);
    }

    /**
     * 获取应用配置模板列表
     */
    public function getTemplates(Request $request, $id)
    {
        if (!$id) {
            return json(['code' => 400, 'message' => '请提供应用ID']);
        }

        // 验证应用是否存在
        $app = App::find($id);
        if (!$app) {
            return json(['code' => 404, 'message' => '应用不存在']);
        }

        $templates = AppConfigTemplate::where('app_id', $id)
            ->orderBy('id', 'desc')
            ->get();

        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $templates
        ]);
    }

    /**
     * 保存配置模板
     */
    public function saveTemplate(Request $request, $id)
    {
        $data = $request->post();
        
        // 验证应用是否存在
        $app = App::find($id);
        if (!$app) {
            return json(['code' => 404, 'message' => '应用不存在']);
        }

        // 验证必填字段
        if (empty($data['name'])) {
            return json(['code' => 400, 'message' => '请提供模板名称']);
        }

        // 如果是179公告应用，验证渠道唯一性并生成key
        if ($app->code === 'CUSTOMS179') {
            $settings = $data['settings'] ?? [];
            if (empty($settings['channel_id'])) {
                return json(['code' => 400, 'message' => '请选择渠道']);
            }

            // 验证渠道唯一性
            $excludeTemplateId = !empty($data['id']) ? $data['id'] : null;
            if (!Customs179Service::validateChannelUnique($settings['channel_id'], $excludeTemplateId)) {
                return json(['code' => 400, 'message' => '该渠道已配置，不可重复配置']);
            }

            // 生成key
            if (empty($data['id'])) {
                $settings['key'] = Customs179Service::generateKey();
                $data['settings'] = $settings;
            }
        }

        // 如果有ID则更新，否则创建
        if (!empty($data['id'])) {
            $template = AppConfigTemplate::find($data['id']);
            if (!$template) {
                return json(['code' => 404, 'message' => '模板不存在']);
            }
            
            $template->update([
                'name' => $data['name'],
                'description' => $data['description'] ?? '',
                'settings' => $data['settings'] ?? [],
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        } else {
            $template = AppConfigTemplate::create([
                'app_id' => $id,
                'name' => $data['name'],
                'description' => $data['description'] ?? '',
                'settings' => $data['settings'] ?? [],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }

        return json([
            'code' => 200,
            'message' => '保存成功',
            'data' => $template
        ]);
    }

    /**
     * 删除配置模板
     */
    public function deleteTemplate(Request $request, $id, $templateId)
    {
        // 验证应用是否存在
        $app = App::find($id);
        if (!$app) {
            return json(['code' => 404, 'message' => '应用不存在']);
        }

        $template = AppConfigTemplate::where('app_id', $id)
            ->where('id', $templateId)
            ->first();
            
        if (!$template) {
            return json(['code' => 404, 'message' => '模板不存在']);
        }

        $template->delete();

        return json([
            'code' => 200,
            'message' => '删除成功'
        ]);
    }

    /**
     * 更新应用配置
     */
    public function updateSettings(Request $request, $id)
    {
        $app = App::find($id);
        if (!$app) {
            return json(['code' => 404, 'message' => '应用不存在']);
        }

        // 获取配置模式
        $configPath = base_path() . "/plugins/{$app->code}/config.json";
        if (!file_exists($configPath)) {
            return json(['code' => 400, 'message' => '应用配置文件不存在']);
        }

        $schema = json_decode(file_get_contents($configPath), true);
        $fields = $schema['settings_schema']['fields'] ?? [];

        // 验证和处理配置数据
        $settings = [];
        foreach ($fields as $field) {
            $value = $request->post($field['field']);
            
            // 验证必填
            if ($field['required'] && empty($value)) {
                return json(['code' => 400, 'message' => $field['label'] . '不能为空']);
            }
            
            // 验证规则
            if (!empty($field['rules'])) {
                $pattern = $field['rules']['pattern'] ?? null;
                if ($pattern && !preg_match("#$pattern#", $value)) {  // 使用 # 作为分隔符
                    return json(['code' => 400, 'message' => $field['rules']['message'] ?? '格式错误']);
                }
            }
            
            // 加密敏感信息
            if (!empty($field['encrypt'])) {
                // $value = encrypt($value);
            }
            
            $settings[$field['field']] = $value;
        }

        // 更新配置
        $app->updateConfig($settings);

        // 记录日志
        AppLog::create([
            'app_id' => $app->id,
            'level' => 'info',
            'message' => '更新应用配置',
            'context' => [
                'operator' => $request->member->uid,
                'settings' => array_keys($settings)
            ],
            'created_at' => date('Y-m-d H:i:s')
        ]);

        return json(['code' => 200, 'message' => '保存成功']);
    }
}