import { defineStore } from 'pinia';
import { menuConfig } from '@/config/menu';
import { getUserInfo as getMemberInfo } from '@/api/user'; // 引入获取用户信息的 API
import { getUserPermissionTree, getPermissionTree } from '@/api/system/permission';

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    token: null,
    refreshToken: null,
    permissions: [] // 初始化permissions为空数组
  }),

  getters: {
    isLoggedIn: (state) => !!state.token,
    username: (state) => state.userInfo?.username,
    name: (state) => state.userInfo?.name,
  },

  actions: {
    setUserInfo(userInfo) {
      this.userInfo = userInfo;
    },

    setToken(token) {
      this.token = token;
      if (token) {
        localStorage.setItem('token', token);
      } else {
        localStorage.removeItem('token');
      }
    },

    setRefreshToken(token) {
      this.refreshToken = token;
      if (token) {
        localStorage.setItem('refreshToken', token);
      } else {
        localStorage.removeItem('refreshToken');
      }
    },

    getRefreshToken() {
      return localStorage.getItem('refreshToken');
    },

    logout() {
      this.userInfo = null;
      this.token = null;
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
    },

    // 初始化状态
    init() {
      const token = localStorage.getItem('token');
      if (token) {
        this.token = token;
      }
    },

    
    // 设置权限列表
    setPermissions(permissions) {
      this.permissions = permissions;
    },

    // 检查是否有指定权限
    hasPermission(permission) {
      // 如果是超级管理员，直接返回true
      if (this.userInfo?.is_super === 1) {
        return true;
      }
      
      // 递归检查权限树
      const checkPermission = (nodes) => {
        for (const node of nodes) {
          if (node.permission_code === permission) {
            return true;
          }
          if (node.children && node.children.length > 0) {
            if (checkPermission(node.children)) {
              return true;
            }
          }
        }
        return false;
      };
      
      return checkPermission(this.permissions);
    },

    // 检查是否有任意一个权限
    hasAnyPermission(permissions) {
      // 如果是超级管理员，直接返回true
      if (this.userInfo?.is_super === 1) {
        return true;
      }
      return permissions.some(permission => this.permissions.includes(permission));
    },

    // 检查是否有全部权限
    hasAllPermissions(permissions) {
      // 如果是超级管理员，直接返回true
      if (this.userInfo?.is_super === 1) {
        return true;
      }
      return permissions.every(permission => this.permissions.includes(permission));
    },


    // 新增：获取用户信息
    async getUserInfo() {
      try {
        const response = await getMemberInfo(); // 调用 API 获取用户信息
        if (response.code === 200) {
          this.setUserInfo(response.data); // 更新用户信息
          return true;
        }
        return false;
      } catch (error) {
        console.error('获取用户信息失败:', error);
        return false;
      }
    },

    // 获取用户权限树
    async getPermissions() {
      try {
        const response = await getUserPermissionTree();
        if (response.code === 200) {
          // 保存完整权限树
          this.permissions = response.data;
          return true;
        }
        return false;
      } catch (error) {
        console.error('获取权限树失败:', error);
        return false;
      }
    },

    // 获取用户权限树
    async getPermissionTree() {
      try {
        const response = await getPermissionTree();
        if (response.code === 200) {
          // 构建权限树
          const buildTree = (nodes, parentId = 0) => {
            return nodes
              .filter(node => node.parent_id === parentId)
              .map(node => ({
                ...node,
                children: buildTree(nodes, node.id)
              }));
          };
          
          this.permissions = buildTree(response.data);
          return true;
        }
        return false;
      } catch (error) {
        console.error('获取权限树失败:', error);
        return false;
      }
    },
    
    // 获取过滤后的菜单配置
    getFilteredMenuConfig() {
      const menuList = this.permissions;

      console.log('menuList:', menuList);

      return menuList.map(item => {
        return {
          key: item.permission_code,
          label: item.name,
          icon: item.icon,
          path: item.path,
          permission: item.permission_code
        };
      });
    },

    // 检查是否有按钮权限
    hasButtonPermission(buttonCode) {
      if (this.userInfo?.is_super === 1) {
        return true;
      }
      
      // 递归检查权限树
      const checkPermission = (nodes) => {
        return nodes.some(item => {
          // 如果是按钮权限且匹配
          if (item.menu_type === 'button' && item.permission_code === buttonCode) {
            return true;
          }
          // 递归检查子权限
          if (item.children && item.children.length > 0) {
            return checkPermission(item.children);
          }
          return false;
        });
      };
      
      return checkPermission(this.permissions);
    },
  },
});
