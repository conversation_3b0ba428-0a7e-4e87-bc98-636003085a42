<?php
namespace app\controller\system;

use support\Request;
use app\model\ApiRequestLog;

class ApiRequestLogController
{
    /**
     * 获取接口请求日志列表
     */
    public function index(Request $request)
    {
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);
        $appId = $request->input('app_id');
        $status = $request->input('status');
        $startTime = $request->input('start_time');
        $endTime = $request->input('end_time');

        $query = ApiRequestLog::with(['app', 'template'])
            ->orderBy('id', 'desc');

        // 条件筛选
        if ($appId) {
            $query->where('app_id', $appId);
        }
        if ($status !== null) {
            $query->where('status', $status);
        }
        if ($startTime && $endTime) {
            $query->whereBetween('created_at', [$startTime, $endTime]);
        }

        // 获取总数
        $total = $query->count();

        // 获取分页数据
        $list = $query->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->get();

        // 增加请求来源字段
        $list->transform(function ($item) {
            // 订单相关日志（无app_id但有template_id且有template）
            if (!$item->app_id && $item->template_id && $item->template) {
                // 通过申报模板获取渠道
                $channelName = $item->template->channel ? $item->template->channel->label : '未知渠道';
                $item->request_source = $channelName;
            } else if ($item->app) {
                $item->request_source = $item->app->name;
            } else {
                $item->request_source = '-';
            }
            return $item;
        });

        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'list' => $list,
                'total' => $total
            ]
        ]);
    }

    /**
     * 删除日志
     */
    public function destroy(Request $request, $id)
    {
        $log = ApiRequestLog::find($id);
        if (!$log) {
            return json(['code' => 404, 'message' => '日志不存在']);
        }

        $log->delete();

        return json([
            'code' => 200,
            'message' => '删除成功'
        ]);
    }

    /**
     * 清空日志
     */
    public function clear(Request $request)
    {
        ApiRequestLog::truncate();

        return json([
            'code' => 200,
            'message' => '清空成功'
        ]);
    }
} 