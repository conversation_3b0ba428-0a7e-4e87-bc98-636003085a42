<template>
  <system-page title="申报模板">
    <div class="declaration-template-container">
      <a-card :bordered="false">
        <!-- 搜索表单 -->
        <a-form layout="inline" class="table-search-form">
          <a-row :gutter="[8, 8]">
            <a-col :span="16">
              <a-form-item label="模板名称">
                <a-input v-model="searchForm.name" placeholder="请输入模板名称" allowClear />
              </a-form-item>
            </a-col>
            <a-col :span="6">
              <a-space>
                <a-button type="primary" @click="handleSearch">
                  <template #icon><search-outlined /></template>
                  查询
                </a-button>
                <a-button @click="handleReset">
                  <template #icon><reload-outlined /></template>
                  重置
                </a-button>
              </a-space>
            </a-col>
          </a-row>
        </a-form>

        <!-- 操作按钮 -->
        <div class="table-operations">
          <a-button type="primary" @click="showAddModal">
            <template #icon><plus-outlined /></template>
            新增模板
          </a-button>
        </div>

        <!-- 模板列表 -->
        <a-table
          :columns="columns"
          :data-source="templateList"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          :row-key="record => record.id"
          :scroll="{ x: 1000 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'platform'">
              {{ record.platform_enterprise.customs_name }}
            </template>
            <template v-else-if="column.key === 'ecommerce'">
              {{ record.ecommerce_enterprise.customs_name }}
            </template>
            <template v-else-if="column.key === 'created_at'">
              {{ formatDate(record.created_at) }}
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" @click="handleEdit(record)">
                  <template #icon><edit-outlined /></template>
                  编辑
                </a-button>
                <a-popconfirm
                  title="确定要删除这个模板吗？"
                  @confirm="handleDelete(record)"
                >
                  <a-button type="link" danger>
                    <template #icon><delete-outlined /></template>
                    删除
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>

        <!-- 新增/编辑模板弹窗 -->
        <template-form-modal
          v-model:visible="modalVisible"
          :title="modalTitle"
          :record="currentRecord"
          @success="handleSuccess"
        />
      </a-card>
    </div>
  </system-page>
</template>

<script>
import SystemPage from '../components/SystemPage.vue';
import TemplateFormModal from './components/TemplateFormModal.vue';
import { defineComponent, ref, reactive, onMounted } from 'vue';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { getDeclarationTemplates, deleteDeclarationTemplate } from '@/api/system/declaration-template';
import { formatDate } from '@/utils/utils';

export default defineComponent({
  name: 'DeclarationTemplateList',
  components: {
    PlusOutlined,
    SearchOutlined,
    ReloadOutlined,
    EditOutlined,
    DeleteOutlined,
    SystemPage,
    TemplateFormModal
  },
  setup() {
    const loading = ref(false);
    const templateList = ref([]);
    const modalVisible = ref(false);
    const modalTitle = ref('新增模板');
    const currentRecord = ref(null);

    // 表格列定义
    const columns = [
      {
        title: '模板ID',
        dataIndex: 'id',
        key: 'id',
        width: 80
      },
      {
        title: '模板名称',
        dataIndex: 'name',
        key: 'name',
        width: 150
      },
      {
        title: '渠道',
        key: 'channel',
        width: 120,
        customRender: ({ record }) => record.channel?.label || '-'
      },
      {
        title: '贸易方式',
        dataIndex: 'trade_mode',
        key: 'trade_mode',
        width: 100
      },
      {
        title: '电商平台',
        key: 'platform',
        width: 250
      },
      {
        title: '电商企业',
        key: 'ecommerce',
        width: 250
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 180
      },
      {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 200
      }
    ];

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showTotal: total => `共 ${total} 条`,
      showSizeChanger: true,
      showQuickJumper: true
    });

    // 搜索表单
    const searchForm = reactive({
      name: ''
    });

    // 获取模板列表
    const fetchTemplates = async () => {
      loading.value = true;
      try {
        const res = await getDeclarationTemplates({
          page: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm
        });
        if (res.code === 200) {
          templateList.value = res.data.list;
          pagination.total = res.data.total;
        }
      } catch (error) {
        console.error('获取模板列表失败:', error);
        message.error('获取模板列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 搜索
    const handleSearch = () => {
      pagination.current = 1;
      fetchTemplates();
    };

    // 重置搜索
    const handleReset = () => {
      searchForm.name = '';
      handleSearch();
    };

    // 表格变化
    const handleTableChange = (pag) => {
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;
      fetchTemplates();
    };

    // 显示新增弹窗
    const showAddModal = () => {
      modalTitle.value = '新增模板';
      currentRecord.value = null;
      modalVisible.value = true;
    };

    // 显示编辑弹窗
    const handleEdit = (record) => {
      modalTitle.value = '编辑模板';
      currentRecord.value = record;
      modalVisible.value = true;
    };

    // 删除模板
    const handleDelete = async (record) => {
      try {
        const res = await deleteDeclarationTemplate(record.id);
        if (res.code === 200) {
          message.success('删除成功');
          if (templateList.value.length === 1 && pagination.current > 1) {
            pagination.current -= 1;
          }
          fetchTemplates();
        }
      } catch (error) {
        console.error('删除模板失败:', error);
        message.error('删除模板失败');
      }
    };

    // 新增/编辑成功回调
    const handleSuccess = () => {
      modalVisible.value = false;
      fetchTemplates();
    };

    onMounted(() => {
      fetchTemplates();
    });

    return {
      loading,
      templateList,
      columns,
      pagination,
      searchForm,
      modalVisible,
      modalTitle,
      currentRecord,
      handleSearch,
      handleReset,
      handleTableChange,
      showAddModal,
      handleEdit,
      handleDelete,
      handleSuccess,
      formatDate
    };
  }
});
</script>

<style lang="less" scoped>
.declaration-template-container {
  padding: 24px;

  .table-operations {
    margin-bottom: 16px;
  }

  .table-search-form {
    margin-bottom: 24px;

    :deep(.ant-form-item) {
      margin-bottom: 16px;
    }
  }
}
</style> 