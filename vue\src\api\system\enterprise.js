import request from '@/utils/request'

// 获取企业列表
export function getEnterprises(params) {
  return request({
    url: '/system/enterprise',
    method: 'get',
    params
  })
}

// 创建企业
export function createEnterprise(data) {
  return request({
    url: '/system/enterprise',
    method: 'post',
    data
  })
}

// 更新企业
export function updateEnterprise(id, data) {
  return request({
    url: `/system/enterprise/${id}`,
    method: 'put',
    data
  })
}

// 删除企业
export function deleteEnterprise(id) {
  return request({
    url: `/system/enterprise/${id}`,
    method: 'delete'
  })
} 