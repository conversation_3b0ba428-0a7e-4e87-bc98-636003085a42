import request from '@/utils/request';

// 获取定时任务列表
export function getScheduleList(params) {
  return request({
    url: '/system/schedule/list',
    method: 'get',
    params
  });
}

// 创建定时任务
export function createSchedule(data) {
  return request({
    url: '/system/schedule/create',
    method: 'post',
    data
  });
}

// 更新定时任务
export function updateSchedule(id, data) {
  return request({
    url: `/system/schedule/update/${id}`,
    method: 'post',
    data
  });
}

// 删除定时任务
export function deleteSchedule(id) {
  return request({
    url: `/system/schedule/delete/${id}`,
    method: 'post'
  });
}

// 获取可用的类方法列表
export function getClassMethods() {
  return request({
    url: '/system/schedule/class-methods',
    method: 'get'
  });
}

// 获取任务执行日志
export function getScheduleLogs(id, params) {
  return request({
    url: `/system/schedule/logs/${id}`,
    method: 'get',
    params
  });
}

// 获取定时任务进程状态
export function getScheduleStatus() {
  return request({
    url: '/system/schedule/status',
    method: 'get'
  });
}

// 手动执行定时任务
export function runSchedule(id) {
  return request({
    url: `/system/schedule/${id}/run`,
    method: 'post'
  });
}

// 测试邮件发送
export function testScheduleEmail(id) {
  return request({
    url: `/system/schedule/${id}/test-email`,
    method: 'post'
  });
}