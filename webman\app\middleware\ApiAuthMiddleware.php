<?php
namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;
use app\model\ApiKey;

class ApiAuthMiddleware implements MiddlewareInterface
{
    /**
     * 处理请求
     */
    public function process(Request $request, callable $handler): Response
    {
        // 获取请求头中的 API Key
        $apiKey = $request->header('x-api-key');
        if (empty($apiKey)) {
            return $this->error('缺少 API Key');
        }

        // 验证 API Key 是否有效
        $apiKeyModel = ApiKey::where('apikey', $apiKey)
            ->where('status', 1)
            ->first();

        if (!$apiKeyModel) {
            return $this->error('无效的 API Key');
        }

        // 更新最后使用时间
        $apiKeyModel->update([
            'last_used_at' => date('Y-m-d H:i:s')
        ]);

        return $handler($request);
    }

    /**
     * 返回错误信息
     */
    protected function error($message): Response
    {
        return json([
            'code' => 401,
            'message' => $message
        ]);
    }
} 