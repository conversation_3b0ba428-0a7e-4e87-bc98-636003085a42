<?php

namespace app\model;

use support\Model;

class EmailConfig extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'gaodux_email_config';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'smtp_host',
        'smtp_port',
        'smtp_encryption',
        'smtp_username',
        'smtp_password',
        'from_email',
        'from_name',
        'is_default',
        'status'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'smtp_port' => 'integer',
        'is_default' => 'boolean',
        'status' => 'boolean'
    ];

    /**
     * 获取默认邮件配置
     */
    public static function getDefault()
    {
        return static::where('is_default', 1)
            ->where('status', 1)
            ->first();
    }

    /**
     * 设置为默认配置
     */
    public function setAsDefault()
    {
        // 先取消其他配置的默认状态
        static::where('is_default', 1)->update(['is_default' => 0]);
        
        // 设置当前配置为默认
        $this->is_default = 1;
        $this->save();
        
        return $this;
    }

    /**
     * 验证邮件配置
     */
    public function validate()
    {
        $required = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'from_email'];
        
        foreach ($required as $field) {
            if (empty($this->$field)) {
                throw new \Exception("邮件配置缺少必填字段: {$field}");
            }
        }
        
        // 验证邮箱格式
        if (!filter_var($this->from_email, FILTER_VALIDATE_EMAIL)) {
            throw new \Exception("发件人邮箱格式不正确");
        }
        
        // 验证端口范围
        if ($this->smtp_port < 1 || $this->smtp_port > 65535) {
            throw new \Exception("SMTP端口范围应在1-65535之间");
        }
        
        // 验证加密方式
        if (!in_array($this->smtp_encryption, ['tls', 'ssl', 'none', ''])) {
            throw new \Exception("不支持的加密方式");
        }
        
        return true;
    }

    /**
     * 加密密码
     */
    public function setSmtpPasswordAttribute($value)
    {
        if (!empty($value)) {
            $this->attributes['smtp_password'] = encrypt($value);
        }
    }

    /**
     * 解密密码
     */
    public function getSmtpPasswordAttribute($value)
    {
        if (!empty($value)) {
            try {
                return decrypt($value);
            } catch (\Exception $e) {
                return $value; // 如果解密失败，返回原值
            }
        }
        return $value;
    }
}
