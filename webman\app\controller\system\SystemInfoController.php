<?php

namespace app\controller\system;

use support\Request;
use app\controller\Controller;
use Webman\RateLimiter\Annotation\RateLimiter;

// 系统信息控制器
class SystemInfoController extends Controller
{
    // 获取系统信息
    #[RateLimiter(limit: 3, ttl: 60, key: RateLimiter::IP, message: '获取系统信息过于频繁，请稍后再试')]
    public function info(Request $request)
    {
        // 获取服务器信息 及 硬件配置 内存占用 磁盘使用情况
        $serverInfo = [
            // 服务器基本信息
            'server_name' => php_uname('n'),
            'server_ip' => 'N/A',
            'server_port' => 'N/A',
            'server_os' => php_uname('s'),
            'server_version' => php_uname('r'),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'N/A',
        
            // 内存信息
            'server_memory_usage' => memory_get_usage(),
            'server_memory_usage_human' => $this->formatBytes(memory_get_usage()),
            'server_memory_total' => memory_get_usage(true),
            'server_memory_total_human' => $this->formatBytes(memory_get_usage(true)),
        
            // 磁盘信息
            'server_disk_total' => disk_total_space('/'),
            'server_disk_total_human' => $this->formatBytes(disk_total_space('/')),
            'server_disk_free' => disk_free_space('/'),
            'server_disk_free_human' => $this->formatBytes(disk_free_space('/')),
        
            // 系统负载信息
            'server_load_avg' => $this->getSystemLoad(),
        
            // CPU 信息
            'cpu_info' => $this->getCpuInfo(),
        
            // 运行时间和启动时间
            'server_uptime' => $this->getUptime(),
        
            // 当前用户和进程信息
            'process_info' => $this->getProcessInfo(),
        
            // 系统时区和时间
            'time_info' => $this->getTimeInfo(),
        
            // PHP 配置信息
            'php_config' => $this->getPhpConfig(),
        
            // PHP 信息
            'php_version' => $this->maskVersion(phpversion()),
            'php_extensions' => get_loaded_extensions(),
        ];
        

        return json([
            'code' => 200,
            'message' => 'success',
            'data' => $serverInfo
        ]);
    }

    // 辅助函数：将字节转换为易读格式
    protected function formatBytes($bytes, $precision = 2) {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= (1 << (10 * $pow));
        return round($bytes, $precision) . ' ' . $units[$pow];
    }

    // 跨平台获取系统负载
    protected function getSystemLoad() {
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            // Windows 系统
            $cpuUsage = shell_exec('wmic cpu get loadpercentage /value');
            preg_match('/LoadPercentage=(\d+)/', $cpuUsage, $matches);
            return $matches[1] ?? 'N/A';
        } else {
            // Linux/Unix 系统
            return sys_getloadavg();
        }
    }

    // 获取 CPU 信息
    protected function getCpuInfo() {
        if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
            // Windows 系统
            $cpuInfo = shell_exec('wmic cpu get name,NumberOfCores,NumberOfLogicalProcessors /value');
            preg_match('/Name=(.+)/', $cpuInfo, $nameMatches);
            preg_match('/NumberOfCores=(\d+)/', $cpuInfo, $coresMatches);
            preg_match('/NumberOfLogicalProcessors=(\d+)/', $cpuInfo, $logicalMatches);
            return [
                'cpu_name' => $nameMatches[1] ?? 'N/A',
                'cpu_cores' => $coresMatches[1] ?? 'N/A',
                'cpu_logical_processors' => $logicalMatches[1] ?? 'N/A',
            ];
        } else {
            // Linux/Unix 系统
            $cpuInfo = file_get_contents('/proc/cpuinfo');
            preg_match_all('/model name\s+:\s+(.+)/', $cpuInfo, $nameMatches);
            preg_match_all('/cpu cores\s+:\s+(\d+)/', $cpuInfo, $coresMatches);
            return [
                'cpu_name' => $nameMatches[1][0] ?? 'N/A',
                'cpu_cores' => $coresMatches[1][0] ?? 'N/A',
                'cpu_logical_processors' => count($nameMatches[1]) ?? 'N/A',
            ];
        }
    }

    // 获取运行时间
    protected function getUptime() {
        // if (strtoupper(substr(PHP_OS, 0, 3)) === 'WIN') {
        //     // Windows 系统
        //     $uptime = shell_exec('systeminfo | find "System Boot Time"');
        //     preg_match('/System Boot Time:\s+(.+)/', $uptime, $matches);
        //     return $matches[1] ?? 'N/A';
        // } else {
        //     // Linux/Unix 系统
        //     $uptime = file_get_contents('/proc/uptime');
        //     $uptime = (float) explode(' ', $uptime)[0];
        //     $days = floor($uptime / 86400);
        //     $hours = floor(($uptime % 86400) / 3600);
        //     $minutes = floor(($uptime % 3600) / 60);
        //     return sprintf('%d days, %d hours, %d minutes', $days, $hours, $minutes);
        // }
        return 'N/A';
    }

    // 获取当前用户和进程信息
    protected function getProcessInfo() {
        return [
            'process_user' => 'admin',
            'process_id' => getmypid(),
        ];
    }

    // 获取系统时区和时间
    protected function getTimeInfo() {
        return [
            'system_timezone' => date_default_timezone_get(),
            'system_time' => date('Y-m-d H:i:s'),
        ];
    }

    // 获取 PHP 配置信息
    protected function getPhpConfig() {
        return [
            'memory_limit' => ini_get('memory_limit'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'max_execution_time' => ini_get('max_execution_time'),
        ];
    }

    protected function maskVersion($version) {
        return preg_replace('/(\d+\.\d+)\.\d+/', '$1.x', $version);
    }

}