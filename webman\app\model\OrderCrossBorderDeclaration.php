<?php
namespace app\model;

use support\Model;

class OrderCrossBorderDeclaration extends Model
{
    protected $table = 'order_cross_border_declarations';
    
    protected $fillable = [
        'order_id',
        'order_no',
        'payment_request',
        'payment_response',
        'transaction_id',
        'created_at',
        'updated_at'
    ];

    /**
     * 关联订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }
} 