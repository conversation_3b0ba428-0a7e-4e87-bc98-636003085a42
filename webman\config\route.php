<?php
/**
 * This file is part of webman.
 *
 * Licensed under The MIT License
 * For full copyright and license information, please see the MIT-LICENSE.txt
 * Redistributions of files must retain the above copyright notice.
 *
 * <AUTHOR>
 * @copyright walkor<<EMAIL>>
 * @link      http://www.workerman.net/
 * @license   http://www.opensource.org/licenses/mit-license.php MIT License
 */

use Webman\Route;

Route::group('/passport', function() {
    // 不需要验证的路由
    Route::post('/login', [app\controller\PassportController::class, 'login']);
    Route::post('/logout', [app\controller\PassportController::class, 'logout']);
    Route::post('/refresh-token', [app\controller\PassportController::class, 'refreshToken']);
});
    
// 需要验证的路由
Route::group('', function() {
    
    // 应用中心路由
    Route::group('/system/apps', function () {
        Route::get('/logs', [app\controller\AppController::class, 'logs']);
        Route::get('', [app\controller\AppController::class, 'index']);
        Route::get('/{id}', [app\controller\AppController::class, 'detail']);
        Route::put('/{id}/settings', [app\controller\AppController::class, 'updateSettings']);
        // 配置模板相关路由
        Route::get('/{id}/templates', [app\controller\AppController::class, 'getTemplates']);
        Route::post('/{id}/templates', [app\controller\AppController::class, 'saveTemplate']);
        Route::delete('/{id}/templates/{templateId}', [app\controller\AppController::class, 'deleteTemplate']);
    })->middleware([
        app\middleware\JwtAuthMiddleware::class
    ]);
    
    // 字典管理路由
    Route::group('/system/dict', function() {
        Route::get('/list', [app\controller\system\DictionaryController::class, 'list']);
        Route::post('', [app\controller\system\DictionaryController::class, 'create']);
        Route::put('/{id}', [app\controller\system\DictionaryController::class, 'update']);
        Route::delete('/{id}', [app\controller\system\DictionaryController::class, 'delete']);
        
        // 字典项管理
        Route::get('/items', [app\controller\system\DictionaryController::class, 'items']);
        Route::get('/{dictId}/items', [app\controller\system\DictionaryController::class, 'items']);
        Route::post('/item', [app\controller\system\DictionaryController::class, 'createItem']);
        Route::put('/item/{id}', [app\controller\system\DictionaryController::class, 'updateItem']);
        Route::delete('/item/{id}', [app\controller\system\DictionaryController::class, 'deleteItem']);
    });
    
    // 定时任务路由
    Route::group('/system/schedule', function () {
        Route::get('/list', [app\controller\system\ScheduleController::class, 'list']);
        Route::post('/create', [app\controller\system\ScheduleController::class, 'create']);
        Route::post('/update/{id}', [app\controller\system\ScheduleController::class, 'update']);
        Route::post('/delete/{id}', [app\controller\system\ScheduleController::class, 'delete']);
        Route::get('/class-methods', [app\controller\system\ScheduleController::class, 'getClassMethods']);
        Route::get('/logs/{id}', [app\controller\system\ScheduleController::class, 'logs']);
        Route::get('/status', [app\controller\system\ScheduleController::class, 'status']);
        Route::post('/{id}/run', [app\controller\system\ScheduleController::class, 'run']);
        Route::post('/{id}/test-email', [app\controller\system\ScheduleController::class, 'testEmail']);
    })->middleware([
        app\middleware\JwtAuthMiddleware::class
    ]);
    
    // 角色管理路由
    Route::group('/api/system/role', function () {
        Route::get('/getList', [app\controller\system\RoleController::class, 'getList']);
        Route::post('/postCreate', [app\controller\system\RoleController::class, 'postCreate']);
        Route::put('/putUpdate/{id}', [app\controller\system\RoleController::class, 'putUpdate']);
        Route::delete('/deleteRole/{id}', [app\controller\system\RoleController::class, 'deleteRole']);
        Route::get('/getPermissions/{id}', [app\controller\system\RoleController::class, 'getPermissions']);
        Route::post('/permissions/{id}', [app\controller\system\RoleController::class, 'assignPermissions']);
    })->middleware([
        app\middleware\JwtAuthMiddleware::class
    ]);

    // 权限管理路由
    Route::group('/api/system/permission', function () {
        Route::get('/getList', [app\controller\system\PermissionController::class, 'getList']);
        Route::get('/getTree', [app\controller\system\PermissionController::class, 'getTree']);
        Route::post('/postCreate', [app\controller\system\PermissionController::class, 'postCreate']);
        Route::put('/putUpdate/{id}', [app\controller\system\PermissionController::class, 'putUpdate']);
        Route::delete('/deletePermission/{id}', [app\controller\system\PermissionController::class, 'deletePermission']);
        Route::get('/getUserPermissionTree', [app\controller\system\PermissionController::class, 'getUserPermissionTree']);
    })->middleware([
        app\middleware\JwtAuthMiddleware::class
    ]);
    
    // 成员管理路由
    Route::group('/member', function () {
        Route::get('/info', [app\controller\MemberController::class, 'info']);
        Route::get('/list', [app\controller\MemberController::class, 'list']);
        Route::post('/create', [app\controller\MemberController::class, 'create']);
        Route::put('/update/{id}', [app\controller\MemberController::class, 'update']);
        Route::delete('/delete/{id}', [app\controller\MemberController::class, 'delete']);
        Route::put('/password/{id}', [app\controller\MemberController::class, 'updatePassword']);
        Route::get('/getRoles/{id}', [app\controller\MemberController::class, 'getRoles']);
        Route::post('/assignRoles/{id}', [app\controller\MemberController::class, 'assignRoles']);
    })->middleware([
        app\middleware\JwtAuthMiddleware::class
    ]);
    
    // 商品管理相关路由
    Route::group('/products', function () {
        // 获取商品总数
        Route::get('/count', [app\controller\ProductController::class, 'count']);
        // 搜索商品
        Route::get('/search', [app\controller\ProductController::class, 'search']);
        // 获取商品列表
        Route::get('', [app\controller\ProductController::class, 'index']);
        // 创建商品
        Route::post('', [app\controller\ProductController::class, 'store']);
        // 更新商品
        Route::put('/{id}', [app\controller\ProductController::class, 'update']);
        // 删除商品
        Route::delete('/{id}', [app\controller\ProductController::class, 'destroy']);
    });

    // 商品分类相关路由
    Route::group('/product-categories', function () {
        // 获取分类列表
        Route::get('', [app\controller\ProductCategoryController::class, 'index']);
        // 创建分类
        Route::post('', [app\controller\ProductCategoryController::class, 'store']);
        // 更新分类
        Route::put('/{id}', [app\controller\ProductCategoryController::class, 'update']);
        // 删除分类
        Route::delete('/{id}', [app\controller\ProductCategoryController::class, 'destroy']);
    });
    
    // 渠道商品相关路由
    Route::group('/channel-products', function () {
        // 获取渠道商品列表
        Route::get('', [app\controller\ChannelProductController::class, 'index']);
        // 创建渠道商品
        Route::post('', [app\controller\ChannelProductController::class, 'store']);
        // 更新渠道商品
        Route::put('/{id}', [app\controller\ChannelProductController::class, 'update']);
        // 删除渠道商品
        Route::delete('/{id}', [app\controller\ChannelProductController::class, 'destroy']);
        // 获取渠道商品数量
        Route::get('/count', [app\controller\ChannelProductController::class, 'count']);
    });

    // 企业管理相关路由
    Route::group('/system/enterprise', function () {
        // 获取企业列表
        Route::get('', [app\controller\system\EnterpriseController::class, 'index']);
        // 创建企业
        Route::post('', [app\controller\system\EnterpriseController::class, 'store']);
        // 更新企业
        Route::put('/{id}', [app\controller\system\EnterpriseController::class, 'update']);
        // 删除企业
        Route::delete('/{id}', [app\controller\system\EnterpriseController::class, 'destroy']);
    });

    // 申报模板相关路由
    Route::group('/system/declaration-templates', function () {
        // 获取模板列表
        Route::get('', [app\controller\system\DeclarationTemplateController::class, 'index']);
        // 获取表单选项数据
        Route::get('/form-options', [app\controller\system\DeclarationTemplateController::class, 'getFormOptions']);
        // 创建模板
        Route::post('', [app\controller\system\DeclarationTemplateController::class, 'store']);
        // 更新模板
        Route::put('/{id}', [app\controller\system\DeclarationTemplateController::class, 'update']);
        // 删除模板
        Route::delete('/{id}', [app\controller\system\DeclarationTemplateController::class, 'destroy']);
    });
    
    // API密钥管理路由
    Route::group('/system/apikey', function () {
        Route::get('', [app\controller\system\ApiKeyController::class, 'index']);
        Route::post('', [app\controller\system\ApiKeyController::class, 'store']);
        Route::put('/{id}', [app\controller\system\ApiKeyController::class, 'update']);
        Route::delete('/{id}', [app\controller\system\ApiKeyController::class, 'destroy']);
        Route::post('/{id}/reset', [app\controller\system\ApiKeyController::class, 'resetSecret']);
    })->middleware([
        app\middleware\JwtAuthMiddleware::class
    ]);
    
    // 接口请求日志路由
    Route::group('/system/api-logs', function () {
        Route::get('', [app\controller\system\ApiRequestLogController::class, 'index']);
        Route::delete('/{id}', [app\controller\system\ApiRequestLogController::class, 'destroy']);
        Route::post('/clear', [app\controller\system\ApiRequestLogController::class, 'clear']);
    })->middleware([
        app\middleware\JwtAuthMiddleware::class
    ]);
    
    // 订单管理相关路由
    Route::group('/orders', function () {
        // 获取实时数据（静态路由放在前面）
        Route::get('/realtime-data', [app\controller\api\OrderController::class, 'getRealTimeData']);
        Route::get('/latest-orders', [app\controller\api\OrderController::class, 'getLatestOrders']);
        Route::get('/hot-products-trend', [app\controller\api\OrderController::class, 'getHotProductsWithTrend']);
        Route::get('/trend', [app\controller\api\OrderController::class, 'getTrend']);
        Route::get('/channel-stats', [app\controller\api\OrderController::class, 'getChannelStats']);
        Route::get('/stats', [app\controller\api\OrderController::class, 'getStats']);
        Route::get('/hot-products', [app\controller\api\OrderController::class, 'getHotProducts']);
        // 更新订单状态
        Route::post('/status', [app\controller\api\OrderController::class, 'updateStatus']);
        Route::delete('/{orderNo}', [app\controller\api\OrderController::class, 'destroy']);
        
        // 基础CRUD路由（动态路由放在后面）
        Route::get('', [app\controller\api\OrderController::class, 'index']);
    })->middleware([
        app\middleware\JwtAuthMiddleware::class
    ]);
    
    // API路由组
    Route::group('/open-api', function() {
        // 订单相关路由
        Route::group('/order', function () {
            // 创建订单
            Route::post('/create', [app\controller\api\OrderController::class, 'store']);
            // 更新订单
            Route::post('/update', [app\controller\api\OrderController::class, 'update']);
        })->middleware([
            app\middleware\ApiAuthMiddleware::class
        ]);

        // 179公告接口
        Route::post('/179', [app\controller\api\Customs179Controller::class, 'handle']);
        
    })->middleware([
        app\middleware\CorsMiddleware::class
    ]);
    
    // ... 其他需要验证的路由
})->middleware([app\middleware\JwtAuthMiddleware::class]);









