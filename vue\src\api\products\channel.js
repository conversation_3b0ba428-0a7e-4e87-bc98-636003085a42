import request from '@/utils/request'

// 获取渠道商品列表
export function getChannelProducts(params) {
  return request({
    url: '/channel-products',
    method: 'get',
    params
  })
}

// 创建渠道商品
export function createChannelProduct(data) {
  return request({
    url: '/channel-products',
    method: 'post',
    data
  })
}

// 更新渠道商品
export function updateChannelProduct(id, data) {
  return request({
    url: `/channel-products/${id}`,
    method: 'put',
    data
  })
}

// 删除渠道商品
export function deleteChannelProduct(id) {
  return request({
    url: `/channel-products/${id}`,
    method: 'delete'
  })
}

// 获取渠道商品数量
export function getChannelProductCount(params) {
  return request({
    url: '/channel-products/count',
    method: 'get',
    params
  })
} 