import request from '@/utils/request';

// 获取权限列表
export function getPermissionList() {
  return request({
    url: '/api/system/permission/getList',
    method: 'get'
  });
}

// 获取权限树
export function getPermissionTree() {
  return request({
    url: '/api/system/permission/getTree',
    method: 'get'
  });
}

// 获取用户权限树
export function getUserPermissionTree() {
  return request({
    url: '/api/system/permission/getUserPermissionTree',
    method: 'get'
  });
}

// 创建权限
export function createPermission(data) {
  return request({
    url: '/api/system/permission/postCreate',
    method: 'post',
    data
  });
}

// 更新权限
export function updatePermission(id, data) {
  return request({
    url: `/api/system/permission/putUpdate/${id}`,
    method: 'put',
    data
  });
}

// 删除权限
export function deletePermission(id) {
  return request({
    url: `/api/system/permission/deletePermission/${id}`,
    method: 'delete'
  });
} 