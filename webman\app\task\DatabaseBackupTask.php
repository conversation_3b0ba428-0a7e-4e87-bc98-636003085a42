<?php

namespace app\task;

/**
 * 数据库备份任务类
 * 提供数据库备份功能，支持邮件发送备份文件
 */
class DatabaseBackupTask
{
    /**
     * 执行数据库备份任务
     * 备份数据库并可选择发送邮件通知
     */
    public function taskBackupDatabase()
    {
        try {
            $config = config('database.connections.mysql');
            $backupDir = runtime_path() . '/backup';
            
            // 确保备份目录存在
            if (!is_dir($backupDir)) {
                mkdir($backupDir, 0755, true);
            }

            // 生成临时配置文件
            $cnfPath = $backupDir . '/.my.cnf';
            $cnfContent = sprintf(
                "[client]\nhost=%s\nport=%s\nuser=%s\npassword=%s\n",
                $config['host'],
                $config['port'],
                $config['username'],
                $config['password']
            );
            file_put_contents($cnfPath, $cnfContent);
            chmod($cnfPath, 0600);

            try {
                $timestamp = date('Y-m-d_His');
                $filename = $backupDir . "/backup_{$timestamp}.sql";
                
                // 执行mysqldump命令
                $command = sprintf(
                    'mysqldump --defaults-file=%s --single-transaction --routines --triggers %s > %s',
                    $cnfPath,
                    $config['database'],
                    $filename
                );

                $output = [];
                $returnVar = 0;
                exec($command . ' 2>&1', $output, $returnVar);

                // 删除临时配置文件
                unlink($cnfPath);

                if ($returnVar !== 0) {
                    throw new \Exception('数据库备份失败: ' . implode("\n", $output));
                }

                // 检查备份文件是否生成且大小大于0
                if (!file_exists($filename) || filesize($filename) === 0) {
                    throw new \Exception('备份文件生成失败或文件为空');
                }

                // 压缩备份文件
                $gzFilename = $filename . '.gz';
                $this->compressFile($filename, $gzFilename);

                // 删除原始SQL文件
                unlink($filename);

                // 清理旧的备份文件
                $this->cleanOldBackups($backupDir, 30);

                // 获取备份文件信息
                $fileSize = filesize($gzFilename);
                $fileSizeFormatted = $this->formatFileSize($fileSize);

                $message = sprintf(
                    '数据库备份成功完成！\n文件名：%s\n文件大小：%s\n备份时间：%s',
                    basename($gzFilename),
                    $fileSizeFormatted,
                    date('Y-m-d H:i:s')
                );

                return $message;

            } catch (\Exception $e) {
                // 确保删除临时配置文件
                if (file_exists($cnfPath)) {
                    unlink($cnfPath);
                }
                throw $e;
            }

        } catch (\Exception $e) {
            throw new \Exception('数据库备份任务执行失败: ' . $e->getMessage());
        }
    }

    /**
     * 执行数据库备份并发送邮件
     * 这个方法会自动将备份文件作为附件发送
     */
    public function taskBackupDatabaseWithEmail()
    {
        try {
            // 执行备份
            $result = $this->taskBackupDatabase();
            
            // 获取最新的备份文件
            $backupDir = runtime_path() . '/backup';
            $backupFiles = glob($backupDir . '/backup_*.sql.gz');
            
            if (empty($backupFiles)) {
                throw new \Exception('未找到备份文件');
            }
            
            // 获取最新的备份文件
            usort($backupFiles, function($a, $b) {
                return filemtime($b) - filemtime($a);
            });
            
            $latestBackupFile = $backupFiles[0];
            
            // 发送邮件通知
            $this->sendBackupNotification($latestBackupFile, $result);
            
            return $result . "\n邮件通知已发送";
            
        } catch (\Exception $e) {
            throw new \Exception('数据库备份邮件任务执行失败: ' . $e->getMessage());
        }
    }

    /**
     * 压缩文件
     */
    protected function compressFile($sourceFile, $targetFile)
    {
        $gz = gzopen($targetFile, 'w9');
        if (!$gz) {
            throw new \Exception('无法创建压缩文件');
        }

        $source = fopen($sourceFile, 'r');
        if (!$source) {
            gzclose($gz);
            throw new \Exception('无法读取源文件');
        }

        while (!feof($source)) {
            $chunk = fread($source, 8192);
            gzwrite($gz, $chunk);
        }

        fclose($source);
        gzclose($gz);
    }

    /**
     * 清理旧的备份文件
     */
    protected function cleanOldBackups($backupDir, $keepDays)
    {
        $files = glob($backupDir . '/backup_*.sql.gz');
        $now = time();
        
        foreach ($files as $file) {
            if (is_file($file)) {
                if ($now - filemtime($file) >= 86400 * $keepDays) {
                    unlink($file);
                }
            }
        }
    }

    /**
     * 发送备份通知邮件
     */
    protected function sendBackupNotification($backupFile, $result)
    {
        try {
            // 获取邮件服务
            $app = \app\model\App::where('code', 'email')
                                ->where('status', 1)
                                ->first();

            if (!$app) {
                throw new \Exception('邮件服务未配置或未启用');
            }

            $emailService = new \plugins\email\Service($app->id, $app->settings);

            // 构建邮件内容
            $subject = '【报关系统】数据库备份完成通知 - ' . date('Y-m-d');
            $body = $this->buildBackupEmailBody($backupFile, $result);
            
            // 获取收件人（可以从配置中读取或使用默认值）
            $recipients = $this->getBackupNotificationRecipients();
            
            if (empty($recipients)) {
                throw new \Exception('未配置备份通知收件人');
            }

            // 准备附件
            $attachments = [
                [
                    'path' => $backupFile,
                    'name' => basename($backupFile)
                ]
            ];

            // 发送邮件
            $emailService->sendMail($recipients, $subject, $body, true, $attachments);

        } catch (\Exception $e) {
            // 记录错误但不抛出异常，避免影响备份任务本身
            error_log("发送备份通知邮件失败: " . $e->getMessage());
        }
    }

    /**
     * 构建备份邮件内容
     */
    protected function buildBackupEmailBody($backupFile, $result)
    {
        $fileName = basename($backupFile);
        $fileSize = $this->formatFileSize(filesize($backupFile));
        $backupTime = date('Y-m-d H:i:s');

        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <div style='background: #f5f5f5; padding: 20px; border-radius: 8px;'>
                <h2 style='color: #333; margin-top: 0;'>🗄️ 数据库备份完成通知</h2>

                <div style='background: white; padding: 20px; border-radius: 6px; margin: 20px 0;'>
                    <div style='background: #e6f7ff; padding: 15px; border-radius: 6px; border-left: 4px solid #1890ff; margin-bottom: 20px;'>
                        <h3 style='color: #1890ff; margin: 0 0 10px 0;'>✅ 备份成功完成</h3>
                        <p style='margin: 0; color: #666;'>数据库备份已成功生成并压缩，备份文件已作为附件发送。</p>
                    </div>

                    <table style='width: 100%; border-collapse: collapse;'>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold; width: 120px;'>备份文件：</td>
                            <td style='padding: 8px 0;'>{$fileName}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold;'>文件大小：</td>
                            <td style='padding: 8px 0;'>{$fileSize}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold;'>备份时间：</td>
                            <td style='padding: 8px 0;'>{$backupTime}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold; vertical-align: top;'>执行结果：</td>
                            <td style='padding: 8px 0;'>
                                <pre style='background: #f8f8f8; padding: 10px; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word; margin: 0;'>{$result}</pre>
                            </td>
                        </tr>
                    </table>

                    <div style='background: #fff7e6; padding: 15px; border-radius: 6px; border-left: 4px solid #faad14; margin-top: 20px;'>
                        <h4 style='color: #faad14; margin: 0 0 10px 0;'>📎 附件说明</h4>
                        <p style='margin: 0; color: #666;'>备份文件已使用 gzip 压缩，请下载后解压使用。建议将备份文件保存到安全的位置。</p>
                    </div>
                </div>

                <div style='text-align: center; color: #666; font-size: 12px; margin-top: 20px;'>
                    <p>此邮件由报关系统自动发送，请勿回复</p>
                    <p>发送时间：{$backupTime}</p>
                </div>
            </div>
        </div>";
    }

    /**
     * 获取备份通知收件人列表
     */
    protected function getBackupNotificationRecipients()
    {
        // 可以从配置文件或数据库中读取
        // 这里返回一个示例配置，实际使用时应该从配置中读取
        return [
            // '<EMAIL>',
            // '<EMAIL>'
        ];
    }

    /**
     * 格式化文件大小
     */
    protected function formatFileSize($bytes)
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }
}
