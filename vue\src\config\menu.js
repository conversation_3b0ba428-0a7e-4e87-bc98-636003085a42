import { LogoutOutlined } from '@ant-design/icons-vue';

export const menuConfig = {
  // 退出登录菜单项
  logoutMenuItem: {
    key: 'logout',
    icon: LogoutOutlined,
    label: '退出'
  }
};

// 默认选中的菜单项
export const defaultSelectedKey = 'home';

// 获取路由路径对应的菜单项 key
export function getMenuKeyByPath(path, menuItems = []) {
  // 查找与当前路径完全匹配的菜单项
  const exactMatch = menuItems.find(item => item.path === path);
  if (exactMatch) {
    return exactMatch.key;
  }

  // 查找最接近的父级路径
  const closestMatch = menuItems
    .filter(item => path.startsWith(item.path))
    .sort((a, b) => b.path.length - a.path.length)[0];
  
  return closestMatch ? closestMatch.key : defaultSelectedKey;
}
