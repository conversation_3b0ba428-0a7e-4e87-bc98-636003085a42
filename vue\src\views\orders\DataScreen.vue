<template>
  <div class="data-screen" ref="screenRef" :class="{ fullscreen: isFullscreen }">
    <!-- 全屏按钮 -->
    <div class="fullscreen-btn" @click="toggleFullScreen">
      <FullscreenOutlined v-if="!isFullscreen" />
      <FullscreenExitOutlined v-else />
    </div>

    <!-- 顶部数据卡片 -->
    <a-row :gutter="[16, 16]" class="data-cards">
      <a-col :span="6">
        <div class="data-card">
          <div class="card-title">实时订单数</div>
          <div class="card-value">{{ formatNumber(animatedNumber.orderCount) }}</div>
          <div class="card-trend">
            <span class="trend-label">较昨日</span>
            <span :class="['trend-value', realTimeData.orderTrend > 0 ? 'up' : 'down']">
              {{ Math.abs(realTimeData.orderTrend) }}%
            </span>
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="data-card">
          <div class="card-title">实时销售额</div>
          <div class="card-value">¥{{ formatNumber(animatedNumber.salesAmount, 2) }}</div>
          <div class="card-trend">
            <span class="trend-label">较昨日</span>
            <span :class="['trend-value', realTimeData.salesTrend > 0 ? 'up' : 'down']">
              {{ Math.abs(realTimeData.salesTrend) }}%
            </span>
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="data-card">
          <div class="card-title">待处理订单</div>
          <div class="card-value">{{ realTimeData.pendingCount }}</div>
          <div class="card-trend warning" v-if="realTimeData.pendingCount > 100">
            <ExclamationCircleOutlined /> 订单积压
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="data-card">
          <div class="card-title">客单价</div>
          <div class="card-value">¥{{ realTimeData.averageAmount }}</div>
          <div class="card-trend">
            <span class="trend-label">较昨日</span>
            <span :class="['trend-value', realTimeData.averageTrend > 0 ? 'up' : 'down']">
              {{ Math.abs(realTimeData.averageTrend) }}%
            </span>
          </div>
        </div>
      </a-col>
    </a-row>

    <!-- 图表区域 -->
    <a-row :gutter="[16, 16]" class="chart-container">
      <!-- 左侧图表 -->
      <a-col :span="16">
        <div class="chart-card">
          <div class="chart-header">
            <h3>今日订单趋势</h3>
            <a-radio-group v-model:value="timeRange" button-style="solid" @change="handleTimeRangeChange">
              <a-radio-button value="hour">按小时</a-radio-button>
              <a-radio-button value="day">按天</a-radio-button>
              <a-radio-button value="week">按周</a-radio-button>
            </a-radio-group>
          </div>
          <div ref="trendChartRef" class="chart-content"></div>
        </div>
        
        <a-row :gutter="[16, 16]">
          <a-col :span="12">
            <div class="chart-card">
              <div class="chart-header">
                <h3>渠道订单分布</h3>
              </div>
              <div ref="channelChartRef" class="chart-content"></div>
            </div>
          </a-col>
          <a-col :span="12">
            <div class="chart-card">
              <div class="chart-header">
                <h3>订单状态分布</h3>
              </div>
              <div ref="statusChartRef" class="chart-content"></div>
            </div>
          </a-col>
        </a-row>
      </a-col>

      <!-- 右侧数据 -->
      <a-col :span="8">
        <div class="chart-card">
          <div class="chart-header">
            <h3>热销商品排行</h3>
            <a-radio-group v-model:value="rankType" button-style="solid" size="small">
              <a-radio-button value="sales">销量</a-radio-button>
              <a-radio-button value="amount">金额</a-radio-button>
            </a-radio-group>
          </div>
          <div class="rank-list">
            <div
              v-for="(item, index) in hotProducts"
              :key="item.name"
              class="rank-item"
            >
              <div class="rank-index" :class="{ 'top-3': index < 3 }">{{ index + 1 }}</div>
              <div class="rank-info">
                <div class="rank-name">{{ item.name }}</div>
                <div class="rank-value">{{ item.sales }}件</div>
              </div>
              <div class="rank-trend">
                <span :class="['trend-value', item.trend > 0 ? 'up' : 'down']">
                  {{ Math.abs(item.trend) }}%
                </span>
              </div>
            </div>
          </div>
        </div>

        <div class="chart-card">
          <div class="chart-header">
            <h3>实时订单</h3>
            <a-tag color="success">
              <SyncOutlined spin /> 实时更新中
            </a-tag>
          </div>
          <div class="realtime-list">
            <a-list
              :data-source="realtimeOrders"
              :pagination="false"
              size="small"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <div class="realtime-order">
                    <div class="order-time">{{ item.time }}</div>
                    <div class="order-info">
                      <div class="order-left">
                        <div class="order-no">{{ item.orderNo }}</div>
                        <div class="order-channel">
                          <a-tag :color="getChannelColor(item.channel)">{{ item.channel }}</a-tag>
                        </div>
                      </div>
                      <div class="order-amount">¥{{ item.amount }}</div>
                    </div>
                  </div>
                </a-list-item>
              </template>
            </a-list>
          </div>
        </div>

        <div class="chart-card">
          <div class="chart-header">
            <h3>异常订单预警</h3>
          </div>
          <div class="warning-list">
            <a-list
              :data-source="warningOrders"
              :pagination="false"
              size="small"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <div class="warning-item">
                    <ExclamationCircleOutlined class="warning-icon" />
                    <div class="warning-content">
                      <div class="warning-title">{{ item.title }}</div>
                      <div class="warning-desc">{{ item.description }}</div>
                      <div class="warning-time">{{ item.time }}</div>
                    </div>
                  </div>
                </a-list-item>
              </template>
            </a-list>
          </div>
        </div>
      </a-col>
    </a-row>

    <!-- 修改底部时间显示，只在全屏时显示 -->
    <div class="datetime-display-bottom" v-if="isFullscreen">
      <div class="time">{{ currentTime }}</div>
      <div class="date">{{ currentDate }}</div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { ExclamationCircleOutlined, FullscreenOutlined, FullscreenExitOutlined, SyncOutlined } from '@ant-design/icons-vue';
import * as echarts from 'echarts';
import { getRealTimeData, getLatestOrders, getHotProductsWithTrend, getTrend, getChannelStats } from '@/api/order';
import screenfull from 'screenfull';
import { useIntervalFn } from '@vueuse/core';
import gsap from 'gsap';

export default defineComponent({
  name: 'DataScreen',
  components: {
    ExclamationCircleOutlined,
    FullscreenOutlined,
    FullscreenExitOutlined,
    SyncOutlined
  },
  setup() {
    const screenRef = ref(null);
    const isFullscreen = ref(false);
    
    // 动画数字
    const animatedNumber = ref({
      orderCount: 0,
      salesAmount: 0,
      pendingCount: 0,
      averageAmount: 0
    });

    // 格式化数字
    const formatNumber = (num, decimals = 0) => {
      return Number(num).toFixed(decimals).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    };

    // 实时数据
    const realTimeData = ref({
      orderCount: 0,
      orderTrend: 0,
      salesAmount: 0,
      salesTrend: 0,
      pendingCount: 0,
      averageAmount: 0,
      averageTrend: 0
    });

    // 监听数据变化，执行动画
    watch(() => realTimeData.value, (newVal) => {
      gsap.to(animatedNumber.value, {
        duration: 2,
        orderCount: newVal.orderCount,
        salesAmount: newVal.salesAmount,
        pendingCount: newVal.pendingCount,
        averageAmount: newVal.averageAmount,
        ease: 'power1.out'
      });
    }, { deep: true });

    // 全屏切换
    const toggleFullScreen = () => {
      if (screenfull.isEnabled) {
        screenfull.toggle(screenRef.value);
        isFullscreen.value = !isFullscreen.value;
      }
    };

    // 监听全屏变化
    const handleFullscreenChange = () => {
      isFullscreen.value = screenfull.isFullscreen;
      // 根据全屏状态控制时间更新
      if (isFullscreen.value) {
        startTimeTimer();
      } else {
        if (timeTimer) {
          clearInterval(timeTimer);
          timeTimer = null;
        }
      }
    };

    // 时间范围选择
    const timeRange = ref('hour');

    // 监听时间范围变化
    watch(timeRange, () => {
      nextTick(() => {
        initTrendChart();
      });
    });

    // 图表实例
    let trendChart = null;
    let channelChart = null;
    const trendChartRef = ref(null);
    const channelChartRef = ref(null);

    // 优化 resize 事件处理
    const handleResize = () => {
      if (trendChart) {
        trendChart.resize();
      }
      if (channelChart) {
        channelChart.resize();
      }
      if (statusChart) {
        statusChart.resize();
      }
    };

    // 热销商品数据
    const hotProducts = ref([]);
    
    // 实时订单数据
    const realtimeOrders = ref([]);

    // 排行榜类型
    const rankType = ref('sales');
    
    // 异常订单数据
    const warningOrders = ref([]);
    
    // 状态图表实例
    let statusChart = null;
    const statusChartRef = ref(null);

    // 获取渠道标签颜色
    const getChannelColor = (channel) => {
      const colorMap = {
        '天猫': 'red',
        '京东': '#272343',
        '拼多多': 'pink'
      };
      return colorMap[channel] || 'blue';
    };

    // 监听排行榜类型变化
    watch(rankType, async () => {
      try {
        const res = await getHotProductsWithTrend({ type: rankType.value });
        if (res.code === 200) {
          hotProducts.value = res.data;
        }
      } catch (error) {
        console.error('获取热销商品失败:', error);
      }
    });

    // 获取实时数据
    const fetchRealTimeData = async () => {
      try {
        const res = await getRealTimeData();
        if (res.code === 200) {
          realTimeData.value = res.data;
        }
      } catch (error) {
        console.error('获取实时数据失败:', error);
      }
    };

    // 获取最新订单
    const fetchLatestOrders = async () => {
      try {
        const res = await getLatestOrders();
        if (res.code === 200) {
          realtimeOrders.value = res.data;
        }
      } catch (error) {
        console.error('获取最新订单失败:', error);
      }
    };

    // 获取热销商品
    const fetchHotProducts = async () => {
      try {
        const res = await getHotProductsWithTrend({ type: rankType.value });
        if (res.code === 200) {
          hotProducts.value = res.data;
        }
      } catch (error) {
        console.error('获取热销商品失败:', error);
      }
    };

    // 初始化趋势图表
    const initTrendChart = async () => {
      if (!trendChartRef.value) return;
      
      // 销毁现有实例
      if (trendChart) {
        trendChart.dispose();
        trendChart = null;
      }
      
      // 创建新实例
      trendChart = echarts.init(trendChartRef.value);
      
      try {
        const res = await getTrend({ type: timeRange.value });
        if (res.code === 200) {
          const option = {
            backgroundColor: 'transparent',
            tooltip: {
              trigger: 'axis',
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderColor: 'rgba(255, 255, 255, 0.1)',
              textStyle: {
                color: '#fff'
              }
            },
            legend: {
              data: res.data.channels,
              textStyle: {
                color: 'rgba(255, 255, 255, 0.65)'
              }
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: res.data.times,
              axisLine: {
                lineStyle: {
                  color: 'rgba(255, 255, 255, 0.1)'
                }
              },
              axisLabel: {
                color: 'rgba(255, 255, 255, 0.65)'
              }
            },
            yAxis: {
              type: 'value',
              axisLine: {
                lineStyle: {
                  color: 'rgba(255, 255, 255, 0.1)'
                }
              },
              splitLine: {
                lineStyle: {
                  color: 'rgba(255, 255, 255, 0.1)'
                }
              },
              axisLabel: {
                color: 'rgba(255, 255, 255, 0.65)'
              }
            },
            series: res.data.series.map(item => ({
              ...item,
              smooth: true,
              lineStyle: {
                width: 3
              },
              areaStyle: {
                opacity: 0.1
              }
            }))
          };
          trendChart.setOption(option);
        }
      } catch (error) {
        console.error('获取趋势数据失败:', error);
      }
    };

    // 初始化渠道分布图表
    const initChannelChart = async () => {
      if (!channelChartRef.value) return;
      
      // 销毁现有实例
      if (channelChart) {
        channelChart.dispose();
        channelChart = null;
      }
      
      // 创建新实例
      channelChart = echarts.init(channelChartRef.value);
      
      try {
        const res = await getChannelStats();
        if (res.code === 200) {
          const option = {
            tooltip: {
              trigger: 'item'
            },
            legend: {
              orient: 'vertical',
              left: 'left'
            },
            series: [
              {
                name: '渠道订单',
                type: 'pie',
                radius: ['40%', '70%'],
                avoidLabelOverlap: false,
                itemStyle: {
                  borderRadius: 10,
                  borderColor: '#fff',
                  borderWidth: 2
                },
                label: {
                  show: false,
                  position: 'center'
                },
                emphasis: {
                  label: {
                    show: true,
                    fontSize: '20',
                    fontWeight: 'bold'
                  }
                },
                labelLine: {
                  show: false
                },
                data: res.data
              }
            ]
          };
          channelChart.setOption(option);
        }
      } catch (error) {
        console.error('获取渠道统计失败:', error);
      }
    };

    // 初始化状态分布图表
    const initStatusChart = async () => {
      if (!statusChartRef.value) return;
      
      if (statusChart) {
        statusChart.dispose();
        statusChart = null;
      }
      
      statusChart = echarts.init(statusChartRef.value);
      
      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          right: 'right',
          textStyle: {
            color: 'rgba(255, 255, 255, 0.65)'
          }
        },
        series: [
          {
            name: '订单状态',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: 'rgba(255, 255, 255, 0.1)',
              borderWidth: 2
            },
            label: {
              show: true,
              color: '#fff'
            },
            data: [
              { value: 35, name: '待付款' },
              { value: 25, name: '待发货' },
              { value: 20, name: '已发货' },
              { value: 15, name: '已完成' },
              { value: 5, name: '已取消' }
            ]
          }
        ]
      };
      
      statusChart.setOption(option);
    };

    // 处理时间范围变化
    const handleTimeRangeChange = () => {
      initTrendChart();
    };

    // 定时刷新数据
    let refreshTimer = null;
    let timeTimer = null;
    const currentTime = ref('');
    const currentDate = ref('');

    const updateDateTime = () => {
      const now = new Date();
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
      currentTime.value = `${hours}:${minutes}:${seconds}.${milliseconds}`;
      currentDate.value = now.toLocaleDateString('zh-CN', { 
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
      });
    };

    // 启动时间更新定时器
    const startTimeTimer = () => {
      updateDateTime(); // 立即更新一次
      timeTimer = setInterval(updateDateTime, 13); // 每100ms更新一次时间
    };

    const startRefreshTimer = () => {
      refreshTimer = setInterval(() => {
        fetchRealTimeData();
        fetchLatestOrders();
        fetchHotProducts();
        initTrendChart();
        initChannelChart();
        initStatusChart();
      }, 5000);
    };

    onMounted(() => {
      // 初始化数据
      fetchRealTimeData();
      fetchLatestOrders();
      fetchHotProducts();
      
      // 等待 DOM 更新后初始化图表
      nextTick(() => {
        initTrendChart();
        initChannelChart();
        initStatusChart();
      });
      
      startRefreshTimer();

      window.addEventListener('resize', handleResize);

      if (screenfull.isEnabled) {
        screenfull.on('change', handleFullscreenChange);
      }
    });

    onUnmounted(() => {
      if (refreshTimer) {
        clearInterval(refreshTimer);
      }
      if (timeTimer) {
        clearInterval(timeTimer);
      }

      // 移除 resize 事件监听
      window.removeEventListener('resize', handleResize);

      // 销毁图表实例
      if (trendChart) {
        trendChart.dispose();
        trendChart = null;
      }
      if (channelChart) {
        channelChart.dispose();
        channelChart = null;
      }

      if (screenfull.isEnabled) {
        screenfull.off('change', handleFullscreenChange);
      }
    });

    return {
      screenRef,
      isFullscreen,
      animatedNumber,
      formatNumber,
      toggleFullScreen,
      realTimeData,
      timeRange,
      hotProducts,
      realtimeOrders,
      trendChartRef,
      channelChartRef,
      getChannelColor,
      handleTimeRangeChange,
      currentTime,
      currentDate,
      rankType,
      warningOrders,
      statusChartRef
    };
  }
});
</script>

<style lang="less" scoped>
.data-screen {
  position: relative;
  min-height: 100vh;
  padding: 40px;
  background: #0f1527;
  color: #fff;
  transition: all 0.3s;
  

  &.fullscreen {
    padding: 60px 40px 40px;
  }

  .fullscreen-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    cursor: pointer;
    z-index: 999;
    transition: all 0.3s;
    color: #fff;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }

    .anticon {
      font-size: 20px;
      color: #fff;
    }
  }

  .data-cards {
    margin-bottom: 24px;

    .data-card {
      background: rgba(255, 255, 255, 0.05);
      padding: 24px;
      border-radius: 8px;
      backdrop-filter: blur(10px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.3s;
      min-height: 180px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
      }

      .card-title {
        color: rgba(255, 255, 255, 0.6);
        font-size: 14px;
        margin-bottom: 12px;
      }

      .card-value {
        font-size: 32px;
        font-weight: bold;
        color: #fff;
        margin-bottom: 12px;
        font-family: DIN;
      }

      .card-trend {
        font-size: 13px;

        &.warning {
          color: #faad14;
        }

        .trend-label {
          color: rgba(255, 255, 255, 0.6);
          margin-right: 8px;
        }

        .trend-value {
          &.up {
            color: #52c41a;
            &::before {
              content: '↑';
              margin-right: 4px;
            }
          }
          &.down {
            color: #ff4d4f;
            &::before {
              content: '↓';
              margin-right: 4px;
            }
          }
        }
      }
    }
  }

  .chart-container {
    .chart-card {
      background: rgba(255, 255, 255, 0.05);
      padding: 24px;
      border-radius: 8px;
      backdrop-filter: blur(10px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.1);
      margin-bottom: 24px;
      min-height: 450px;

      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
          color: rgba(255, 255, 255, 0.85);
        }
      }

      .chart-content {
        height: 300px;
      }

      .rank-list {
        .rank-item {
          display: flex;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);

          &:last-child {
            border-bottom: none;
          }

          .rank-index {
            width: 24px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            margin-right: 16px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.85);

            &.top-3 {
              background: #1890ff;
              color: white;
            }
          }

          .rank-info {
            flex: 1;

            .rank-name {
              font-size: 14px;
              color: rgba(255, 255, 255, 0.85);
              margin-bottom: 4px;
            }

            .rank-value {
              font-size: 12px;
              color: rgba(255, 255, 255, 0.45);
            }
          }

          .rank-trend {
            .trend-value {
              font-size: 13px;

              &.up {
                color: #52c41a;
                &::before {
                  content: '↑';
                  margin-right: 4px;
                }
              }
              &.down {
                color: #ff4d4f;
                &::before {
                  content: '↓';
                  margin-right: 4px;
                }
              }
            }
          }
        }
      }

      .realtime-list {
        height: 300px;
        overflow-y: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
        
        &::-webkit-scrollbar {
          display: none;
        }

        :deep(.ant-list-item) {
          padding: 12px 0;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);

          &:last-child {
            border-bottom: none;
          }
        }

        .realtime-order {
          width: 100%;
          
          .order-time {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.45);
            margin-bottom: 4px;
          }

          .order-info {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .order-left {
              display: flex;
              align-items: center;
              gap: 8px;

              .order-no {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.85);
              }
            }

            .order-amount {
              font-weight: 500;
              color: rgba(255, 255, 255, 0.85);
            }
          }
        }
      }
    }
  }

  // 修改图表主题色
  :deep(.echarts-for-react) {
    background: transparent !important;
  }
}

// 暗色主题适配
:deep(.ant-radio-button-wrapper) {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.85);

  &:not(:first-child)::before {
    background-color: rgba(255, 255, 255, 0.2);
  }

  &:hover {
    color: #1890ff;
  }

  &-checked {
    background: #1890ff;
    border-color: #1890ff;
    color: #fff;

    &:hover {
      color: #fff;
    }
  }
}

.warning-list {
  height: 300px;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  
  &::-webkit-scrollbar {
    display: none;
  }

  .warning-item {
    display: flex;
    align-items: flex-start;
    padding: 12px 0;

    .warning-icon {
      font-size: 16px;
      color: #faad14;
      margin-right: 12px;
      margin-top: 2px;
    }

    .warning-content {
      flex: 1;

      .warning-title {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.85);
        margin-bottom: 4px;
      }

      .warning-desc {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.45);
        margin-bottom: 4px;
      }

      .warning-time {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.45);
      }
    }
  }
}

// 添加新的底部时间显示样式
.datetime-display-bottom {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  z-index: 999;
  background: rgba(0, 0, 0, 0.2);
  padding: 10px 20px;
  border-radius: 8px;
  backdrop-filter: blur(10px);

  .time {
    font-size: 32px;
    font-weight: bold;
    color: #fff;
    text-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    font-family: DIN;
  }

  .date {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.85);
    margin-top: 4px;
  }
}
</style>