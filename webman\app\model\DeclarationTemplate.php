<?php
namespace app\model;

use support\Model;

class DeclarationTemplate extends Model
{
    protected $table = 'declaration_templates';
    
    protected $fillable = [
        'name',
        'channel_id',
        'trade_mode',
        'platform_enterprise_id',
        'ecommerce_enterprise_id',
        'logistics_enterprise_id',
        'bonded_enterprise_id',
        'declare_enterprise_id',
        'transmission_enterprise_id',
        'declare_customs',
        'port_customs',
        'transport_mode',
        'departure_country',
        'account_book_no',
        'logistics_app_id',
        'logistics_template_id',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by'
    ];

    // 关联支付企业（包含应用信息）
    public function paymentEnterprises()
    {
        return $this->belongsToMany(Enterprise::class, 'declaration_template_payment_enterprises', 'template_id', 'enterprise_id')
            ->withPivot(['app_id', 'app_template_id'])
            ->withTimestamps();
    }

    // 关联物流应用
    public function logisticsApp()
    {
        return $this->belongsTo(App::class, 'logistics_app_id');
    }

    // 关联物流应用配置模板
    public function logisticsTemplate()
    {
        return $this->belongsTo(AppConfigTemplate::class, 'logistics_template_id');
    }

    // 关联电商平台企业
    public function platformEnterprise()
    {
        return $this->belongsTo(Enterprise::class, 'platform_enterprise_id');
    }

    // 关联电商企业
    public function ecommerceEnterprise()
    {
        return $this->belongsTo(Enterprise::class, 'ecommerce_enterprise_id');
    }

    // 关联物流企业
    public function logisticsEnterprise()
    {
        return $this->belongsTo(Enterprise::class, 'logistics_enterprise_id');
    }

    // 关联区内企业
    public function bondedEnterprise()
    {
        return $this->belongsTo(Enterprise::class, 'bonded_enterprise_id');
    }

    // 关联申报企业
    public function declareEnterprise()
    {
        return $this->belongsTo(Enterprise::class, 'declare_enterprise_id');
    }

    // 关联传输企业
    public function transmissionEnterprise()
    {
        return $this->belongsTo(Enterprise::class, 'transmission_enterprise_id');
    }

    // 关联渠道
    public function channel()
    {
        return $this->belongsTo(DictionaryItem::class, 'channel_id');
    }
} 