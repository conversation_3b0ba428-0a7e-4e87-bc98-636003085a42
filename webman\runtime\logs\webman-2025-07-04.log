[2025-07-04 12:26:11] default.ERROR: 127.0.0.1 POST localhost:8787/system/schedule/2/test-email
TypeError: plugins\email\Service::__construct(): Argument #2 ($config) must be of type array, null given, called in D:\2025\gaodux\报关系统\webman\app\model\Schedule.php on line 110 and defined in D:\2025\gaodux\报关系统\webman\plugins\email\src\Service.php:16
Stack trace:
#0 D:\2025\gaodux\报关系统\webman\app\model\Schedule.php(110): plugins\email\Service->__construct()
#1 D:\2025\gaodux\报关系统\webman\app\controller\system\ScheduleController.php(505): app\model\Schedule->sendTaskNotification()
#2 D:\2025\gaodux\报关系统\webman\vendor\workerman\webman-framework\src\App.php(336): app\controller\system\ScheduleController->testEmail()
#3 D:\2025\gaodux\报关系统\webman\vendor\workerman\webman-framework\src\App.php(359): Webman\App::Webman\{closure}()
#4 D:\2025\gaodux\报关系统\webman\app\middleware\JwtAuthMiddleware.php(76): Webman\App::Webman\{closure}()
#5 D:\2025\gaodux\报关系统\webman\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtAuthMiddleware->process()
#6 D:\2025\gaodux\报关系统\webman\app\middleware\JwtAuthMiddleware.php(76): Webman\App::Webman\{closure}()
#7 D:\2025\gaodux\报关系统\webman\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtAuthMiddleware->process()
#8 D:\2025\gaodux\报关系统\webman\app\middleware\OperationLogMiddleware.php(43): Webman\App::Webman\{closure}()
#9 D:\2025\gaodux\报关系统\webman\vendor\workerman\webman-framework\src\App.php(352): app\middleware\OperationLogMiddleware->process()
#10 D:\2025\gaodux\报关系统\webman\app\middleware\JwtAuthMiddleware.php(76): Webman\App::Webman\{closure}()
#11 D:\2025\gaodux\报关系统\webman\vendor\workerman\webman-framework\src\App.php(352): app\middleware\JwtAuthMiddleware->process()
#12 D:\2025\gaodux\报关系统\webman\app\middleware\CorsMiddleware.php(16): Webman\App::Webman\{closure}()
#13 D:\2025\gaodux\报关系统\webman\vendor\workerman\webman-framework\src\App.php(352): app\middleware\CorsMiddleware->process()
#14 D:\2025\gaodux\报关系统\webman\vendor\webman\rate-limiter\src\Limiter.php(109): Webman\App::Webman\{closure}()
#15 D:\2025\gaodux\报关系统\webman\vendor\workerman\webman-framework\src\App.php(352): Webman\RateLimiter\Limiter->process()
#16 D:\2025\gaodux\报关系统\webman\vendor\workerman\webman-framework\src\App.php(150): Webman\App::Webman\{closure}()
#17 D:\2025\gaodux\报关系统\webman\vendor\workerman\workerman\src\Connection\TcpConnection.php(676): Webman\App->onMessage()
#18 D:\2025\gaodux\报关系统\webman\vendor\workerman\workerman\src\Events\Select.php(408): Workerman\Connection\TcpConnection->baseRead()
#19 D:\2025\gaodux\报关系统\webman\vendor\workerman\workerman\src\Worker.php(1601): Workerman\Events\Select->run()
#20 D:\2025\gaodux\报关系统\webman\vendor\workerman\workerman\src\Worker.php(1526): Workerman\Worker::forkWorkersForWindows()
#21 D:\2025\gaodux\报关系统\webman\vendor\workerman\workerman\src\Worker.php(593): Workerman\Worker::forkWorkers()
#22 D:\2025\gaodux\报关系统\webman\runtime\windows\start_webman.php(33): Workerman\Worker::runAll()
#23 {main} [] []
