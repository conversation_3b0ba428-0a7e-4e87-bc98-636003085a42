<?php

namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;
use app\model\OperationLog;
use app\model\Member;
use support\Log;

class OperationLogMiddleware implements MiddlewareInterface
{
    // 不需要记录日志的路由
    protected $excludePaths = [
        '/api/login',
        '/api/logout',
        '/system/login-log/list',
        '/system/operation-log/list',
    ];

    // 不记录请求参数的路由（可能包含敏感信息）
    protected $hideParamsPaths = [
        '/api/member/update-password',
    ];

    /**
     * 处理请求
     * @param Request $request
     * @param callable $handler
     * @return Response
     */
    public function process(Request $request, callable $handler): Response
    {
        // 如果是排除的路径，直接处理请求
        if ($this->isExcludePath($request->path())) {
            return $handler($request);
        }

        // 记录开始时间
        $startTime = microtime(true);
        // 处理请求
        $response = $handler($request);

        // 如果不是 JSON 响应，不记录日志
        if (!$this->isJsonResponse($response)) {
            return $response;
        }


        try {
            // 获取当前登录用户
            $member = $request->member;
            if (!$member) {
                return $response;
            }
            
            $user = Member::where('id', $member->uid)->first();

            // 获取响应数据
            $responseData = json_decode($response->rawBody(), true);

            // IP地址
            $ip = $request->getRealIp();
            // 如果IP为127.0.0.1，则默认是本地
            if ($ip == '127.0.0.1') {
                $ip = '内网IP';
                $address = '内网';
            }else{
                // 获取IP地址
                $response = get('https://ip9.com.cn/get?ip=' . $ip);
                $response = json_decode($response, true);

                // 如果请求成功，则获取地址
                if ($response['ret'] == 200) {
                    $address = "【" . $response['data']['isp'] . "】" . $response['data']['country'] . $response['data']['prov'] . $response['data']['city'];
                }else{
                    $address = "【未知】" . $ip;
                }
            }

            // 准备日志数据
            $logData = [
                'member_id' => $member->uid,
                'username' => $user->username,
                'module' => $request->controller, //$this->getModule($request->path()),
                'action' => $request->action, //$this->getAction($request->path()),
                'method' => $request->method(),
                'url' => $request->url(),
                'params' => $this->getRequestParams($request),
                'ip' => $ip,
                'location' => $address, // 可以接入 IP 地理位置服务
                'browser' => $this->getBrowser($request),
                'os' => $this->getOS($request),
                'result' => $this->hideParamsPaths($request->path()) ? '***' : json_encode($responseData, JSON_UNESCAPED_UNICODE),
            ];

            // 记录日志
            OperationLog::record($logData);

        } catch (\Exception $e) {
            Log::error('记录操作日志失败: ' . $e->getMessage());
        }

        return $response;
    }

    /**
     * 判断是否是排除的路径
     * @param string $path
     * @return bool
     */
    protected function isExcludePath(string $path): bool
    {
        foreach ($this->excludePaths as $excludePath) {
            if (strpos($path, $excludePath) !== false) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否需要隐藏参数
     * @param string $path
     * @return bool
     */
    protected function hideParamsPaths(string $path): bool
    {
        foreach ($this->hideParamsPaths as $hidePath) {
            if (strpos($path, $hidePath) !== false) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取请求参数
     * @param Request $request
     * @return string
     */
    protected function getRequestParams(Request $request): string
    {
        $params = array_merge($request->get(), $request->post());
        
        // 如果是敏感路由，隐藏参数
        if ($this->hideParamsPaths($request->path())) {
            return '***';
        }

        // 过滤掉文件上传等二进制数据
        $params = array_filter($params, function ($value) {
            return !is_resource($value) && !is_object($value);
        });

        return json_encode($params, JSON_UNESCAPED_UNICODE);
    }

    /**
     * 获取模块名
     * @param string $path
     * @return string
     */
    protected function getModule(string $path): string
    {
        $parts = explode('/', trim($path, '/'));
        return isset($parts[1]) ? ucfirst($parts[1]) : 'Unknown';
    }

    /**
     * 获取操作名
     * @param string $path
     * @return string
     */
    protected function getAction(string $path): string
    {
        $parts = explode('/', trim($path, '/'));
        return isset($parts[2]) ? ucfirst($parts[2]) : 'Unknown';
    }

    /**
     * 判断是否是 JSON 响应
     * @param Response $response
     * @return bool
     */
    protected function isJsonResponse(Response $response): bool
    {
        $contentType = $response->getHeader('Content-Type');
        if (!$contentType) {
            return false;
        }
        
        if (is_array($contentType)) {
            $contentType = $contentType[0];
        }
        
        return strpos($contentType, 'application/json') !== false;
    }

    /**
     * 获取浏览器信息
     * @param Request $request
     * @return string
     */
    protected function getBrowser(Request $request): string
    {
        $userAgent = $request->header('user-agent');
        if (preg_match('/MSIE\s+([^\s;]+)/i', $userAgent, $matches)) {
            return "Internet Explorer {$matches[1]}";
        }
        if (preg_match('/Chrome\/([^\s;]+)/i', $userAgent, $matches)) {
            return "Chrome {$matches[1]}";
        }
        if (preg_match('/Firefox\/([^\s;]+)/i', $userAgent, $matches)) {
            return "Firefox {$matches[1]}";
        }
        if (preg_match('/Safari\/([^\s;]+)/i', $userAgent, $matches)) {
            return "Safari {$matches[1]}";
        }
        return $userAgent;
    }

    /**
     * 获取操作系统信息
     * @param Request $request
     * @return string
     */
    protected function getOS(Request $request): string
    {
        $userAgent = $request->header('user-agent');
        if (preg_match('/windows/i', $userAgent)) {
            return 'Windows';
        }
        if (preg_match('/linux/i', $userAgent)) {
            return 'Linux';
        }
        if (preg_match('/mac/i', $userAgent)) {
            return 'MacOS';
        }
        if (preg_match('/android/i', $userAgent)) {
            return 'Android';
        }
        if (preg_match('/iphone/i', $userAgent)) {
            return 'iOS';
        }
        return 'Unknown';
    }
} 