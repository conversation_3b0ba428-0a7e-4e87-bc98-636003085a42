<?php
namespace app\model;

use support\Model;

class Enterprise extends Model
{
    protected $table = 'enterprises';
    
    protected $fillable = [
        'customs_name',
        'customs_code',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by'
    ];

    // 关联企业类型
    public function types()
    {
        return $this->hasMany(EnterpriseType::class, 'enterprise_id')
            ->with('type');
    }
} 