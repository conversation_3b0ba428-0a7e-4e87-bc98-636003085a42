import { defineStore } from 'pinia';
import { login, logout } from '@/api/passport';
import { getUserInfo as getMemberInfo } from '@/api/user';
import { getUserPermissionTree } from '@/api/system/permission';

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: null,
    permissions: [] // 用户权限列表
  }),

  getters: {
    
  },

  actions: {
    // 设置token
    setToken(token) {
      this.token = token;
      localStorage.setItem('token', token);
    },

    // 设置用户信息
    setUserInfo(info) {
      this.userInfo = info;
    },

    // 设置权限列表
    setPermissions(permissions) {
      this.permissions = permissions;
    },

    // 检查是否有指定权限
    hasPermission(permission) {
      return this.permissions.includes(permission);
    },

    // 检查是否有任意一个权限
    hasAnyPermission(permissions) {
      return permissions.some(permission => this.permissions.includes(permission));
    },

    // 检查是否有全部权限
    hasAllPermissions(permissions) {
      return permissions.every(permission => this.permissions.includes(permission));
    },

    // 登录
    async login(loginData) {
      try {
        const response = await login(loginData);
        if (response.code === 200) {
          this.setToken(response.data.token);
          await this.getUserInfo();
          return true;
        }
        return false;
      } catch (error) {
        console.error('登录失败:', error);
        return false;
      }
    },

    // 获取用户信息
    async getUserInfo() {
      try {
        const response = await getMemberInfo();
        if (response.code === 200) {
          this.setUserInfo(response.data);
          // 获取用户权限列表
          await this.getPermissions();
          return true;
        }
        return false;
      } catch (error) {
        console.error('获取用户信息失败:', error);
        return false;
      }
    },

    // 获取用户权限列表
    async getPermissions() {
      try {
        const response = await getUserPermissionTree();
        if (response.code === 200) {
          // 递归提取所有权限码（包括菜单和按钮权限）
          const extractPermissions = (nodes) => {
            let permissions = [];
            nodes.forEach(node => {
              // 添加当前节点的权限
              if (node.permission_code) {
                permissions.push(node.permission_code);
              }
              
              // 处理子节点（按钮权限）
              if (node.children && node.children.length > 0) {
                node.children.forEach(child => {
                  if (child.permission_code) {
                    permissions.push(child.permission_code);
                  }
                });
              }
            });
            return permissions;
          };
          
          const permissions = extractPermissions(response.data);
          this.setPermissions(permissions);
          return true;
        }
        return false;
      } catch (error) {
        console.error('获取权限列表失败:', error);
        return false;
      }
    },

    // 登出
    async logout() {
      try {
        await logout();
        this.token = '';
        this.userInfo = null;
        this.permissions = [];
        localStorage.removeItem('token');
        return true;
      } catch (error) {
        console.error('登出失败:', error);
        return false;
      }
    }
  }
});
