<template>
  <system-page title="日志管理">
    <div class="system-log">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane key="login" tab="登录日志">
          <login-log v-if="activeTab === 'login'" />
        </a-tab-pane>
        <a-tab-pane key="operation" tab="操作日志">
          <operation-log v-if="activeTab === 'operation'" />
        </a-tab-pane>
        <a-tab-pane key="api" tab="接口请求日志">
          <api-log v-if="activeTab === 'api'" />
        </a-tab-pane>
      </a-tabs>
    </div>
  </system-page>
  
</template>

<script setup>
import { ref } from 'vue';
import LoginLog from './modules/login.vue';
import OperationLog from './modules/operation.vue';
import ApiLog from './modules/api.vue';
import SystemPage from '../components/SystemPage.vue';

// 当前激活的标签页
const activeTab = ref('login');

// 标签页切换处理
const handleTabChange = (key) => {
  activeTab.value = key;
};
</script>

<style scoped>
.system-log {
  background: #fff;
  padding: 24px;
  min-height: 100%;
}
</style> 