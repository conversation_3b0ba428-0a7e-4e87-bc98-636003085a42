<?php
namespace app\controller;

use app\model\ChannelProduct;
use support\Request;
use support\Response;

class ChannelProductController
{
    /**
     * 获取渠道商品列表
     */
    public function index(Request $request)
    {
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);
        $channelId = $request->input('channel_id');
        $channelProductName = $request->input('channel_product_name');
        $channelProductNo = $request->input('channel_product_no');

        $query = ChannelProduct::with(['product', 'channel']);

        if ($channelId) {
            $query->where('channel_id', $channelId);
        }
        if ($channelProductName) {
            $query->where('channel_product_name', 'like', "%{$channelProductName}%");
        }
        if ($channelProductNo) {
            $query->where('channel_product_no', 'like', "%{$channelProductNo}%");
        }

        $total = $query->count();
        $list = $query->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->orderBy('id', 'desc')
            ->get();

        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'list' => $list,
                'total' => $total
            ]
        ]);
    }

    /**
     * 创建渠道商品
     */
    public function store(Request $request)
    {
        $data = $request->post();
        
        // 验证数据
        if (empty($data['channel_id'])) {
            return json(['code' => 400, 'message' => '请选择渠道']);
        }
        if (empty($data['channel_product_name'])) {
            return json(['code' => 400, 'message' => '请输入渠道商品名称']);
        }
        if (empty($data['channel_product_no'])) {
            return json(['code' => 400, 'message' => '请输入渠道商品编码']);
        }
        if (empty($data['product_id'])) {
            return json(['code' => 400, 'message' => '请选择关联商品']);
        }

        // 检查商品编码是否已存在
        $exists = ChannelProduct::where('channel_id', $data['channel_id'])
            ->where('channel_product_no', $data['channel_product_no'])
            ->exists();
        if ($exists) {
            return json(['code' => 400, 'message' => '该渠道商品编码已存在']);
        }

        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');

        $channelProduct = ChannelProduct::create($data);

        return json([
            'code' => 200,
            'message' => '创建成功',
            'data' => $channelProduct
        ]);
    }

    /**
     * 更新渠道商品
     */
    public function update(Request $request, $id)
    {
        $data = $request->post();
        
        $channelProduct = ChannelProduct::find($id);
        if (!$channelProduct) {
            return json(['code' => 404, 'message' => '渠道商品不存在']);
        }

        // 验证数据
        if (empty($data['channel_product_name'])) {
            return json(['code' => 400, 'message' => '请输入渠道商品名称']);
        }
        if (empty($data['channel_product_no'])) {
            return json(['code' => 400, 'message' => '请输入渠道商品编码']);
        }
        if (empty($data['product_id'])) {
            return json(['code' => 400, 'message' => '请选择关联商品']);
        }

        // 检查商品编码是否已存在（排除自身）
        $exists = ChannelProduct::where('channel_id', $channelProduct->channel_id)
            ->where('channel_product_no', $data['channel_product_no'])
            ->where('id', '!=', $id)
            ->exists();
        if ($exists) {
            return json(['code' => 400, 'message' => '该渠道商品编码已存在']);
        }

        $data['updated_at'] = date('Y-m-d H:i:s');
        $channelProduct->update($data);

        return json([
            'code' => 200,
            'message' => '更新成功',
            'data' => $channelProduct
        ]);
    }

    /**
     * 删除渠道商品
     */
    public function destroy(Request $request, $id)
    {
        $channelProduct = ChannelProduct::find($id);
        if (!$channelProduct) {
            return json(['code' => 404, 'message' => '渠道商品不存在']);
        }

        $channelProduct->delete();

        return json([
            'code' => 200,
            'message' => '删除成功'
        ]);
    }

    /**
     * 获取渠道商品数量统计
     */
    public function count(Request $request)
    {
        $channelId = $request->input('channel_id');
        
        $query = ChannelProduct::query();
        if ($channelId) {
            $query->where('channel_id', $channelId);
        }
        
        $count = $query->count();
        
        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $count
        ]);
    }
} 