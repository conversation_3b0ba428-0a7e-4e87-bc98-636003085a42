<?php
namespace app\controller\api;

use support\Request;
use app\model\Order;
use app\model\OrderItem;
use app\model\OrderAddress;
use app\model\OrderCrossBorderDeclaration;
use app\model\DeclarationTemplate;
use app\model\Dictionary;
use app\model\DictionaryItem;
use support\Db;
use app\model\ApiRequestLog;

class OrderController
{
    /**
     * 获取渠道列表
     */
    private function getChannels()
    {
        $channelDict = Dictionary::where('code', 'channel')->first();
        if (!$channelDict) {
            return collect();
        }

        return DictionaryItem::where('dict_id', $channelDict->id)
            ->where('status', 1)
            ->orderBy('sort')
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'name' => $item->label,
                    'code' => $item->value
                ];
            });
    }

    /**
     * 创建订单
     */
    public function store(Request $request)
    {
        $data = $request->post();
        $response = null;
        $error = null;
        $template = null;
        try {
            // 验证基本参数（必填）
            if (empty($data['template_id'])) {
                $response = ['code' => 400, 'error_code' => 10001, 'message' => '请选择申报模板'];
                return json($response);
            }

            // 验证申报模板是否存在
            $template = DeclarationTemplate::find($data['template_id']);
            if (!$template) {
                $response = ['code' => 404, 'error_code' => 10002, 'message' => '申报模板不存在'];
                return json($response);
            }

            // 验证其他必填参数
            if (empty($data['order_no'])) {
                return json(['code' => 400, 'error_code' => 10003, 'message' => '缺少订单号']);
            }
            if (empty($data['order_time'])) {
                return json(['code' => 400, 'error_code' => 10004, 'message' => '缺少下单时间']);
            }

            if (empty($data['payment_method']) || !in_array($data['payment_method'], ['alipay', 'wechat'])) {
                return json(['code' => 400, 'error_code' => 10005, 'message' => '请提供正确的支付方式']);
            }
            if(empty($data['pay_amount']) || $data['pay_amount'] <= 0){
                return json(['code' => 400, 'error_code' => 10020, 'message' => '实际付款金额不能为空或小于0']);
            }
            if (empty($data['transaction_id'])) {
                return json(['code' => 400, 'error_code' => 10006, 'message' => '请提供支付流水号']);
            }
            if (empty($data['items']) || !is_array($data['items'])) {
                return json(['code' => 400, 'error_code' => 10007, 'message' => '请提供订单商品信息']);
            }
            if (empty($data['address'])) {
                return json(['code' => 400, 'error_code' => 10008, 'message' => '请提供订单地址信息']);
            }

            // 检查订单号是否已存在（基于渠道ID）
            $existingOrder = Order::where('channel_id', $template->channel_id)
                ->where('order_no', $data['order_no'])
                ->first();
            
            if ($existingOrder) {
                return json([
                    'code' => 400,
                    'error_code' => 10009,
                    'message' => "订单号 {$data['order_no']} 在该渠道下已存在，请检查后重试，若是更新订单请使用更新接口"
                ]);
            }

            // 验证商品信息
            foreach ($data['items'] as $item) {
                if (empty($item['channel_product_name'])) {
                    return json(['code' => 400, 'error_code' => 10010, 'message' => '请提供渠道商品名称']);
                }
                if (empty($item['channel_product_no'])) {
                    return json(['code' => 400, 'error_code' => 10011, 'message' => '请提供渠道商品编码']);
                }
                if (empty($item['unit_price']) || $item['unit_price'] <= 0) {
                    return json(['code' => 400, 'error_code' => 10012, 'message' => '请提供正确的商品单价']);
                }
                if (empty($item['quantity']) || $item['quantity'] <= 0) {
                    return json(['code' => 400, 'error_code' => 10013, 'message' => '请提供正确的商品数量']);
                }
            }

            // 验证地址信息
            $address = $data['address'];
            if (empty($address['receiver_name'])) {
                return json(['code' => 400, 'error_code' => 10014, 'message' => '请提供收件人姓名']);
            }
            if (empty($address['receiver_phone'])) {
                return json(['code' => 400, 'error_code' => 10015, 'message' => '请提供收件人手机号']);
            }
            if (empty($address['receiver_address'])) {
                return json(['code' => 400, 'error_code' => 10016, 'message' => '请提供收货地址']);
            }
            if (empty($address['buyer_name'])) {
                return json(['code' => 400, 'error_code' => 10017, 'message' => '请提供订购人姓名']);
            }
            if (empty($address['buyer_phone'])) {
                return json(['code' => 400, 'error_code' => 10018, 'message' => '请提供订购人手机号']);
            }
            if (empty($address['buyer_id_number'])) {
                return json(['code' => 400, 'error_code' => 10019, 'message' => '请提供订购人身份证号']);
            }

            // 开始事务
            Db::beginTransaction();
            try {
                // 计算订单金额
                $goodsAmount = 0;
                $items = [];
                foreach ($data['items'] as $item) {
                    // 根据渠道商品编码获取平台备案商品ID
                    $channelProduct = \app\model\ChannelProduct::where('channel_product_no', $item['channel_product_no'])
                        ->where('channel_id', $template->channel_id)
                        ->first();
                    
                    if (!$channelProduct) {
                        throw new \Exception("渠道商品编码 {$item['channel_product_no']} 未关联平台备案商品");
                    }

                    $totalPrice = $item['unit_price'] * $item['quantity'];
                    $goodsAmount += $totalPrice;
                    $items[] = [
                        'channel_product_name' => $item['channel_product_name'],
                        'channel_product_no' => $item['channel_product_no'],
                        'product_id' => $channelProduct->product_id,
                        'unit_price' => $item['unit_price'],
                        'quantity' => $item['quantity'],
                        'total_price' => $totalPrice
                    ];
                }

                // 计算实际支付金额
                $freightAmount = $data['freight_amount'] ?? 0;
                $discountAmount = $data['discount_amount'] ?? 0;
                $taxAmount = $data['tax_amount'] ?? 0;
                $payAmount = $goodsAmount + $freightAmount + $taxAmount - $discountAmount;

                // 判断实付金额与计算金额是否一致
                if($payAmount != $data['pay_amount']){
                    return json(['code' => 400, 'error_code' => 10021, 'message' => '实付金额需等于商品总金额+运杂费+税费-优惠金额']);
                }

                $orderNo = $data['order_no'];

                // 创建订单
                $order = Order::create([
                    'order_no' => $orderNo,
                    'template_id' => $data['template_id'],
                    'channel_id' => $template->channel_id,
                    'order_time' => $data['order_time'],
                    'goods_amount' => $goodsAmount,
                    'freight_amount' => $freightAmount,
                    'discount_amount' => $discountAmount,
                    'tax_amount' => $taxAmount,
                    'pay_amount' => $payAmount,
                    'payment_method' => $data['payment_method'],
                    'transaction_id' => $data['transaction_id'],
                    'order_status' => Order::STATUS_PAID,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);

                // 创建订单商品
                foreach ($items as $item) {
                    $item['order_id'] = $order->id;
                    $item['order_no'] = $orderNo;
                    $item['created_at'] = date('Y-m-d H:i:s');
                    $item['updated_at'] = date('Y-m-d H:i:s');
                    OrderItem::create($item);
                }

                // 创建订单地址
                $address['order_id'] = $order->id;
                $address['order_no'] = $orderNo;
                $address['created_at'] = date('Y-m-d H:i:s');
                $address['updated_at'] = date('Y-m-d H:i:s');
                OrderAddress::create($address);

                // 创建跨境申报信息（非必填）
                if (!empty($data['declaration'])) {
                    $declaration = $data['declaration'];
                    $declaration['order_id'] = $order->id;
                    $declaration['order_no'] = $orderNo;
                    $declaration['transaction_id'] = $data['transaction_id'];
                    $declaration['created_at'] = date('Y-m-d H:i:s');
                    $declaration['updated_at'] = date('Y-m-d H:i:s');
                    OrderCrossBorderDeclaration::create($declaration);
                }

                Db::commit();
                $response = [
                    'code' => 200,
                    'message' => '创建成功',
                    'data' => [
                        'order_no' => $orderNo
                    ]
                ];
                return json($response);
            } catch (\Exception $e) {
                Db::rollBack();
                $error = $e->getMessage();
                $response = [
                    'code' => 500,
                    'error_code' => 50001,
                    'message' => '创建失败：' . $e->getMessage()
                ];
                return json($response);
            } finally {
                // 记录接口请求日志
                ApiRequestLog::create([
                    'app_id' => null,
                    'template_id' => $template->id ?? null,
                    'method' => $request->method(),
                    'url' => $request->url(),
                    'headers' => $request->header(),
                    'request_data' => $request->all(),
                    'response_data' => $response,
                    'ip' => $request->getRealIp(),
                    'status' => $error ? 0 : 1,
                    'error_message' => $error,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '创建失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取订单详情
     */
    public function show(Request $request, $orderNo)
    {
        $order = Order::with(['items', 'address', 'declaration', 'template', 'channel'])
            ->where('order_no', $orderNo)
            ->first();

        if (!$order) {
            return json(['code' => 404, 'message' => '订单不存在']);
        }

        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $order
        ]);
    }

    /**
     * 更新订单状态
     */
    public function updateStatus(Request $request)
    {
        $data = $request->post();
        $order = Order::where('order_no', $data['order_no'])->first();
        if (!$order) {
            return json(['code' => 404, 'error_code' => 20001, 'message' => '订单不存在']);
        }
        $order->order_status = $data['status'];
        $order->updated_at = date('Y-m-d H:i:s');
        $order->save();

        return json(['code' => 200, 'message' => '更新成功', 'data' => $order]);
    }

    /**
     * 删除订单（仅限已取消状态）
     */
    public function destroy(Request $request, $orderNo)
    {
        $order = Order::where('order_no', $orderNo)->first();
        if (!$order) {
            return json(['code' => 404, 'message' => '订单不存在']);
        }
        if ($order->order_status !== Order::STATUS_CANCELLED) {
            return json(['code' => 400, 'message' => '仅已取消的订单可删除']);
        }
        // 级联删除订单相关信息
        \app\model\OrderItem::where('order_id', $order->id)->delete();
        \app\model\OrderAddress::where('order_id', $order->id)->delete();
        \app\model\OrderCrossBorderDeclaration::where('order_id', $order->id)->delete();
        \app\model\OrderMessage::where('order_id', $order->id)->delete();
        $order->delete();
        return json(['code' => 200, 'message' => '删除成功']);
    }

    /**
     * 更新订单信息
     */
    public function update(Request $request)
    {
        $data = $request->post();
        $response = null;
        $error = null;
        $order = Order::where('order_no', $data['order_no'])->first();
        try {
            if (!$order) {
                $response = ['code' => 404, 'error_code' => 20001, 'message' => '订单不存在'];
                return json($response);
            }
            
            Db::beginTransaction();
            
            // 更新金额信息
            $updateData = [];
            if (isset($data['freight_amount'])) {
                if (!is_numeric($data['freight_amount']) || $data['freight_amount'] < 0) {
                    return json(['code' => 400, 'error_code' => 20005, 'message' => '无效的运杂费金额']);
                }
                $updateData['freight_amount'] = $data['freight_amount'];
            }
            if (isset($data['discount_amount'])) {
                if (!is_numeric($data['discount_amount']) || $data['discount_amount'] < 0) {
                    return json(['code' => 400, 'error_code' => 20006, 'message' => '无效的优惠金额']);
                }
                $updateData['discount_amount'] = $data['discount_amount'];
            }
            if (isset($data['tax_amount'])) {
                if (!is_numeric($data['tax_amount']) || $data['tax_amount'] < 0) {
                    return json(['code' => 400, 'error_code' => 20007, 'message' => '无效的税费金额']);
                }
                $updateData['tax_amount'] = $data['tax_amount'];
            }

            // 如果有金额更新，重新计算支付金额
            if (!empty($updateData)) {
                $order->refresh();
                $updateData['pay_amount'] = $order->goods_amount + 
                    ($updateData['freight_amount'] ?? $order->freight_amount) + 
                    ($updateData['tax_amount'] ?? $order->tax_amount) - 
                    ($updateData['discount_amount'] ?? $order->discount_amount);
                $updateData['updated_at'] = date('Y-m-d H:i:s');
                $order->update($updateData);
            }

            // 更新地址信息
            if (!empty($data['address'])) {
                $address = $data['address'];
                $addressData = [];

                if (isset($address['receiver_name'])) {
                    if (empty($address['receiver_name'])) {
                        return json(['code' => 400, 'error_code' => 20008, 'message' => '收件人姓名不能为空']);
                    }
                    $addressData['receiver_name'] = $address['receiver_name'];
                }

                if (isset($address['receiver_phone'])) {
                    if (empty($address['receiver_phone'])) {
                        return json(['code' => 400, 'error_code' => 20009, 'message' => '收件人手机号不能为空']);
                    }
                    $addressData['receiver_phone'] = $address['receiver_phone'];
                }

                if (isset($address['receiver_address'])) {
                    if (empty($address['receiver_address'])) {
                        return json(['code' => 400, 'error_code' => 20010, 'message' => '收货地址不能为空']);
                    }
                    $addressData['receiver_address'] = $address['receiver_address'];
                }

                if (isset($address['buyer_name'])) {
                    if (empty($address['buyer_name'])) {
                        return json(['code' => 400, 'error_code' => 20011, 'message' => '订购人姓名不能为空']);
                    }
                    $addressData['buyer_name'] = $address['buyer_name'];
                }

                if (isset($address['buyer_phone'])) {
                    if (empty($address['buyer_phone'])) {
                        return json(['code' => 400, 'error_code' => 20012, 'message' => '订购人手机号不能为空']);
                    }
                    $addressData['buyer_phone'] = $address['buyer_phone'];
                }

                if (isset($address['buyer_id_number'])) {
                    if (empty($address['buyer_id_number'])) {
                        return json(['code' => 400, 'error_code' => 20013, 'message' => '订购人身份证号不能为空']);
                    }
                    $addressData['buyer_id_number'] = $address['buyer_id_number'];
                }

                if (!empty($addressData)) {
                    $addressData['updated_at'] = date('Y-m-d H:i:s');
                    OrderAddress::where('order_id', $order->id)->update($addressData);
                }
            }

            Db::commit();
            $response = [
                'code' => 200,
                'message' => '更新成功'
            ];
            return json($response);
        } catch (\Exception $e) {
            Db::rollBack();
            $error = $e->getMessage();
            $response = [
                'code' => 500,
                'error_code' => 50001,
                'message' => '更新失败：' . $e->getMessage()
            ];
            return json($response);
        } finally {
            // 记录接口请求日志
            ApiRequestLog::create([
                'app_id' => null,
                'template_id' => $order->template_id ?? null,
                'method' => $request->method(),
                'url' => $request->url(),
                'headers' => $request->header(),
                'request_data' => $request->all(),
                'response_data' => $response,
                'ip' => $request->getRealIp(),
                'status' => $error ? 0 : 1,
                'error_message' => $error,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }
    }

    /**
     * 获取订单列表
     */
    public function index(Request $request)
    {
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);
        $orderNo = $request->input('order_no');
        $expressNo = $request->input('express_no');
        $templateId = $request->input('template_id');
        $orderStatus = $request->input('order_status');
        $startTime = $request->input('start_time');
        $endTime = $request->input('end_time');
        $paymentMethod = $request->input('payment_method');
        $isTest = $request->input('is_test');

        $query = Order::with(['template', 'items', 'address']);

        // 条件筛选
        if ($orderNo) {
            $query->where('order_no', 'like', "%{$orderNo}%");
        }
        if ($expressNo) {
            $query->where('express_no', 'like', "%{$expressNo}%");
        }
        if ($templateId) {
            $query->where('template_id', $templateId);
        }
        if ($orderStatus) {
            $query->where('order_status', $orderStatus);
        }
        if ($startTime && $endTime) {
            $query->whereBetween('order_time', [$startTime, $endTime]);
        }
        if ($paymentMethod) {
            $query->where('payment_method', $paymentMethod);
        }
        if ($isTest !== null) {
            $query->where('is_test', $isTest);
        }

        // 获取总数
        $total = $query->count();

        // 获取分页数据
        $list = $query->orderBy('id', 'desc')
            ->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->get();

        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'list' => $list,
                'total' => $total
            ]
        ]);
    }

    /**
     * 获取订单统计数据
     */
    public function getStats(Request $request)
    {
        try {
            // 获取今日订单数据
            $todayStart = date('Y-m-d 00:00:00');
            $todayEnd = date('Y-m-d 23:59:59');
            $yesterdayStart = date('Y-m-d 00:00:00', strtotime('-1 day'));
            $yesterdayEnd = date('Y-m-d 23:59:59', strtotime('-1 day'));

            // 今日订单数和销售额
            $todayOrders = Order::whereBetween('created_at', [$todayStart, $todayEnd])->get();
            $todayOrderCount = $todayOrders->count();
            $todaySalesAmount = $todayOrders->sum('pay_amount');

            // 昨日订单数和销售额
            $yesterdayOrders = Order::whereBetween('created_at', [$yesterdayStart, $yesterdayEnd])->get();
            $yesterdayOrderCount = $yesterdayOrders->count();
            $yesterdaySalesAmount = $yesterdayOrders->sum('pay_amount');

            // 计算环比增长
            $orderTrend = $yesterdayOrderCount > 0 ? 
                round(($todayOrderCount - $yesterdayOrderCount) / $yesterdayOrderCount * 100, 2) : 0;
            $salesTrend = $yesterdaySalesAmount > 0 ? 
                round(($todaySalesAmount - $yesterdaySalesAmount) / $yesterdaySalesAmount * 100, 2) : 0;

            // 获取本月数据
            $monthStart = date('Y-m-01 00:00:00');
            $monthEnd = date('Y-m-d 23:59:59');
            $lastMonthStart = date('Y-m-01 00:00:00', strtotime('-1 month'));
            $lastMonthEnd = date('Y-m-t 23:59:59', strtotime('-1 month'));

            // 本月订单数和销售额
            $monthOrders = Order::whereBetween('created_at', [$monthStart, $monthEnd])->get();
            $monthOrderCount = $monthOrders->count();
            $monthSalesAmount = $monthOrders->sum('pay_amount');

            // 上月订单数和销售额
            $lastMonthOrders = Order::whereBetween('created_at', [$lastMonthStart, $lastMonthEnd])->get();
            $lastMonthOrderCount = $lastMonthOrders->count();
            $lastMonthSalesAmount = $lastMonthOrders->sum('pay_amount');

            // 计算环比增长
            $monthOrderTrend = $lastMonthOrderCount > 0 ? 
                round(($monthOrderCount - $lastMonthOrderCount) / $lastMonthOrderCount * 100, 2) : 0;
            $monthSalesTrend = $lastMonthSalesAmount > 0 ? 
                round(($monthSalesAmount - $lastMonthSalesAmount) / $lastMonthSalesAmount * 100, 2) : 0;

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'today' => [
                        'orderCount' => $todayOrderCount,
                        'orderTrend' => $orderTrend,
                        'salesAmount' => $todaySalesAmount,
                        'salesTrend' => $salesTrend
                    ],
                    'month' => [
                        'orderCount' => $monthOrderCount,
                        'orderTrend' => $monthOrderTrend,
                        'salesAmount' => $monthSalesAmount,
                        'salesTrend' => $monthSalesTrend
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取统计数据失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取订单趋势数据
     */
    public function getTrend(Request $request)
    {
        try {
            $type = $request->input('type', 'hour');
            $channels = $this->getChannels();
            $channelNames = $channels->pluck('name')->toArray();
            
            // 根据类型确定时间范围和格式
            switch ($type) {
                case 'hour':
                    $start = date('Y-m-d H:00:00', strtotime('-23 hours'));
                    $end = date('Y-m-d H:59:59');
                    $format = 'H:i';
                    $interval = '1 hour';
                    break;
                case 'day':
                    $start = date('Y-m-d 00:00:00', strtotime('-29 days'));
                    $end = date('Y-m-d 23:59:59');
                    $format = 'm-d';
                    $interval = '1 day';
                    break;
                case 'month':
                    $start = date('Y-m-01 00:00:00', strtotime('-11 months'));
                    $end = date('Y-m-t 23:59:59');
                    $format = 'Y-m';
                    $interval = '1 month';
                    break;
                default:
                    return json(['code' => 400, 'message' => '无效的时间类型']);
            }

            // 生成时间点
            $times = [];
            $current = strtotime($start);
            while ($current <= strtotime($end)) {
                $times[] = date($format, $current);
                $current = strtotime("+{$interval}", $current);
            }

            // 获取各渠道订单数据
            $series = [];
            foreach ($channels as $channel) {
                $data = [];
                $current = strtotime($start);
                while ($current <= strtotime($end)) {
                    $periodStart = date('Y-m-d H:i:s', $current);
                    $periodEnd = date('Y-m-d H:i:s', strtotime("+{$interval}", $current) - 1);
                    
                    $count = Order::where('channel_id', $channel['id'])
                        ->whereBetween('created_at', [$periodStart, $periodEnd])
                        ->count();
                    
                    $data[] = $count;
                    $current = strtotime("+{$interval}", $current);
                }

                $series[] = [
                    'name' => $channel['name'],
                    'type' => 'line',
                    'data' => $data
                ];
            }

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'times' => $times,
                    'channels' => $channelNames,
                    'series' => $series
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取趋势数据失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取渠道订单占比
     */
    public function getChannelStats(Request $request)
    {
        try {
            $channels = $this->getChannels();
            $data = [];
            
            foreach ($channels as $channel) {
                $orderCount = Order::where('channel_id', $channel['id'])->count();
                if ($orderCount > 0) {
                    $data[] = [
                        'name' => $channel['name'],
                        'value' => $orderCount
                    ];
                }
            }

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取渠道统计失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取热销商品排行
     */
    public function getHotProducts(Request $request)
    {
        try {
            $channels = $this->getChannels();
            $result = [];

            foreach ($channels as $channel) {
                // 获取该渠道下的热销商品
                $hotProducts = OrderItem::join('orders', 'order_items.order_id', '=', 'orders.id')
                    ->where('orders.channel_id', $channel['id'])
                    ->select('order_items.channel_product_name')
                    ->selectRaw('SUM(quantity) as sales')
                    ->groupBy('order_items.channel_product_name')
                    ->orderBy('sales', 'desc')
                    ->limit(10)
                    ->get()
                    ->map(function ($item) {
                        return [
                            'name' => $item->channel_product_name,
                            'sales' => $item->sales
                        ];
                    });

                if ($hotProducts->isNotEmpty()) {
                    $result[] = [
                        'name' => $channel['name'],
                        'products' => $hotProducts
                    ];
                }
            }

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取热销商品失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取实时数据
     */
    public function getRealTimeData(Request $request)
    {
        try {
            // 获取今日数据
            $todayStart = date('Y-m-d 00:00:00');
            $todayEnd = date('Y-m-d 23:59:59');
            $yesterdayStart = date('Y-m-d 00:00:00', strtotime('-1 day'));
            $yesterdayEnd = date('Y-m-d 23:59:59', strtotime('-1 day'));

            // 今日订单数和销售额
            $todayOrders = Order::whereBetween('created_at', [$todayStart, $todayEnd])->get();
            $todayOrderCount = $todayOrders->count();
            $todaySalesAmount = $todayOrders->sum('pay_amount');

            // 昨日订单数和销售额
            $yesterdayOrders = Order::whereBetween('created_at', [$yesterdayStart, $yesterdayEnd])->get();
            $yesterdayOrderCount = $yesterdayOrders->count();
            $yesterdaySalesAmount = $yesterdayOrders->sum('pay_amount');

            // 计算环比增长
            $orderTrend = $yesterdayOrderCount > 0 ? 
                round(($todayOrderCount - $yesterdayOrderCount) / $yesterdayOrderCount * 100, 2) : 0;
            $salesTrend = $yesterdaySalesAmount > 0 ? 
                round(($todaySalesAmount - $yesterdaySalesAmount) / $yesterdaySalesAmount * 100, 2) : 0;

            // 待处理订单数（已支付未发货的订单）
            $pendingCount = Order::where('order_status', Order::STATUS_PAID)->count();

            // 计算客单价
            $todayAverageAmount = $todayOrderCount > 0 ? round($todaySalesAmount / $todayOrderCount, 2) : 0;
            $yesterdayAverageAmount = $yesterdayOrderCount > 0 ? round($yesterdaySalesAmount / $yesterdayOrderCount, 2) : 0;
            $averageTrend = $yesterdayAverageAmount > 0 ? 
                round(($todayAverageAmount - $yesterdayAverageAmount) / $yesterdayAverageAmount * 100, 2) : 0;

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'orderCount' => $todayOrderCount,
                    'orderTrend' => $orderTrend,
                    'salesAmount' => $todaySalesAmount,
                    'salesTrend' => $salesTrend,
                    'pendingCount' => $pendingCount,
                    'averageAmount' => $todayAverageAmount,
                    'averageTrend' => $averageTrend
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取实时数据失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取最新订单列表
     */
    public function getLatestOrders(Request $request)
    {
        try {
            $limit = $request->input('limit', 5);
            
            $orders = Order::with(['channel'])
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get()
                ->map(function ($order) {
                    return [
                        'time' => date('H:i:s', strtotime($order->created_at)),
                        'channel' => $order->channel ? $order->channel->label : '未知渠道',
                        'amount' => $order->pay_amount,
                        'orderNo' => $order->order_no
                    ];
                });

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $orders
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取最新订单失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取热销商品排行（带趋势）
     */
    public function getHotProductsWithTrend(Request $request)
    {
        try {
            // 获取今日和昨日的时间范围
            $todayStart = date('Y-m-d 00:00:00');
            $todayEnd = date('Y-m-d 23:59:59');
            $yesterdayStart = date('Y-m-d 00:00:00', strtotime('-1 day'));
            $yesterdayEnd = date('Y-m-d 23:59:59', strtotime('-1 day'));

            // 获取今日热销商品
            $todayHotProducts = OrderItem::join('orders', 'order_items.order_id', '=', 'orders.id')
                ->whereBetween('orders.created_at', [$todayStart, $todayEnd])
                ->select('channel_product_name')
                ->selectRaw('SUM(quantity) as sales')
                ->groupBy('channel_product_name')
                ->orderBy('sales', 'desc')
                ->limit(8)
                ->get();

            // 获取昨日同商品销量
            $result = [];
            foreach ($todayHotProducts as $product) {
                $yesterdaySales = OrderItem::join('orders', 'order_items.order_id', '=', 'orders.id')
                    ->whereBetween('orders.created_at', [$yesterdayStart, $yesterdayEnd])
                    ->where('channel_product_name', $product->channel_product_name)
                    ->sum('quantity');

                // 计算环比增长
                $trend = $yesterdaySales > 0 ? 
                    round(($product->sales - $yesterdaySales) / $yesterdaySales * 100, 2) : 0;

                $result[] = [
                    'name' => $product->channel_product_name,
                    'sales' => $product->sales,
                    'trend' => $trend
                ];
            }

            return json([
                'code' => 200,
                'message' => '获取成功',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取热销商品失败：' . $e->getMessage()
            ]);
        }
    }
} 