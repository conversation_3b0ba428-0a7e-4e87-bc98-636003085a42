<template>
  <a-modal
    :title="`字典项管理 - ${dictName}`"
    :open="visible"
    @cancel="handleCancel"
    :footer="null"
    width="800px"
    :zIndex="10"
  >
    <div class="dict-items-container">
      <!-- 操作按钮 -->
      <div class="table-operations">
        <a-row :gutter="[16, 16]">
          <a-col :span="16">
            <a-input-search
              v-model:value="searchValue"
              placeholder="请输入字典标签或字典键值"
              enter-button
              @search="onSearch"
              style="width: 300px"
            />
            <!-- 重置 -->
            <a-button @click="resetSearch">
              <template #icon><reload-outlined /></template>
              重置
            </a-button>
          </a-col>
          <a-col :span="8" style="text-align: right">
            <a-button type="primary" @click="showAddModal">
              <template #icon><PlusOutlined /></template>
              新增字典项
            </a-button>
          </a-col>
        </a-row>
      </div>

      <!-- 字典项列表 -->
      <a-table
        :columns="columns"
        :data-source="itemList"
        :loading="loading"
        :pagination="{ 
          total: total,
          current: current,
          pageSize: pageSize,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: total => `共 ${total} 条`
        }"
        @change="handleTableChange"
      >
        <!-- 状态列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status ? 'success' : 'error'">
              {{ record.status ? '启用' : '禁用' }}
            </a-tag>
          </template>
          
          <!-- 操作列 -->
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="showEditModal(record)">
                <template #icon><EditOutlined /></template>
                编辑
              </a-button>
              <!-- <a-popconfirm
                title="确定要删除此字典项吗？"
                @confirm="handleDelete(record.id)"
                okText="确定"
                cancelText="取消"
              >
                <a-button type="link" danger>
                  <template #icon><DeleteOutlined /></template>
                  删除
                </a-button>
              </a-popconfirm> -->
              <a-button type="link" danger @click="confirmDelete(record.id)">
                <template #icon><DeleteOutlined /></template>
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 字典项表单模态框 -->
      <a-modal
        :title="modalTitle"
        :open="modalVisible"
        @ok="handleModalOk"
        @cancel="handleModalCancel"
        :confirmLoading="modalLoading"
        okText="确定"
        cancelText="取消"
      >
        <a-form
          ref="formRef"
          :model="formState"
          :rules="rules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="标签" name="label">
            <a-input v-model:value="formState.label" placeholder="请输入标签" />
          </a-form-item>
          <a-form-item label="键值" name="value">
            <a-input v-model:value="formState.value" placeholder="请输入键值" />
          </a-form-item>
          <a-form-item label="排序" name="sort">
            <a-input-number v-model:value="formState.sort" :min="0" style="width: 100%" />
          </a-form-item>
          <a-form-item label="状态" name="status">
            <a-switch v-model:checked="formState.status" checked-children="启用" un-checked-children="关闭"/>
          </a-form-item>
          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="formState.remark" placeholder="请输入备注信息" :rows="4" />
          </a-form-item>
        </a-form>
      </a-modal>
    </div>
  </a-modal>
</template>

<script>
import { ref, reactive, watch } from 'vue';
import { message, Modal } from 'ant-design-vue';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue';
import {
  getDictItems,
  createDictItem,
  updateDictItem,
  deleteDictItem
} from '@/api/system/dict';

export default {
  name: 'DictItemsModal',
  components: {
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    ReloadOutlined
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dictId: {
      type: [Number, String],
      default: null
    },
    dictName: {
      type: String,
      default: ''
    }
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    // 表格列定义
    const columns = [
      {
        title: '字典标签',
        dataIndex: 'label',
        key: 'label'
      },
      {
        title: '字典键值',
        dataIndex: 'value',
        key: 'value'
      },
      {
        title: '排序',
        dataIndex: 'sort',
        key: 'sort'
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status'
      },
      {
        title: '备注',
        dataIndex: 'remark',
        key: 'remark',
        ellipsis: true
      },
      {
        title: '操作',
        key: 'action',
        width: 200
      }
    ];

    // 表格数据
    const itemList = ref([]);
    const loading = ref(false);
    const total = ref(0);
    const current = ref(1);
    const pageSize = ref(10);
    const searchValue = ref('');

    // 表单相关
    const formRef = ref(null);
    const modalVisible = ref(false);
    const modalLoading = ref(false);
    const modalTitle = ref('新增字典项');
    const editingId = ref(null);

    const formState = reactive({
      label: '',
      value: '',
      sort: 0,
      status: true,
      remark: ''
    });

    const rules = {
      label: [{ required: true, message: '请输入字典标签' }],
      value: [{ required: true, message: '请输入字典键值' }],
      sort: [{ required: true, message: '请输入排序号' }]
    };

    // 重置搜索
    const resetSearch = () => {
      searchValue.value = '';
      current.value = 1;
      fetchItems();
    };

    // 获取字典项列表
    const fetchItems = async () => {
      if (!props.dictId) return;
      
      loading.value = true;
      try {
        const response = await getDictItems(props.dictId, {
          page: current.value,
          pageSize: pageSize.value,
          keyword: searchValue.value
        });
        
        if (response.code === 200) {
          itemList.value = response.data.list;
          total.value = response.data.total;
        }
      } catch (error) {
        console.error('获取字典项列表失败:', error);
        message.error('获取字典项列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 监听dictId变化
    watch(() => props.dictId, () => {
      if (props.visible && props.dictId) {
        fetchItems();
      }
    });

    // 监听visible变化
    watch(() => props.visible, (val) => {
      if (val && props.dictId) {
        fetchItems();
      }
    });

    // 显示新增模态框
    const showAddModal = () => {
      modalTitle.value = '新增字典项';
      editingId.value = null;
      Object.assign(formState, {
        label: '',
        value: '',
        sort: 0,
        status: true,
        remark: ''
      });
      modalVisible.value = true;
    };

    // 显示编辑模态框
    const showEditModal = (record) => {
      modalTitle.value = '编辑字典项';
      editingId.value = record.id;
      Object.assign(formState, record);
      formState.status = record.status === 1;
      modalVisible.value = true;
    };

    // 处理模态框确认
    const handleModalOk = async () => {
      try {
        await formRef.value.validate();
        modalLoading.value = true;

        const data = {
          ...formState,
          dict_id: props.dictId
        };

        if (editingId.value) {
          await updateDictItem(editingId.value, data);
          message.success('字典项更新成功');
        } else {
          await createDictItem(data);
          message.success('字典项添加成功');
        }
        
        modalVisible.value = false;
        fetchItems();
      } catch (error) {
        console.error('操作失败:', error);
        message.error('操作失败');
      } finally {
        modalLoading.value = false;
      }
    };

    // 处理模态框取消
    const handleModalCancel = () => {
      modalVisible.value = false;
    };

    // 确认删除方法
    const confirmDelete = (id) => {
      // 使用 $confirm 进行确认
      Modal.confirm({
        title: '确定要删除此字典项吗？',
        onOk: () => {
          handleDelete(id);
        },
      });
    };

    // 处理删除
    const handleDelete = async (id) => {
      try {
        await deleteDictItem(id);
        message.success('删除成功');
        fetchItems();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    };

    // 处理表格变化
    const handleTableChange = (pagination) => {
      current.value = pagination.current;
      pageSize.value = pagination.pageSize;
      fetchItems();
    };

    // 处理取消
    const handleCancel = () => {
      emit('update:visible', false);
    };

    // 处理搜索
    const onSearch = () => {
      current.value = 1;
      fetchItems();
    };

    return {
      columns,
      itemList,
      loading,
      total,
      current,
      pageSize,
      searchValue,
      formRef,
      formState,
      rules,
      modalVisible,
      modalLoading,
      modalTitle,
      confirmDelete,
      showAddModal,
      showEditModal,
      handleModalOk,
      handleModalCancel,
      handleDelete,
      handleTableChange,
      handleCancel,
      onSearch,
      resetSearch
    };
  }
};
</script>

<style scoped>
.dict-items-container {
  min-height: 400px;
}

.table-operations {
  margin-bottom: 16px;
}
</style> 