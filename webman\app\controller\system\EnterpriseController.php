<?php
namespace app\controller\system;

use support\Request;
use app\model\Enterprise;
use app\model\EnterpriseType;

class EnterpriseController
{
    /**
     * 获取企业列表
     */
    public function index(Request $request)
    {
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);
        $customsName = $request->input('customs_name');
        $customsCode = $request->input('customs_code');
        $typeId = $request->input('type_id');

        $query = Enterprise::with(['types']);

        if ($customsName) {
            $query->where('customs_name', 'like', "%{$customsName}%");
        }
        if ($customsCode) {
            $query->where('customs_code', 'like', "%{$customsCode}%");
        }
        if ($typeId) {
            $query->whereHas('types', function($q) use ($typeId) {
                $q->where('type_id', $typeId);
            });
        }

        $total = $query->count();
        $list = $query->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->orderBy('id', 'desc')
            ->get();

        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'list' => $list,
                'total' => $total
            ]
        ]);
    }

    /**
     * 创建企业
     */
    public function store(Request $request)
    {
        $data = $request->post();
        
        // 验证数据
        if (empty($data['customs_name'])) {
            return json(['code' => 400, 'message' => '请输入海关备案名称']);
        }
        if (empty($data['customs_code'])) {
            return json(['code' => 400, 'message' => '请输入海关备案编码']);
        }
        if (empty($data['types']) || !is_array($data['types'])) {
            return json(['code' => 400, 'message' => '请选择企业类型']);
        }

        // 检查编码是否已存在
        $exists = Enterprise::where('customs_code', $data['customs_code'])->exists();
        if ($exists) {
            return json(['code' => 400, 'message' => '该海关备案编码已存在']);
        }

        // 开启事务
        \support\Db::beginTransaction();
        try {
            // 创建企业
            $enterprise = Enterprise::create([
                'customs_name' => $data['customs_name'],
                'customs_code' => $data['customs_code'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
                'created_by' => $request->user->id ?? null,
                'updated_by' => $request->user->id ?? null
            ]);

            // 创建企业类型关联
            foreach ($data['types'] as $type) {
                EnterpriseType::create([
                    'enterprise_id' => $enterprise->id,
                    'type_id' => $type['id'],
                    'dxp_id' => $type['dxp_id'] ?? null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            \support\Db::commit();
            return json([
                'code' => 200,
                'message' => '创建成功',
                'data' => $enterprise
            ]);
        } catch (\Exception $e) {
            \support\Db::rollBack();
            return json([
                'code' => 500,
                'message' => '创建失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 更新企业
     */
    public function update(Request $request, $id)
    {
        $data = $request->post();
        
        $enterprise = Enterprise::find($id);
        if (!$enterprise) {
            return json(['code' => 404, 'message' => '企业不存在']);
        }

        // 验证数据
        if (empty($data['customs_name'])) {
            return json(['code' => 400, 'message' => '请输入海关备案名称']);
        }
        if (empty($data['customs_code'])) {
            return json(['code' => 400, 'message' => '请输入海关备案编码']);
        }
        if (empty($data['types']) || !is_array($data['types'])) {
            return json(['code' => 400, 'message' => '请选择企业类型']);
        }

        // 检查编码是否已存在（排除自身）
        $exists = Enterprise::where('customs_code', $data['customs_code'])
            ->where('id', '!=', $id)
            ->exists();
        if ($exists) {
            return json(['code' => 400, 'message' => '该海关备案编码已存在']);
        }

        // 开启事务
        \support\Db::beginTransaction();
        try {
            // 更新企业
            $enterprise->update([
                'customs_name' => $data['customs_name'],
                'customs_code' => $data['customs_code'],
                'updated_at' => date('Y-m-d H:i:s'),
                'updated_by' => $request->user->id ?? null
            ]);

            // 删除旧的类型关联
            EnterpriseType::where('enterprise_id', $id)->delete();

            // 创建新的类型关联
            foreach ($data['types'] as $type) {
                EnterpriseType::create([
                    'enterprise_id' => $enterprise->id,
                    'type_id' => $type['id'],
                    'dxp_id' => $type['dxp_id'] ?? null,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }

            \support\Db::commit();
            return json([
                'code' => 200,
                'message' => '更新成功',
                'data' => $enterprise
            ]);
        } catch (\Exception $e) {
            \support\Db::rollBack();
            return json([
                'code' => 500,
                'message' => '更新失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 删除企业
     */
    public function destroy(Request $request, $id)
    {
        $enterprise = Enterprise::find($id);
        if (!$enterprise) {
            return json(['code' => 404, 'message' => '企业不存在']);
        }

        // 开启事务
        \support\Db::beginTransaction();
        try {
            // 删除企业（会级联删除类型关联）
            $enterprise->delete();

            \support\Db::commit();
            return json([
                'code' => 200,
                'message' => '删除成功'
            ]);
        } catch (\Exception $e) {
            \support\Db::rollBack();
            return json([
                'code' => 500,
                'message' => '删除失败：' . $e->getMessage()
            ]);
        }
    }
} 