<?php
namespace app\controller;

use support\Request;
use app\model\ProductCategory;

class ProductCategoryController
{
    /**
     * 获取分类列表
     */
    public function index(Request $request)
    {
        try {
            $categories = ProductCategory::orderBy('sort', 'asc')
                                      ->where('status', 1)
                                      ->get();
            return json(['code' => 200, 'data' => $categories]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '获取分类列表失败：' . $e->getMessage()]);
        }
    }

    /**
     * 创建分类
     */
    public function store(Request $request)
    {
        try {
            $data = $request->post();

            // 验证分类编码是否已存在
            if (ProductCategory::where('code', $data['code'])->exists()) {
                return json(['code' => 400, 'message' => '分类编码已存在']);
            }

            $category = ProductCategory::create($data);
            return json(['code' => 200, 'message' => '创建成功', 'data' => $category]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '创建分类失败：' . $e->getMessage()]);
        }
    }

    /**
     * 更新分类
     */
    public function update(Request $request, $id)
    {
        try {
            $category = ProductCategory::find($id);
            if (!$category) {
                return json(['code' => 404, 'message' => '分类不存在']);
            }

            $data = $request->post();

            // 验证分类编码是否已存在（排除当前分类）
            if (ProductCategory::where('code', $data['code'])
                ->where('id', '!=', $id)
                ->exists()) {
                return json(['code' => 400, 'message' => '分类编码已存在']);
            }

            $category->update($data);
            return json(['code' => 200, 'message' => '更新成功', 'data' => $category]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '更新分类失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除分类
     */
    public function destroy(Request $request, $id)
    {
        try {
            $category = ProductCategory::find($id);
            if (!$category) {
                return json(['code' => 404, 'message' => '分类不存在']);
            }

            // 检查是否有商品使用该分类
            if ($category->products()->exists()) {
                return json(['code' => 400, 'message' => '该分类下存在商品，无法删除']);
            }

            $category->delete();
            return json(['code' => 200, 'message' => '删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '删除分类失败：' . $e->getMessage()]);
        }
    }
} 