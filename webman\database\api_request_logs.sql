CREATE TABLE `api_request_logs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `app_id` bigint(20) unsigned DEFAULT NULL COMMENT '应用ID',
  `template_id` bigint(20) unsigned DEFAULT NULL COMMENT '模板ID',
  `method` varchar(10) NOT NULL COMMENT '请求方法',
  `url` varchar(255) NOT NULL COMMENT '请求URL',
  `headers` json DEFAULT NULL COMMENT '请求头',
  `request_data` json DEFAULT NULL COMMENT '请求数据',
  `response_data` json DEFAULT NULL COMMENT '响应数据',
  `ip` varchar(45) DEFAULT NULL COMMENT '请求IP',
  `status` tinyint(4) DEFAULT 1 COMMENT '状态：0失败，1成功',
  `error_message` varchar(255) DEFAULT NULL COMMENT '错误信息',
  `created_at` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_app_id` (`app_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci; 