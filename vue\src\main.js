import { createApp } from 'vue';
import { createPinia } from 'pinia';
import Antd from 'ant-design-vue';
import App from './App.vue';
import router from './router';
import permission from './directives/permission';
import 'ant-design-vue/dist/reset.css';
import '@/assets/tailwind.css';
import './styles/global.css';

const app = createApp(App);

// 注册权限指令
app.directive('permission', permission);

app.use(createPinia())
   .use(router)
   .use(Antd)
   .mount('#app');
