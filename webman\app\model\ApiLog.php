<?php
namespace app\model;

use support\Model;

class ApiLog extends Model
{
    protected $table = 'api_logs';
    
    protected $fillable = [
        'app_id',
        'method',
        'path',
        'params',
        'response',
        'status_code',
        'ip',
        'user_agent',
        'duration',
        'created_at'
    ];

    /**
     * 记录API请求日志
     */
    public static function log($appId, $request, $response, $duration)
    {
        return self::create([
            'app_id' => $appId,
            'method' => $request->method(),
            'path' => $request->path(),
            'params' => json_encode($request->all(), JSON_UNESCAPED_UNICODE),
            'response' => $response,
            'status_code' => 200,
            'ip' => $request->getRealIp(),
            'user_agent' => $request->header('user-agent'),
            'duration' => $duration,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
} 