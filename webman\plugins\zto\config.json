{"name": "中通国际", "code": "ZTO", "version": "1.0.0", "description": "用于使用中通国际获取运单及推送物流单", "picture": "https://gaodux.oss-cn-qingdao.aliyuncs.com/gaodux_customs/zto.jpg", "docUrl": "https://open.ztoglobal.com/#/doc/-2", "author": {"name": "官方开发团队", "email": "<EMAIL>"}, "settings_schema": {"fields": [{"field": "env_type", "label": "环境类型", "type": "select", "required": true, "default": "prod", "options": [{"label": "生产环境", "value": "prod"}, {"label": "测试环境", "value": "test"}], "description": ""}, {"field": "api_type", "label": "接口类型", "type": "select", "required": true, "default": "bbc", "options": [{"label": "保税进口", "value": "bbc"}, {"label": "直邮进口", "value": "bc"}, {"label": "CC直邮进口", "value": "cc"}], "description": "选择接口类型"}, {"field": "app_code", "label": "应用标识/客户ID", "type": "text", "required": true, "default": "", "placeholder": "请输入应用标识appCode", "description": "应用标识appCode", "rules": {"pattern": "^[a-zA-Z0-9]{1,32}$", "message": "应用标识必须是1-32位字母或数字"}}, {"field": "secret_key", "label": "密钥", "type": "password", "required": true, "default": "", "placeholder": "请输入密钥secretKey", "description": "应用密钥secretKey", "encrypt": true}, {"field": "shipper_name", "label": "发件人姓名", "type": "text", "required": true, "default": "", "placeholder": "请输入发件人姓名", "description": ""}, {"field": "shipper_phone", "label": "发件人手机号", "type": "text", "required": true, "default": "", "placeholder": "请输入发件人手机号", "rules": {"pattern": "^[0-9]{11}$", "message": "发件人手机号必须是11位数字"}, "description": ""}, {"field": "shipper_country", "label": "发件人国家", "type": "text", "required": true, "default": "中国", "placeholder": "请输入发件人国家", "description": ""}, {"field": "shipper_province", "label": "发件人省份", "type": "text", "required": true, "default": "", "placeholder": "请输入发件人省份", "description": ""}, {"field": "shipper_city", "label": "发件人城市", "type": "text", "required": true, "default": "", "placeholder": "请输入发件人城市", "description": ""}, {"field": "shipper_district", "label": "发件人区/县", "type": "text", "required": true, "default": "", "placeholder": "请输入发件人区/县", "description": ""}, {"field": "shipper_address", "label": "发件人地址", "type": "text", "required": true, "default": "", "placeholder": "请输入发件人详细地址", "description": ""}, {"field": "stock_flag", "label": "订单贸易类型", "type": "select", "required": false, "default": "", "options": [{"label": "集货", "value": "1"}, {"label": "备货", "value": "2"}], "description": "保税或直邮必填，CC直邮可不填"}, {"field": "warehouse_code", "label": "仓库编码", "type": "text", "required": false, "default": "", "placeholder": "请输入仓库编码warehouseCode", "description": "仓库编码warehouseCode：根据业务按需填写"}, {"field": "order_source", "label": "订单来源", "type": "text", "required": false, "default": "", "placeholder": "请输入订单来源orderSource", "description": "订单来源orderSource：CC直邮必填，其他接口类型可不填"}, {"field": "is_return_order_no", "label": "是否回传单号", "type": "switch", "required": false, "default": false, "description": "开启后将根据填写的回调地址将运单号回传"}, {"field": "notify_url", "label": "回调地址", "type": "text", "required": false, "default": "", "placeholder": "https://example.com/notify", "description": "运单号通知地址", "rules": {"pattern": "^https?://.*$", "message": "请输入有效的URL地址"}}, {"field": "log_level", "label": "日志级别", "type": "select", "required": true, "default": "info", "options": [{"label": "调试", "value": "debug"}, {"label": "信息", "value": "info"}, {"label": "警告", "value": "warning"}, {"label": "错误", "value": "error"}], "description": "日志记录级别"}]}}