<template>
  <system-page title="系统信息">
    <a-spin :spinning="loading">
      <a-row :gutter="[16, 16]">
        <!-- 服务器基本信息 -->
        <a-col :span="24">
          <a-card title="服务器基本信息" :bordered="false">
            <a-descriptions :column="3">
              <a-descriptions-item label="服务器名称">{{ info.server_name }}</a-descriptions-item>
              <a-descriptions-item label="操作系统">{{ info.server_os }} {{ info.server_version }}</a-descriptions-item>
              <a-descriptions-item label="Web服务器">{{ info.server_software }}</a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>

        <!-- 运行状态 -->
        <a-col :span="24">
          <a-card title="运行状态" :bordered="false">
            <a-descriptions :column="3">
              <a-descriptions-item label="启动时间">{{ info.server_uptime }}</a-descriptions-item>
              <a-descriptions-item label="系统时区">{{ info.time_info?.system_timezone }}</a-descriptions-item>
              <a-descriptions-item label="当前时间">{{ info.time_info?.system_time }}</a-descriptions-item>
              <a-descriptions-item label="当前用户">{{ info.process_info?.process_user }}</a-descriptions-item>
              <a-descriptions-item label="进程ID">{{ info.process_info?.process_id }}</a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>

        <!-- CPU信息 -->
        <a-col :span="12">
          <a-card title="CPU信息" :bordered="false">
            <a-descriptions :column="1">
              <a-descriptions-item label="处理器">{{ info.cpu_info?.cpu_name }}</a-descriptions-item>
              <a-descriptions-item label="物理核心数">{{ info.cpu_info?.cpu_cores }} 核</a-descriptions-item>
              <a-descriptions-item label="逻辑处理器">{{ info.cpu_info?.cpu_logical_processors }} 个</a-descriptions-item>
              <a-descriptions-item label="系统负载">{{ info.server_load_avg }}</a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>

        <!-- 内存和磁盘信息 -->
        <a-col :span="12">
          <a-card title="内存和磁盘" :bordered="false">
            <a-descriptions :column="1">
              <a-descriptions-item label="内存使用">
                {{ info.server_memory_usage_human }} / {{ info.server_memory_total_human }}
                <a-progress 
                  :percent="Math.round((info.server_memory_usage / info.server_memory_total) * 100)"
                  size="small"
                  :stroke-color="{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }"
                />
              </a-descriptions-item>
              <a-descriptions-item label="磁盘空间">
                已用: {{ (Number(info.server_disk_total) - Number(info.server_disk_free)) / 1024 / 1024 / 1024 | 0 }} GB / 
                总计: {{ info.server_disk_total_human }}
                <a-progress 
                  :percent="Math.round(((info.server_disk_total - info.server_disk_free) / info.server_disk_total) * 100)"
                  size="small"
                  :stroke-color="{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }"
                />
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>

        <!-- PHP配置信息 -->
        <a-col :span="12">
          <a-card title="PHP配置" :bordered="false">
            <a-descriptions :column="2">
              <a-descriptions-item label="PHP版本">{{ info.php_version }}</a-descriptions-item>
              <a-descriptions-item
                v-for="(value, key) in info.php_config"
                :key="key"
                :label="key">
                {{ value }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>

        <!-- PHP扩展 -->
        <a-col :span="12">
          <a-card title="已加载的PHP扩展" :bordered="false" class="extension-card">
            <a-space wrap>
              <a-tag 
                v-for="ext in info.php_extensions" 
                :key="ext"
                :color="getExtensionColor(ext)">
                {{ ext }}
              </a-tag>
            </a-space>
          </a-card>
        </a-col>
      </a-row>
    </a-spin>
  </system-page>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import SystemPage from '../components/SystemPage.vue';
import { getSystemInfo } from '@/api/system/info';

const info = ref({});
const loading = ref(false);

const getExtensionColor = (ext) => {
  const colors = {
    Core: '#f50',
    PDO: '#2db7f5',
    mysql: '#87d068',
    mysqli: '#87d068',
    pdo_mysql: '#87d068',
    curl: '#108ee9',
    openssl: '#722ed1',
    gd: '#eb2f96',
    zip: '#faad14',
    default: '#8c8c8c'
  };
  
  for (const key in colors) {
    if (ext.toLowerCase().includes(key.toLowerCase())) {
      return colors[key];
    }
  }
  return colors.default;
};

const fetchSystemInfo = async () => {
  loading.value = true;
  try {
    const res = await getSystemInfo();
    if (res.code === 200) {
      info.value = res.data;
    } else {
      message.error(res.message || '获取系统信息失败');
    }
  } catch (error) {
    console.error('获取系统信息失败:', error);
    message.error('获取系统信息失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchSystemInfo();
});
</script>

<style scoped>
:deep(.ant-card) {
  height: 100%;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  min-width: 120px;
}

:deep(.ant-tag) {
  margin: 4px;
  font-size: 13px;
  padding: 4px 8px;
}

:deep(.ant-divider) {
  margin: 12px 0;
  color: rgba(0, 0, 0, 0.45);
}

:deep(.extension-card .ant-card-body) {
  padding: 16px;
  min-height: 200px;
}

:deep(.ant-progress) {
  margin-top: 8px;
}

:deep(.ant-descriptions-item-content) {
  color: rgba(0, 0, 0, 0.65);
}

:deep(.ant-spin-nested-loading) {
  height: 100%;
}

:deep(.ant-spin) {
  max-height: none;
}
</style> 