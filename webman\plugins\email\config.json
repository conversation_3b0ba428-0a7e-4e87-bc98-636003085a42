{"name": "邮件通知服务", "code": "email", "version": "1.0.0", "description": "提供SMTP邮件发送服务，支持定时任务邮件通知、系统报警等功能", "picture": "https://gaodux.oss-cn-qingdao.aliyuncs.com/gaodux_customs/email.png", "docUrl": "https://github.com/PHPMailer/PHPMailer", "author": {"name": "官方开发团队", "email": "<EMAIL>"}, "settings_schema": {"fields": [{"field": "smtp_host", "label": "SMTP服务器", "type": "text", "required": true, "default": "smtp.qq.com", "placeholder": "请输入SMTP服务器地址", "description": "邮件服务器的SMTP地址，如：smtp.qq.com、smtp.163.com", "rules": {"pattern": "^[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "message": "请输入有效的服务器地址"}}, {"field": "smtp_port", "label": "SMTP端口", "type": "text", "required": true, "default": "587", "placeholder": "请输入SMTP端口", "description": "SMTP服务器端口，常用端口：587(TLS)、465(SSL)、25(无加密)", "rules": {"pattern": "^[0-9]{1,5}$", "message": "端口必须是1-65535之间的数字"}}, {"field": "smtp_encryption", "label": "加密方式", "type": "select", "required": true, "default": "tls", "description": "邮件传输加密方式", "options": [{"value": "tls", "label": "TLS加密"}, {"value": "ssl", "label": "SSL加密"}, {"value": "none", "label": "无加密"}]}, {"field": "smtp_username", "label": "SMTP用户名", "type": "text", "required": true, "placeholder": "请输入邮箱账号", "description": "用于SMTP认证的邮箱账号", "rules": {"pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "message": "请输入有效的邮箱地址"}}, {"field": "smtp_password", "label": "SMTP密码", "type": "password", "required": true, "placeholder": "请输入邮箱密码或授权码", "description": "邮箱密码或第三方客户端授权码（推荐使用授权码）", "encrypt": true}, {"field": "from_email", "label": "发件人邮箱", "type": "text", "required": true, "placeholder": "请输入发件人邮箱", "description": "邮件发送方显示的邮箱地址", "rules": {"pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$", "message": "请输入有效的邮箱地址"}}, {"field": "from_name", "label": "发件人姓名", "type": "text", "required": false, "default": "报关系统", "placeholder": "请输入发件人显示名称", "description": "邮件发送方显示的名称"}, {"field": "timeout", "label": "连接超时(秒)", "type": "text", "required": false, "default": "30", "placeholder": "请输入超时时间", "description": "SMTP连接超时时间，默认30秒", "rules": {"pattern": "^[0-9]{1,3}$", "message": "超时时间必须是1-999之间的数字"}}, {"field": "debug_level", "label": "调试级别", "type": "select", "required": false, "default": "0", "description": "邮件发送调试级别，生产环境建议设为0", "options": [{"value": "0", "label": "关闭调试"}, {"value": "1", "label": "客户端消息"}, {"value": "2", "label": "客户端和服务器消息"}, {"value": "3", "label": "详细调试信息"}, {"value": "4", "label": "低级数据输出"}]}], "groups": [{"name": "smtp", "label": "SMTP服务器设置", "fields": ["smtp_host", "smtp_port", "smtp_encryption", "smtp_username", "smtp_password"]}, {"name": "sender", "label": "发件人设置", "fields": ["from_email", "from_name"]}, {"name": "advanced", "label": "高级设置", "fields": ["timeout", "debug_level"]}]}}