<?php
namespace app\model;

use support\Model;

class AppLog extends Model
{
    protected $table = 'app_logs';
    
    protected $fillable = [
        'app_id',
        'level',
        'message',
        'context',
        'created_at'
    ];

    protected $casts = [
        'context' => 'json'
    ];

    /**
     * 关联应用
     */
    public function app()
    {
        return $this->belongsTo(App::class);
    }
}