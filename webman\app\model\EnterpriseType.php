<?php
namespace app\model;

use support\Model;

class EnterpriseType extends Model
{
    protected $table = 'enterprise_types';
    
    protected $fillable = [
        'enterprise_id',
        'type_id',
        'dxp_id',
        'created_at',
        'updated_at'
    ];

    // 关联企业
    public function enterprise()
    {
        return $this->belongsTo(Enterprise::class, 'enterprise_id');
    }

    // 关联字典项
    public function type()
    {
        return $this->belongsTo(DictionaryItem::class, 'type_id');
    }
} 