<template>
  <div class="product-categories">
    <div class="page-header">
      <a-space>
        <a-button @click="goBack">
          <template #icon><left-outlined /></template>
          返回
        </a-button>
        <a-divider type="vertical" />
        <h2>商品分类</h2>
      </a-space>
    </div>

    <a-card :bordered="false">
      <!-- 操作按钮 -->
      <div class="table-operations">
        <a-button type="primary" @click="showAddModal">
          <template #icon><plus-outlined /></template>
          添加分类
        </a-button>
      </div>

      <!-- 分类列表 -->
      <a-table
        :columns="columns"
        :data-source="categoryList"
        :loading="loading"
        :pagination="false"
        size="middle"
        :row-key="record => record.id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'success' : 'error'">
              {{ record.status === 1 ? '启用' : '禁用' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="handleEdit(record)" v-permission="'update'">
                <template #icon><EditOutlined /></template>
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除这个分类吗？"
                v-permission="'delete'"
                @confirm="handleDelete(record)"
              >
                <a-button v-permission="'delete'" type="link" danger>
                    <DeleteOutlined />
                    删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑分类模态框 -->
    <a-modal
      :title="modalTitle"
      v-model:open="modalVisible"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :maskClosable="false"
      width="500px"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 19 }"
      >
        <a-form-item label="分类名称" name="name">
          <a-input v-model:value="formState.name" placeholder="请输入分类名称" />
        </a-form-item>
        <a-form-item label="分类编码" name="code">
          <a-input v-model:value="formState.code" placeholder="请输入分类编码" />
        </a-form-item>
        <a-form-item label="排序" name="sort">
          <a-input-number v-model:value="formState.sort" :min="0" style="width: 100%" placeholder="请输入排序号" />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="formState.status">
            <a-radio :value="1">启用</a-radio>
            <a-radio :value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formState.remark" :rows="4" placeholder="请输入备注" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted } from 'vue';
import { 
  PlusOutlined,
  LeftOutlined,
  DeleteOutlined,
  EditOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { formatDate } from '@/utils/utils';
import { getProductCategories, createProductCategory, updateProductCategory, deleteProductCategory } from '@/api/products';

export default defineComponent({
  name: 'ProductCategories',
  components: {
    PlusOutlined,
    LeftOutlined,
    DeleteOutlined,
    EditOutlined
  },
  setup() {
    const router = useRouter();
    const loading = ref(false);
    const categoryList = ref([]);
    const modalVisible = ref(false);
    const modalTitle = ref('添加分类');
    const formRef = ref(null);

    // 表格列定义
    const columns = [
      {
        title: '分类名称',
        dataIndex: 'name',
        key: 'name',
        width: 200,
      },
      {
        title: '分类编码',
        dataIndex: 'code',
        key: 'code',
        width: 150,
      },
      {
        title: '排序',
        dataIndex: 'sort',
        key: 'sort',
        width: 100,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
      },
      {
        title: '备注',
        dataIndex: 'remark',
        key: 'remark',
        ellipsis: true,
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 180,
      },
      {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 200,
      },
    ];

    // 表单状态
    const formState = reactive({
      id: null,
      name: '',
      code: '',
      sort: 0,
      status: 1,
      remark: '',
    });

    // 表单验证规则
    const rules = {
      name: [
        { required: true, message: '请输入分类名称', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
      ],
      code: [
        { required: true, message: '请输入分类编码', trigger: 'blur' },
        { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
      ],
      sort: [
        { required: true, message: '请输入排序号', trigger: 'blur' },
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' },
      ],
    };

    // 获取分类列表
    const fetchCategories = async () => {
      loading.value = true;
      try {
        const res = await getProductCategories();
        if (res.code === 200) {
          categoryList.value = res.data || [];
        }
      } catch (error) {
        console.error('获取分类列表失败:', error);
        message.error('获取分类列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 显示添加模态框
    const showAddModal = () => {
      modalTitle.value = '添加分类';
      formState.id = null;
      formState.name = '';
      formState.code = '';
      formState.sort = 0;
      formState.status = 1;
      formState.remark = '';
      modalVisible.value = true;
    };

    // 显示编辑模态框
    const handleEdit = (record) => {
      modalTitle.value = '编辑分类';
      Object.assign(formState, {
        id: record.id,
        name: record.name,
        code: record.code,
        sort: record.sort,
        status: record.status,
        remark: record.remark
      });
      modalVisible.value = true;
    };

    // 删除分类
    const handleDelete = async (record) => {
      try {
        const res = await deleteProductCategory(record.id);
        if (res.code === 200) {
          message.success('删除成功');
          fetchCategories();
        } else {
          message.error(res.message || '删除失败');
        }
      } catch (error) {
        console.error('删除分类失败:', error);
        message.error('删除分类失败');
      }
    };

    // 提交表单
    const handleModalOk = () => {
      formRef.value.validate().then(async () => {
        try {
          let res;
          if (formState.id) {
            res = await updateProductCategory(formState.id, formState);
          } else {
            res = await createProductCategory(formState);
          }
          
          if (res.code === 200) {
            message.success(`${modalTitle.value}成功`);
            modalVisible.value = false;
            fetchCategories();
          } else {
            message.error(res.message || `${modalTitle.value}失败`);
          }
        } catch (error) {
          console.error(`${modalTitle.value}失败:`, error);
          message.error(`${modalTitle.value}失败`);
        }
      });
    };

    // 取消表单
    const handleModalCancel = () => {
      formRef.value?.resetFields();
      modalVisible.value = false;
    };

    // 返回上一页
    const goBack = () => {
      router.push('/products');
    };

    onMounted(() => {
      fetchCategories();
    });

    return {
      loading,
      categoryList,
      columns,
      modalVisible,
      modalTitle,
      formRef,
      formState,
      rules,
      formatDate,
      showAddModal,
      handleEdit,
      handleDelete,
      handleModalOk,
      handleModalCancel,
      goBack
    };
  }
});
</script>

<style lang="less" scoped>
.product-categories {
  .page-header {
    margin-bottom: 16px;
    display: flex;
    align-items: center;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
      color: #272343;
    }
  }

  .table-operations {
    margin-bottom: 16px;
  }

  .text-danger {
    color: #ff4d4f;
  }
}
</style> 