<template>
  <div class="system-page">
    <div class="page-header">
      <a-button class="back-button" @click="goBack">
        <left-outlined />返回
      </a-button>
      <h2>{{ title }}</h2>
    </div>
    <div class="page-content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
import { LeftOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';

export default {
  name: 'SystemPage',
  components: {
    LeftOutlined
  },
  props: {
    title: {
      type: String,
      required: true
    }
  },
  setup() {
    const router = useRouter();

    const goBack = () => {
      router.push('/system');
    };

    return {
      goBack
    };
  }
};
</script>

<style scoped>
.system-page {
  padding: 20px;
}

.page-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 10px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-content {
  background: #fff;
  border-radius: 8px;
  padding: 10px 0;
}
</style> 