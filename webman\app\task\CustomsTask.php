<?php

namespace app\task;

use app\model\Order;

// 海关报文处理
class CustomsTask
{
    /**
     * 海关报文处理流程
     * 1、非测试订单根据订单申报模板生成报文
     * 2、传输企业需电脑端部署运行程序，以获取报文
     * 3、先生成订单报文
     * 4、若订单信息存在支付原始信息和申报模板已配置支付应用信息，则推送支付单
     * 5、同时根据申报模板配置的物流应用获取单号并根据应用配置模板的回调地址返回发货信息，及推送运单
     * 6、三单推送成功后，待接收到海关回执后，自动推送清单
     * 7、程序运行的日志需要记录到数据库，并可在订单列表查看该订单的日志信息
     */
    public function taskHandle()
    {
        // 获取所有未申报的订单列表 [待新增申报和待重推申报]
        $orders = Order::with([
            'template' => [
                'paymentEnterprises',
                'logisticsApp',
                'logisticsTemplate',
                'platformEnterprise',
                'ecommerceEnterprise',
                'logisticsEnterprise',
                'bondedEnterprise',
                'declareEnterprise',
                'transmissionEnterprise',
                'channel'
            ],
            'items' => [
                'product' => [
                    'originCountry',
                    'unit',
                    'unit1',
                    'unit2'
                ]
            ],
            'declaration'

        ])
        ->whereIn('order_status', ['paid', 'shipped']) // 使用 whereIn 代替 where('order_status', 'in', ...)
        ->whereIn('customs_status', [0, 2]) // 使用 whereIn 代替 where('customs_status', 'in', ...)
        ->where('is_test', 0)
        ->get();
        
        // 处理成功的订单数量
        $successCount = 0;

        return $orders->toArray();

        // 测试
        return $this->handleOrder($orders[0]);

        // 循环处理订单
        foreach ($orders as $order) {
            $result = $this->handleOrder($order);
            if ($result['status'] === 'success') {
                $successCount++;
            }
        }

        // 记录状态
        $result = [
            'status' => 'success',
            'message' => '任务执行成功',
            'data' => [
                // 订单总数量
                'total_count' => count($orders),
                // 成功数量
                'success_count' => $successCount,
                'timestamp' => date('Y-m-d H:i:s')
            ]
        ];

        return $result;
    }

    /**
     * 处理订单
     * @param Order $order
     */
    public function handleOrder(Order $order)
    {
        // 推送支付单
        $this->pushPayment($order);

        // 生成订单报文
        $this->generateOrderXml($order);
        // return sprintf('订单：%s，申报模板：%s，渠道：%s，商品：%s', $order->order_no, $order->template->name, $order->channel->name, $order->items->name);

    }

    /**
     * 推送支付单
     * @param Order $order
     */
    public function pushPayment(Order $order)
    {
        // 微信支付
        if($order['payment_type'] == 'wechat') {
            // 调用微信支付接口
            $this->wechatPayment($order);
        }

        // 支付宝支付
        if($order['payment_type'] == 'alipay') {
            // 调用支付宝支付接口
            $this->alipayPayment($order);
        }
    }

    /**
     * 微信支付
     * @param Order $order
     */
    public function wechatPayment(Order $order)
    {
    }

    /**
     * 支付宝支付
     * @param Order $order
     */
    public function alipayPayment(Order $order)
    {
    }

    /**
     * 生成订单报文
     * @param Order $order
     */
    public function generateOrderXml(Order $order)
    {
    }
    
} 