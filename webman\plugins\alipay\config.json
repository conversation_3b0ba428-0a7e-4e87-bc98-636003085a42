{"name": "支付宝支付报关", "code": "ALIPAY", "version": "1.0.0", "description": "用于支付宝支付的商户提交海关需要的订单附加信息（支付单）", "picture": "https://gaodux.oss-cn-qingdao.aliyuncs.com/gaodux_customs/alipay.jpg", "author": {"name": "官方开发团队", "email": "<EMAIL>"}, "settings_schema": {"fields": [{"field": "mch_id", "label": "商户号", "type": "text", "required": true, "default": "", "placeholder": "请输入商户号", "description": "微信支付商户号", "rules": {"pattern": "^[0-9]{10}$", "message": "商户号必须是10位数字"}}, {"field": "app_id", "label": "应用ID", "type": "text", "required": true, "default": "", "placeholder": "请输入微信应用ID", "description": "微信支付对应的应用ID"}, {"field": "api_key", "label": "API密钥", "type": "password", "required": true, "default": "", "placeholder": "请输入API密钥", "description": "微信支付API密钥", "encrypt": true}, {"field": "notify_url", "label": "回调地址", "type": "text", "required": true, "default": "", "placeholder": "https://example.com/notify", "description": "支付结果通知地址", "rules": {"pattern": "^https?://.*$", "message": "请输入有效的URL地址"}}, {"field": "sandbox_mode", "label": "沙箱模式", "type": "switch", "required": false, "default": false, "description": "是否启用沙箱模式"}, {"field": "payment_types", "label": "支付方式", "type": "checkbox", "required": true, "default": ["jsapi"], "options": [{"label": "JSAPI支付", "value": "jsapi"}, {"label": "Native支付", "value": "native"}, {"label": "H5支付", "value": "h5"}], "description": "启用的支付方式"}, {"field": "log_level", "label": "日志级别", "type": "select", "required": true, "default": "info", "options": [{"label": "调试", "value": "debug"}, {"label": "信息", "value": "info"}, {"label": "警告", "value": "warning"}, {"label": "错误", "value": "error"}], "description": "日志记录级别"}]}}