<?php
namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;
use app\model\Permission;
use support\Redis;

class PermissionMiddleware implements MiddlewareInterface
{
    public function process(Request $request, callable $next): Response
    {
        // 获取当前用户
        $user = $request->user;
        if (!$user) {
            return json(['code' => 401, 'msg' => '未登录']);
        }

        // 超级管理员跳过权限验证
        if ($user->is_super === 1) {
            return $next($request);
        }

        // 获取当前请求路径和方法
        $path = $request->path();
        $method = strtoupper($request->method());

        // 从缓存中获取用户权限
        $cacheKey = "user_permissions:{$user->id}";
        $permissions = Redis::get($cacheKey);
        
        if (!$permissions) {
            // 缓存不存在，从数据库获取权限并缓存
            $permissions = $user->getAllPermissions()->toArray();
            Redis::setex($cacheKey, 3600, json_encode($permissions)); // 缓存1小时
        } else {
            $permissions = json_decode($permissions, true);
        }

        // 检查是否有权限访问
        $hasPermission = false;
        foreach ($permissions as $permission) {
            if ($permission['path'] === $path && 
                ($permission['method'] === $method || $permission['method'] === '*')) {
                $hasPermission = true;
                break;
            }
        }

        if (!$hasPermission) {
            return json(['code' => 403, 'msg' => '无权限访问']);
        }

        return $next($request);
    }
}
