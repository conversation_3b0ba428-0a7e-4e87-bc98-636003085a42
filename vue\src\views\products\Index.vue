<template>
  <div class="products-container-main">
    <div class="page-header">
      <h2>商品管理</h2>
      <p class="subtitle">备案商品及渠道商品管理</p>
    </div>

    <div class="products-grid">
      <!-- 备案商品模块 -->
      <div class="products-card" @click="navigateTo('/products/registered')">
        <div class="card-icon">
          <tags-outlined />
        </div>
        <div class="card-content">
          <h3>备案商品</h3>
          <p>管理所有备案商品信息</p>
          <div class="card-status-wrapper">
            <div class="card-status normal">{{ productCount || 0 }}个商品</div>
          </div>
        </div>
        <div class="card-arrow">
          <right-outlined />
        </div>
      </div>

      <!-- 商品分类模块 -->
      <div class="products-card" @click="navigateTo('/products/categories')">
        <div class="card-icon">
          <apartment-outlined />
        </div>
        <div class="card-content">
          <h3>商品分类</h3>
          <p>管理商品分类信息</p>
          <div class="card-status-wrapper">
            <div class="card-status normal">{{ categoryCount || 0 }}个分类</div>
          </div>
        </div>
        <div class="card-arrow">
          <right-outlined />
        </div>
      </div>

      <!-- 渠道管理模块 -->
      <div class="products-card" @click="navigateTo('/products/channel')">
        <div class="card-icon">
          <shop-outlined />
        </div>
        <div class="card-content">
          <h3>渠道商品</h3>
          <p>渠道商品与备案商品的关联管理</p>
          <div class="channel-list">
            <a-button 
              type="primary" 
              size="default" 
              @click.stop="goToChannelDict"
            >
              <template #icon><setting-outlined /></template>
              渠道管理
            </a-button>
          </div>
        </div>
        <div class="card-arrow">
          <right-outlined />
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import {
  TagsOutlined,
  ShopOutlined,
  RightOutlined,
  SettingOutlined,
  ApartmentOutlined
} from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';
import { getDictItems } from '@/api/system/dict';
import { getProductCount, getProductCategories } from '@/api/products';

export default {
  name: 'ProductsIndex',
  components: {
    TagsOutlined,
    ShopOutlined,
    RightOutlined,
    SettingOutlined,
    ApartmentOutlined
  },
  setup() {
    const router = useRouter();
    const channels = ref([]);
    const productCount = ref(0);
    const categoryCount = ref(0);

    // 获取渠道列表
    const fetchChannels = async () => {
      try {
        const res = await getDictItems('channel');
        if (res.code === 200) {
          channels.value = res.data || [];
        }
      } catch (error) {
        console.error('获取渠道列表失败:', error);
      }
    };

    // 获取商品总数
    const fetchProductCount = async () => {
      try {
        const res = await getProductCount();
        if (res.code === 200) {
          productCount.value = res.data;
        }
      } catch (error) {
        console.error('获取商品总数失败:', error);
      }
    };

    // 获取分类总数
    const fetchCategoryCount = async () => {
      try {
        const res = await getProductCategories();
        if (res.code === 200) {
          categoryCount.value = res.data?.length || 0;
        }
      } catch (error) {
        console.error('获取分类总数失败:', error);
      }
    };

    const navigateTo = (path) => {
      router.push(path);
    };

    const goToChannelDict = () => {
      router.push({
        path: '/system/dict',
        query: { type: 'channel' }
      });
    };

    onMounted(() => {
      fetchChannels();
      fetchProductCount();
      fetchCategoryCount();
    });

    return {
      channels,
      productCount,
      categoryCount,
      navigateTo,
      goToChannelDict
    };
  }
};
</script>

<style lang="less" scoped>
.products-container-main {
  padding: 24px;
  min-height: 100%;
}

.page-header {
  margin-bottom: 24px;

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #272343;
  }

  .subtitle {
    margin-top: 8px;
    color: #666;
    font-size: 14px;
  }
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.products-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #f0f0f0;

  &:hover {
    border-color: #272343;

    .card-arrow {
      color: #272343;
      transform: translateX(4px);
    }
  }

  .card-icon {
    width: 40px;
    height: 40px;
    background: #f5f5f5;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: #272343;
    flex-shrink: 0;
  }

  .card-content {
    flex: 1;
    min-width: 0;

    h3 {
      margin: 0 0 8px;
      font-size: 16px;
      font-weight: 500;
      color: #272343;
    }

    p {
      margin: 0 0 12px;
      font-size: 14px;
      color: #666;
      line-height: 1.5;
    }
  }

  .card-arrow {
    color: #bfbfbf;
    font-size: 14px;
    transition: transform 0.3s;
    flex-shrink: 0;
    margin-top: 12px;
  }
}

.channel-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;

  .ant-tag {
    margin-right: 0;
  }
}

.card-status {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;

  &.normal {
    background: #f6ffed;
    color: #52c41a;
  }
}

@media screen and (max-width: 1200px) {
  .products-grid {
    grid-template-columns: 1fr;
  }
}
</style> 