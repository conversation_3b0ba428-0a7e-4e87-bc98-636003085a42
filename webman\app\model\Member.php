<?php

namespace app\model;

use support\Model;

class Member extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'member';

    /**
     * The primary key associated with the table.
     *
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'username',
        'password',
        'name',
        'phone',
        'email',
        'status'
    ];

    // 隐藏属性
    protected $hidden = [
        'password',
    ];

    /**
     * 获取用户角色
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'member_roles', 'member_id', 'role_id');
    }

    /**
     * 获取用户所有权限
     */
    public function getAllPermissions()
    {
        return Permission::whereIn('id', function ($query) {
            $query->select('permission_id')
                ->from('role_permissions')
                ->whereIn('role_id', function ($query) {
                    $query->select('role_id')
                        ->from('member_roles')
                        ->where('member_id', $this->id);
                });
        })->where('status', 1)->get();
    }

    /**
     * 检查用户是否有指定权限
     */
    public function hasPermission($permissionCode)
    {
        // 超级管理员拥有所有权限
        if ($this->username === 'admin') {
            return true;
        }

        return Permission::whereIn('id', function ($query) {
            $query->select('permission_id')
                ->from('role_permissions')
                ->whereIn('role_id', function ($query) {
                    $query->select('role_id')
                        ->from('member_roles')
                        ->where('member_id', $this->id);
                });
        })
        ->where('permission_code', $permissionCode)
        ->where('status', 1)
        ->exists();
    }

    /**
     * 检查用户是否有任意一个指定权限
     */
    public function hasAnyPermission(array $permissionCodes)
    {
        // 超级管理员拥有所有权限
        if ($this->username === 'admin') {
            return true;
        }

        return Permission::whereIn('id', function ($query) {
            $query->select('permission_id')
                ->from('role_permissions')
                ->whereIn('role_id', function ($query) {
                    $query->select('role_id')
                        ->from('member_roles')
                        ->where('member_id', $this->id);
                });
        })
        ->whereIn('permission_code', $permissionCodes)
        ->where('status', 1)
        ->exists();
    }

    /**
     * 检查用户是否有所有指定权限
     */
    public function hasAllPermissions(array $permissionCodes)
    {
        // 超级管理员拥有所有权限
        if ($this->username === 'admin') {
            return true;
        }

        $count = Permission::whereIn('id', function ($query) {
            $query->select('permission_id')
                ->from('role_permissions')
                ->whereIn('role_id', function ($query) {
                    $query->select('role_id')
                        ->from('member_roles')
                        ->where('member_id', $this->id);
                });
        })
        ->whereIn('permission_code', $permissionCodes)
        ->where('status', 1)
        ->count();

        return $count === count($permissionCodes);
    }

    /**
     * 检查用户是否被锁定
     */
    public function isLocked()
    {
        return $this->locked_until && strtotime($this->locked_until) > time();
    }

    /**
     * 记录登录失败
     */
    public function recordLoginFail()
    {
        $this->login_fails++;
        $this->save();
    }

    /**
     * 重置登录失败计数
     */
    public function resetLoginFails()
    {
        $this->login_fails = 0;
        $this->save();
    }

    /**
     * 验证密码
     */
    public function verifyPassword($password)
    {
        return password_verify($password, $this->password);
    }
}
