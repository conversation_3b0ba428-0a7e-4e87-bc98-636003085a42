<?php
namespace app\controller;

use support\Request;
use app\model\RegisteredProduct;
use app\model\ProductCategory;

class ProductController
{
    /**
     * 获取商品数量
     */
    public function count(Request $request)
    {
        $categoryId = $request->input('category_id');
        
        $query = RegisteredProduct::query();
        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }
        
        $count = $query->count();
        
        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $count
        ]);
    }

    /**
     * 获取商品列表
     */
    public function index(Request $request)
    {
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);
        $goodsName = $request->input('goods_name');
        $goodsNo = $request->input('goods_no');
        $categoryId = $request->input('category_id');
        $hsCode = $request->input('hs_code');
        $barCode = $request->input('bar_code');
        $status = $request->input('status');

        $query = RegisteredProduct::with(['category']);

        if ($goodsName) {
            $query->where('goods_name', 'like', "%{$goodsName}%");
        }
        if ($goodsNo) {
            $query->where('goods_no', 'like', "%{$goodsNo}%");
        }
        if ($categoryId) {
            $query->where('category_id', $categoryId);
        }
        if ($hsCode) {
            $query->where('hs_code', 'like', "%{$hsCode}%");
        }
        if ($barCode) {
            $query->where('bar_code', 'like', "%{$barCode}%");
        }
        if ($status !== null && $status !== '') {
            $query->where('status', $status);
        }

        $total = $query->count();
        $list = $query->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->orderBy('id', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'goods_type' => $item->goods_type,
                    'sequence_no' => $item->sequence_no,
                    'filing_no' => $item->filing_no,
                    'goods_name' => $item->goods_name,
                    'goods_no' => $item->goods_no,
                    'bar_code' => $item->bar_code,
                    'goods_tax' => $item->goods_tax,
                    'hs_code' => $item->hs_code,
                    'goods_model' => $item->goods_model,
                    'origin_country_id' => $item->origin_country_id,
                    'unit_id' => $item->unit_id,
                    'unit1_id' => $item->unit1_id,
                    'unit2_id' => $item->unit2_id,
                    'qty1' => $item->qty1,
                    'qty2' => $item->qty2,
                    'net_weight' => $item->net_weight,
                    'gross_weight' => $item->gross_weight,
                    'category_id' => $item->category_id,
                    'category' => $item->category ? $item->category->name : '',
                    'price' => $item->price,
                    'status' => $item->status,
                    'description' => $item->description,
                    'created_at' => $item->created_at,
                    'updated_at' => $item->updated_at
                ];
            });

        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => [
                'list' => $list,
                'total' => $total
            ]
        ]);
    }

    /**
     * 搜索商品
     */
    public function search(Request $request)
    {
        $keyword = $request->input('keyword', '');
        $pageSize = $request->input('pageSize', 20);

        $query = RegisteredProduct::with(['category']);

        if ($keyword) {
            $query->where(function($q) use ($keyword) {
                $q->where('goods_name', 'like', "%{$keyword}%")
                  ->orWhere('goods_no', 'like', "%{$keyword}%")
                  ->orWhere('bar_code', 'like', "%{$keyword}%");
            });
        }

        $list = $query->limit($pageSize)
            ->orderBy('id', 'desc')
            ->get()
            ->map(function ($item) {
                return [
                    'id' => $item->id,
                    'goods_name' => $item->goods_name,
                    'goods_no' => $item->goods_no,
                    'bar_code' => $item->bar_code,
                    'category' => $item->category ? [
                        'id' => $item->category->id,
                        'name' => $item->category->name
                    ] : null
                ];
            });

        return json([
            'code' => 200,
            'message' => '获取成功',
            'data' => $list
        ]);
    }

    /**
     * 创建商品
     */
    public function store(Request $request)
    {
        $data = $request->post();
        
        // 验证数据
        if (empty($data['goods_name'])) {
            return json(['code' => 400, 'message' => '请输入商品名称']);
        }
        if (empty($data['goods_no'])) {
            return json(['code' => 400, 'message' => '请输入商品编码']);
        }
        if (empty($data['category_id'])) {
            return json(['code' => 400, 'message' => '请选择商品分类']);
        }

        // 检查商品编码是否已存在
        $exists = RegisteredProduct::where('goods_no', $data['goods_no'])->exists();
        if ($exists) {
            return json(['code' => 400, 'message' => '该商品编码已存在']);
        }

        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');

        $product = RegisteredProduct::create($data);

        return json([
            'code' => 200,
            'message' => '创建成功',
            'data' => $product
        ]);
    }

    /**
     * 更新商品
     */
    public function update(Request $request, $id)
    {
        $data = $request->post();
        
        $product = RegisteredProduct::find($id);
        if (!$product) {
            return json(['code' => 404, 'message' => '商品不存在']);
        }

        // 验证数据
        if (empty($data['goods_name'])) {
            return json(['code' => 400, 'message' => '请输入商品名称']);
        }
        if (empty($data['goods_no'])) {
            return json(['code' => 400, 'message' => '请输入商品编码']);
        }
        if (empty($data['category_id'])) {
            return json(['code' => 400, 'message' => '请选择商品分类']);
        }

        // 检查商品编码是否已存在（排除自身）
        $exists = RegisteredProduct::where('goods_no', $data['goods_no'])
            ->where('id', '!=', $id)
            ->exists();
        if ($exists) {
            return json(['code' => 400, 'message' => '该商品编码已存在']);
        }

        $data['updated_at'] = date('Y-m-d H:i:s');
        $product->update($data);

        return json([
            'code' => 200,
            'message' => '更新成功',
            'data' => $product
        ]);
    }

    /**
     * 删除商品
     */
    public function destroy(Request $request, $id)
    {
        $product = RegisteredProduct::find($id);
        if (!$product) {
            return json(['code' => 404, 'message' => '商品不存在']);
        }

        $product->delete();

        return json([
            'code' => 200,
            'message' => '删除成功'
        ]);
    }
} 