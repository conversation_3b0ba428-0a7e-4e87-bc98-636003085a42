<?php

namespace app\controller\system;

use support\Request;
use app\controller\Controller;
use app\model\Dictionary;
use app\model\DictionaryItem;

class DictionaryController extends Controller
{
    /**
     * 获取字典列表
     * @param Request $request
     * @return \support\Response
     */
    public function list(Request $request)
    {
        $page = $request->get('page', 1);
        $pageSize = $request->get('pageSize', 10);
        $name = $request->get('name', '');
        $code = $request->get('code', '');
        $status = $request->get('status', '');

        $query = Dictionary::when($name, function ($query) use ($name) {
                return $query->where('name', 'like', "%{$name}%");
            })
            ->when($code, function ($query) use ($code) {
                return $query->where('code', 'like', "%{$code}%");
            })
            ->when($status !== '', function ($query) use ($status) {
                return $query->where('status', $status);
            });

        $total = $query->count();
        $list = $query->orderBy('id', 'desc')
            ->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->get();

        return json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'pageSize' => $pageSize
            ]
        ]);
    }

    /**
     * 创建字典
     * @param Request $request
     * @return \support\Response
     */
    public function create(Request $request)
    {
        $data = $request->post();
        
        // 验证字典编码是否已存在
        $exists = Dictionary::where('code', $data['code'])->exists();
        if ($exists) {
            return json(['code' => 400, 'message' => '字典编码已存在']);
        }

        try {
            Dictionary::create($data);
            return json(['code' => 200, 'message' => '创建成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '创建失败：' . $e->getMessage()]);
        }
    }

    /**
     * 更新字典
     * @param Request $request
     * @param int $id
     * @return \support\Response
     */
    public function update(Request $request, $id)
    {
        $data = $request->post();
        
        // 验证字典编码是否已存在（排除自身）
        $exists = Dictionary::where('code', $data['code'])
            ->where('id', '!=', $id)
            ->exists();
        if ($exists) {
            return json(['code' => 400, 'message' => '字典编码已存在']);
        }

        try {
            $dict = Dictionary::find($id);
            if (!$dict) {
                return json(['code' => 404, 'message' => '字典不存在']);
            }

            $dict->update($data);
            return json(['code' => 200, 'message' => '更新成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除字典
     * @param Request $request
     * @param int $id
     * @return \support\Response
     */
    public function delete(Request $request, $id)
    {
        try {
            $dict = Dictionary::find($id);
            if (!$dict) {
                return json(['code' => 404, 'message' => '字典不存在']);
            }

            // 删除字典及其关联的字典项
            $dict->items()->delete();
            $dict->delete();
            
            return json(['code' => 200, 'message' => '删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '删除失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取字典项列表
     * @param Request $request
     * @param int|null $dictId
     * @return \support\Response
     */
    public function items(Request $request, $dictId = null, $dict_code = null)
    {
        $page = $request->get('page', 1);
        $pageSize = $request->get('pageSize', 10);
        $keyword = $request->get('keyword', '');
        $code = $request->get('code', $dict_code);

        $query = DictionaryItem::query();
        
        // 如果提供了字典编码，则通过编码查询
        if ($code) {
            $dict = Dictionary::where('code', $code)->first();
            if ($dict) {
                $query->where('dict_id', $dict->id);
            } else {
                return json([
                    'code' => 404,
                    'message' => '字典不存在',
                    'data' => [
                        'list' => [],
                        'total' => 0,
                        'page' => $page,
                        'pageSize' => $pageSize
                    ]
                ]);
            }
        } elseif ($dictId) {
            // 如果提供了字典ID，则通过ID查询
            $query->where('dict_id', $dictId);
        } else {
            return json([
                'code' => 400,
                'message' => '请提供字典编码或字典ID',
                'data' => [
                    'list' => [],
                    'total' => 0,
                    'page' => $page,
                    'pageSize' => $pageSize
                ]
            ]);
        }
        
        // 添加搜索条件
        if ($keyword) {
            $query->where(function ($query) use ($keyword) {
                $query->where('label', 'like', "%{$keyword}%")
                    ->orWhere('value', 'like', "%{$keyword}%");
            });
        }

        $total = $query->count();
        
        $list = $query->orderBy('sort', 'asc')
            ->orderBy('id', 'asc')
            ->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->get();

        return json([
            'code' => 200,
            'message' => 'success',
            'data' => [
                'list' => $list,
                'total' => $total,
                'page' => $page,
                'pageSize' => $pageSize
            ]
        ]);
    }

    /**
     * 创建字典项
     * @param Request $request
     * @return \support\Response
     */
    public function createItem(Request $request)
    {
        $data = $request->post();
        
        try {
            // 验证字典是否存在
            $dict = Dictionary::find($data['dict_id']);
            if (!$dict) {
                return json(['code' => 404, 'message' => '字典不存在']);
            }

            // 验证字典项值是否重复
            $exists = DictionaryItem::where('dict_id', $data['dict_id'])
                ->where('value', $data['value'])
                ->exists();
            if ($exists) {
                return json(['code' => 400, 'message' => '字典项值已存在']);
            }

            DictionaryItem::create($data);
            return json(['code' => 200, 'message' => '创建成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '创建失败：' . $e->getMessage()]);
        }
    }

    /**
     * 更新字典项
     * @param Request $request
     * @param int $id
     * @return \support\Response
     */
    public function updateItem(Request $request, $id)
    {
        $data = $request->post();
        
        try {
            $item = DictionaryItem::find($id);
            if (!$item) {
                return json(['code' => 404, 'message' => '字典项不存在']);
            }

            // 验证字典项值是否重复（排除自身）
            $exists = DictionaryItem::where('dict_id', $item->dict_id)
                ->where('value', $data['value'])
                ->where('id', '!=', $id)
                ->exists();
            if ($exists) {
                return json(['code' => 400, 'message' => '字典项值已存在']);
            }

            $item->update($data);
            return json(['code' => 200, 'message' => '更新成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '更新失败：' . $e->getMessage()]);
        }
    }

    /**
     * 删除字典项
     * @param Request $request
     * @param int $id
     * @return \support\Response
     */
    public function deleteItem(Request $request, $id)
    {
        try {
            $item = DictionaryItem::find($id);
            if (!$item) {
                return json(['code' => 404, 'message' => '字典项不存在']);
            }

            $item->delete();
            return json(['code' => 200, 'message' => '删除成功']);
        } catch (\Exception $e) {
            return json(['code' => 500, 'message' => '删除失败：' . $e->getMessage()]);
        }
    }
} 