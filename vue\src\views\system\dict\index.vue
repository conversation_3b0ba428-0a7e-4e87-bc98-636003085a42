<template>
  <system-page title="字典设置">
    <div class="dict-container">
      <!-- 操作按钮 -->
      <div class="table-operations">
        <a-button type="primary" size="large" @click="showAddModal">
          <template #icon><PlusOutlined /></template>
          新增字典
        </a-button>
      </div>

      <!-- 字典列表 -->
      <a-table
        :columns="columns"
        :data-source="dictList"
        :loading="loading"
        :pagination="{ 
          total: total,
          current: current,
          pageSize: pageSize,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: total => `共 ${total} 条`
        }"
        :scroll="{ x: 1000 }"
        @change="handleTableChange"
      >
        <!-- 状态列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status ? 'success' : 'error'">
              {{ record.status ? '启用' : '禁用' }}
            </a-tag>
          </template>
          
          <!-- 操作列 -->
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="showItemsModal(record)">
                <template #icon><UnorderedListOutlined /></template>
                字典项
              </a-button>
              <a-button type="link" @click="showEditModal(record)">
                <template #icon><EditOutlined /></template>
                编辑
              </a-button>
              <a-popconfirm
                v-if="record.is_system !== 1"
                title="确定要删除此字典吗？"
                @confirm="handleDelete(record.id)"
                okText="确定"
                cancelText="取消"
              >
                <a-button type="link" danger>
                  <template #icon><DeleteOutlined /></template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 字典表单模态框 -->
      <a-modal
        :title="modalTitle"
        v-model:open="modalVisible"
        @ok="handleModalOk"
        @cancel="handleModalCancel"
        :confirmLoading="modalLoading"
        okText="确定"
        cancelText="取消"
      >
        <a-form
          ref="formRef"
          :model="formState"
          :rules="rules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="字典名称" name="name">
            <a-input v-model:value="formState.name" placeholder="请输入字典名称" :disabled="formState.is_system === 1"/>
          </a-form-item>
          <a-form-item label="字典编码" name="code">
            <a-input v-model:value="formState.code" placeholder="请输入字典编码" :disabled="formState.is_system === 1"/>
          </a-form-item>
          <a-form-item label="状态" name="status">
            <a-switch v-model:checked="formState.status" :disabled="formState.is_system === 1"/>
          </a-form-item>
          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="formState.remark" placeholder="请输入备注信息" :rows="4" />
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 字典项模态框 -->
      <dict-items-modal
        v-model:visible="itemsModalVisible"
        :dict-id="selectedDictId"
        :dict-name="selectedDictName"
      />
    </div>
  </system-page>
</template>

<script>
import { ref, reactive, getCurrentInstance, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UnorderedListOutlined
} from '@ant-design/icons-vue';
import SystemPage from '../components/SystemPage.vue';
import DictItemsModal from './components/DictItemsModal.vue';
import {
  getDictList,
  createDict,
  updateDict,
  deleteDict
} from '@/api/system/dict';

export default {
  name: 'SystemDict',
  components: {
    SystemPage,
    DictItemsModal,
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    UnorderedListOutlined
  },
  setup() {
    const { proxy } = getCurrentInstance();

    // 表格列定义
    const columns = [
      {
        title: '字典名称',
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: '字典编码',
        dataIndex: 'code',
        key: 'code'
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status'
      },
      {
        title: '备注',
        dataIndex: 'remark',
        key: 'remark',
        ellipsis: true
      },
      {
        title: '操作',
        key: 'action',
        width: 320,
        fixed: 'right'
      }
    ];

    // 表格数据
    const dictList = ref([]);
    const loading = ref(false);
    const total = ref(0);
    const current = ref(1);
    const pageSize = ref(10);

    // 获取字典列表
    const fetchDictList = async (params = {}) => {
      loading.value = true;
      try {
        const response = await getDictList({
          page: current.value,
          pageSize: pageSize.value,
          ...params
        });
        if (response.code === 200) {
          dictList.value = response.data.list;
          total.value = response.data.total;
        }
      } catch (error) {
        console.error('获取字典列表失败:', error);
        message.error('获取字典列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 表格变化处理
    const handleTableChange = (pagination) => {
      current.value = pagination.current;
      pageSize.value = pagination.pageSize;
      fetchDictList();
    };

    // 表单相关
    const formRef = ref(null);
    const modalVisible = ref(false);
    const modalLoading = ref(false);
    const modalTitle = ref('新增字典');
    const editingId = ref(null);

    const formState = reactive({
      name: '',
      code: '',
      is_system: 0,
      status: true,
      remark: ''
    });

    const rules = {
      name: [{ required: true, message: '请输入字典名称' }],
      code: [{ required: true, message: '请输入字典编码' }]
    };

    // 字典项模态框
    const itemsModalVisible = ref(false);
    const selectedDictId = ref(null);
    const selectedDictName = ref('');

    // 显示字典项模态框
    const showItemsModal = (record) => {
      selectedDictId.value = record.id;
      selectedDictName.value = record.name;
      itemsModalVisible.value = true;
    };

    // 显示新增模态框
    const showAddModal = () => {
      editingId.value = null;
      modalTitle.value = '新增字典';
      formState.name = '';
      formState.code = '';
      formState.status = true;
      formState.remark = '';
      modalVisible.value = true;
    };

    // 显示编辑模态框
    const showEditModal = (record) => {
      editingId.value = record.id;
      modalTitle.value = '编辑字典';
      Object.assign(formState, record);
      formState.status = record.status == 1;
      modalVisible.value = true;
    };

    // 处理模态框确认
    const handleModalOk = async () => {
      try {
        await formRef.value.validate();
        modalLoading.value = true;

        if (editingId.value) {
          await updateDict(editingId.value, formState);
          message.success('字典更新成功');
        } else {
          await createDict(formState);
          message.success('字典添加成功');
        }
        
        modalVisible.value = false;
        fetchDictList();
      } catch (error) {
        console.error('操作失败:', error);
        message.error('操作失败');
      } finally {
        modalLoading.value = false;
      }
    };

    // 处理模态框取消
    const handleModalCancel = () => {
      modalVisible.value = false;
    };

    // 处理删除
    const handleDelete = async (id) => {
      try {
        await deleteDict(id);
        message.success('删除成功');
        fetchDictList();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    };

    // 初始化加载数据
    onMounted(() => {
      fetchDictList();
    });

    return {
      columns,
      dictList,
      loading,
      total,
      current,
      pageSize,
      formRef,
      formState,
      rules,
      modalVisible,
      modalLoading,
      modalTitle,
      itemsModalVisible,
      selectedDictId,
      selectedDictName,
      handleTableChange,
      showItemsModal,
      showAddModal,
      showEditModal,
      handleModalOk,
      handleModalCancel,
      handleDelete
    };
  }
};
</script>

<style scoped>
.dict-container {
  background: #fff;
  padding: 10px 0;
  border-radius: 8px;
}

.table-operations {
  margin-bottom: 16px;
}
</style> 