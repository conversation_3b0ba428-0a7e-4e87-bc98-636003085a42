import request from '@/utils/request'

// 获取商品列表
export function getProducts(params) {
  return request({
    url: '/products',
    method: 'get',
    params
  })
}

// 搜索商品
export function searchProducts(keyword) {
  return request({
    url: '/products/search',
    method: 'get',
    params: {
      keyword,
      pageSize: 20
    }
  })
}

// 创建商品
export function createProduct(data) {
  return request({
    url: '/products',
    method: 'post',
    data
  })
}

// 更新商品
export function updateProduct(id, data) {
  return request({
    url: `/products/${id}`,
    method: 'put',
    data
  })
}

// 删除商品
export function deleteProduct(id) {
  return request({
    url: `/products/${id}`,
    method: 'delete'
  })
}

// 获取商品数量
export function getProductCount(params) {
  return request({
    url: '/products/count',
    method: 'get',
    params
  })
}

// 获取商品分类
export function getProductCategories() {
  return request({
    url: '/product-categories',
    method: 'get'
  })
} 