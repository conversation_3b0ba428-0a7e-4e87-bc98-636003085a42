{"name": "workerman/webman", "type": "project", "keywords": ["high performance", "http service"], "homepage": "https://www.workerman.net", "license": "MIT", "description": "High performance HTTP Service Framework.", "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "https://www.workerman.net", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/walkor/webman/issues", "forum": "https://wenda.workerman.net/", "wiki": "https://workerman.net/doc/webman", "source": "https://github.com/walkor/webman"}, "require": {"php": ">=8.0", "workerman/webman-framework": "~2.1", "monolog/monolog": "^2.0", "vlucas/phpdotenv": "^5.6", "illuminate/database": "^11.37", "firebase/php-jwt": "^6.10", "illuminate/pagination": "^11.37", "webman/rate-limiter": "^1.1", "workerman/crontab": "^1.0", "guzzlehttp/guzzle": "^7.9", "symfony/console": "^7.2", "illuminate/validation": "^11.41", "illuminate/translation": "^11.41", "workerman/workerman": "~5.1", "webman/database": "~2.1", "dragonmantank/cron-expression": "^3.4"}, "suggest": {"ext-event": "For better performance. "}, "autoload": {"psr-4": {"": "./", "app\\": "./app", "App\\": "./app", "app\\View\\Components\\": "./app/view/components", "plugins\\": "./plugins", "plugins\\customs179\\": "plugins/customs179/src/", "plugins\\zto\\": "plugins/zto/src/", "plugins\\yto\\": "plugins/yto/src/"}}, "scripts": {"post-package-install": ["support\\Plugin::install"], "post-package-update": ["support\\Plugin::install"], "pre-package-uninstall": ["support\\Plugin::uninstall"]}, "minimum-stability": "dev", "prefer-stable": true}