<template>
  <div class="apps-container">
    <!-- 顶部操作栏 -->
    <div class="header">
      <div class="left">
        <h2 class="title">应用中心</h2>
        <span class="subtitle">管理和配置系统集成的第三方应用</span>
      </div>
      <div class="right">
        <a-space>
          <a-button type="primary" @click="handleRefresh">
            <template #icon><sync-outlined /></template>
            刷新
          </a-button>
          <a-button @click="handleLogs">
            <template #icon><file-outlined /></template>
            日志
          </a-button>
        </a-space>
      </div>
    </div>

    <!-- 应用列表 -->
    <a-row :gutter="[24, 24]" class="app-list">
      <a-col :xs="24" :sm="12" :md="8" :lg="6" v-for="app in appList" :key="app.id">
        <div class="app-card" :class="{ 'disabled': !app.status }">
          <div class="app-header">
            <div class="app-icon">
              <img :src="app.picture" :alt="app.name" />
            </div>
            <div class="app-info">
              <h3 class="app-name">{{ app.name }}</h3>
              <div class="app-meta">
                <span class="version">v{{ app.version }}</span>
                <a-tag :color="app.status ? 'success' : 'default'">
                  {{ app.status ? '已启用' : '已禁用' }}
                </a-tag>
              </div>
            </div>
          </div>
          <div class="app-body">
            <p class="app-desc">{{ app.description }}</p>
            <div class="app-details">
              <div class="detail-item">
                <span class="label">应用编码</span>
                <span class="value">{{ app.code }}</span>
              </div>
              <div class="detail-item" v-if="app.author">
                <span class="label">开发者</span>
                <span class="value">{{ app.author.name }}</span>
              </div>
            </div>
          </div>
          <div class="app-footer">
            <a-space>
              <a-button @click="handleTemplates(app)" class="action-btn">
                <template #icon><setting-outlined /></template>
                配置模板
              </a-button>
              <a-tooltip title="文档">
                <a-button @click="handleDocs(app)" class="action-btn">
                  <template #icon><read-outlined /></template>
                  文档
                </a-button>
              </a-tooltip>
            </a-space>
          </div>
        </div>
      </a-col>
    </a-row>

    <!-- 配置模板弹窗 -->
    <a-modal
      v-model:open="templateModalVisible"
      :title="currentApp?.name + ' - 配置模板'"
      :footer="null"
      width="800px"
    >
      <div class="templates-container">
        <!-- 模板列表 -->
        <div class="templates-list">
          <div class="list-header">
            <a-button type="primary" @click="handleAddTemplate">
              <template #icon><plus-outlined /></template>
              新增模板
            </a-button>
          </div>
          <a-table
            :loading="templatesLoading"
            :columns="templateColumns"
            :data-source="configTemplates"
            :pagination="false"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'created_at'">
                {{ formatDate(record.created_at) }}
              </template>
              <template v-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" @click="handleEditTemplate(record)">
                    编辑
                  </a-button>
                  <a-button type="link" @click="handleCopyTemplate(record)">
                    复制
                  </a-button>
                  <a-button type="link" @click="handleCopyApi(record)" v-if="currentApp?.code === 'CUSTOMS179'">
                    复制接口
                  </a-button>
                  <a-button type="link" danger @click="showDeleteModal(record)">
                    删除
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </a-modal>

    <!-- 模板编辑弹窗 -->
    <a-modal
      v-model:open="showTemplateForm"
      :title="templateForm.id ? '编辑模板' : '新增模板'"
      @ok="handleTemplatesSave"
      @cancel="handleTemplateFormCancel"
      width="720px"
    >
      <a-form
        ref="templateFormRef"
        :model="templateForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item 
          label="模板名称" 
          name="name" 
          :rules="[{ required: true, message: '请输入模板名称' }]"
        >
          <a-input v-model:value="templateForm.name" placeholder="请输入模板名称" />
        </a-form-item>
        <a-form-item label="模板描述" name="description">
          <a-textarea 
            v-model:value="templateForm.description" 
            placeholder="请输入模板描述" 
            :rows="2" 
          />
        </a-form-item>
        <template v-if="currentApp?.settings_schema">
          <a-form-item
            v-for="field in currentApp.settings_schema.fields"
            :key="field.field"
            :label="field.label"
            :name="['settings', field.field]"
            :rules="[{ required: field.required, message: '请输入' + field.label }]"
            :extra="field.description"
          >
            <!-- 文本输入 -->
            <a-input
              v-if="field.type === 'text'"
              v-model:value="templateForm.settings[field.field]"
              :placeholder="field.placeholder"
            />
            <!-- 密码输入 -->
            <a-input-password
              v-else-if="field.type === 'password'"
              v-model:value="templateForm.settings[field.field]"
              :placeholder="field.placeholder"
            />
            <!-- 开关 -->
            <a-switch
              v-else-if="field.type === 'switch'"
              v-model:checked="templateForm.settings[field.field]"
            />
            <!-- 选择器 -->
            <a-select
              v-else-if="field.type === 'select'"
              v-model:value="templateForm.settings[field.field]"
              :options="field.options"
              :placeholder="field.placeholder"
              :get-popup-container="getPopupContainer"
            />
            <!-- 多选框 -->
            <a-checkbox-group
              v-else-if="field.type === 'checkbox'"
              v-model:value="templateForm.settings[field.field]"
              :options="field.options"
            />
          </a-form-item>
        </template>
      </a-form>
    </a-modal>

    <!-- 删除确认弹框 -->
    <a-modal
      v-model:open="deleteModalVisible"
      title="删除确认"
      @ok="confirmDelete"
      @cancel="cancelDelete"
      :confirmLoading="deleteLoading"
    >
      <p>确定要删除这个配置模板吗？</p>
    </a-modal>
  </div>
</template>
<script>
import { defineComponent, ref, reactive, onMounted } from 'vue';
import {
  SyncOutlined,
  SettingOutlined,
  FileOutlined,
  ReadOutlined,
  PlusOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { 
  getApps, 
  getAppTemplates,
  saveAppTemplate,
  deleteAppTemplate
} from '@/api/system/app';
import { getDictionaryItems } from '@/api/system/dictionary';
import { useRouter } from 'vue-router';
import { formatDate } from '@/utils/utils';

export default defineComponent({
  name: 'Apps',
  components: {
    SyncOutlined,
    SettingOutlined,
    FileOutlined,
    ReadOutlined,
    PlusOutlined
  },
  setup() {
    // 应用列表
    const appList = ref([]);
    const loading = ref(false);

    // 当前应用
    const currentApp = ref(null);

    // 路由实例
    const router = useRouter();

    // 跳转日志
    const handleLogs = () => {
      router.push({ name: 'AppsLogs' });
    };

    const getPopupContainer = (triggerNode) => {
        return triggerNode.parentElement;
    };

    // 获取应用列表
    const fetchApps = async () => {
      loading.value = true;
      try {
        const res = await getApps();
        if (res.code === 200) {
          appList.value = res.data;
        } else {
          message.error(res.msg || '获取应用列表失败');
        }
      } catch (error) {
        console.error('获取应用列表失败:', error);
        message.error('获取应用列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 刷新列表
    const handleRefresh = () => {
      fetchApps();
    };

    const handleDocs = (app) => {
      // 打开新窗口
      window.open(app.docUrl, '_blank');
    };

    // 配置模板相关
    const templateModalVisible = ref(false);
    const templatesLoading = ref(false);
    const configTemplates = ref([]);
    const showTemplateForm = ref(false);
    const templateFormRef = ref(null);
    const templateForm = reactive({
      id: '',
      name: '',
      description: '',
      settings: {}
    });
    
    // 模板列表列定义
    const templateColumns = [
      {
        title: '模板名称',
        dataIndex: 'name',
        width: 200
      },
      {
        title: '描述',
        dataIndex: 'description',
        ellipsis: true
      },
      {
        title: '创建时间',
        key: 'created_at',
        dataIndex: 'created_at',
        width: 180
      },
      {
        title: '操作',
        key: 'action',
        width: 300,
        fixed: 'right'
      }
    ];

    // 字典数据缓存
    const dictionaryCache = reactive({});

    // 获取字典数据
    const getDictionaryOptions = async (code) => {
      if (dictionaryCache[code]) {
        return dictionaryCache[code];
      }
      try {
        const res = await getDictionaryItems(code);
        if (res.code === 200) {
          const options = res.data.list.map(item => ({
            label: item.label,
            value: item.id
          }));
          dictionaryCache[code] = options;
          return options;
        }
        return [];
      } catch (error) {
        console.error('获取字典数据失败:', error);
        return [];
      }
    };

    // 处理字段选项
    const getFieldOptions = async (field) => {
      if (field.dictionary_code) {
        return await getDictionaryOptions(field.dictionary_code);
      }
      return field.options || [];
    };

    // 打开配置模板管理
    const handleTemplates = async (app) => {
      currentApp.value = app;
      
      // 如果有选择器字段，先加载字典数据
      if (app.settings_schema?.fields) {
        for (const field of app.settings_schema.fields) {
          if (field.type === 'select' && field.dictionary_code) {
            const options = await getDictionaryOptions(field.dictionary_code);
            field.options = options;
          }
        }
      }

      templateModalVisible.value = true;
      showTemplateForm.value = false;
      fetchTemplates(app.id);
    };
    
    // 获取配置模板列表
    const fetchTemplates = async (appId) => {
      templatesLoading.value = true;
      try {
        const res = await getAppTemplates(appId);
        if (res.code === 200) {
          configTemplates.value = res.data;
        }
      } catch (error) {
        console.error('获取配置模板失败:', error);
        message.error('获取配置模板失败');
      } finally {
        templatesLoading.value = false;
      }
    };
    
    // 新增模板
    const handleAddTemplate = async () => {
      templateForm.id = '';
      templateForm.name = '';
      templateForm.description = '';
      templateForm.settings = {};

      // 设置默认值
      if (currentApp.value?.settings_schema?.fields) {
        for (const field of currentApp.value.settings_schema.fields) {
          // 如果是选择器类型且有字典代码，先获取选项数据
          if (field.type === 'select' && field.dictionary_code) {
            const options = await getDictionaryOptions(field.dictionary_code);
            field.options = options;
          }

          if (field.default !== undefined) {
            templateForm.settings[field.field] = field.default;
          } else {
            // 根据字段类型设置合适的默认值
            switch (field.type) {
              case 'text':
              case 'password':
                templateForm.settings[field.field] = '';
                break;
              case 'switch':
                templateForm.settings[field.field] = false;
                break;
              case 'select':
                templateForm.settings[field.field] = field.options?.[0]?.value ?? '';
                break;
              case 'checkbox':
                templateForm.settings[field.field] = [];
                break;
              default:
                templateForm.settings[field.field] = '';
            }
          }
        }
      }
      
      showTemplateForm.value = true;
    };
    
    // 编辑模板
    const handleEditTemplate = async (template) => {
      // 如果有选择器字段，先加载字典数据
      if (currentApp.value?.settings_schema?.fields) {
        for (const field of currentApp.value.settings_schema.fields) {
          if (field.type === 'select' && field.dictionary_code) {
            const options = await getDictionaryOptions(field.dictionary_code);
            field.options = options;
          }
        }
      }

      showTemplateForm.value = true;
      Object.assign(templateForm, template);
    };
    
    // 复制模板
    const handleCopyTemplate = async (template) => {
      // 如果有选择器字段，先加载字典数据
      if (currentApp.value?.settings_schema?.fields) {
        for (const field of currentApp.value.settings_schema.fields) {
          if (field.type === 'select' && field.dictionary_code) {
            const options = await getDictionaryOptions(field.dictionary_code);
            field.options = options;
          }
        }
      }

      showTemplateForm.value = true;
      templateForm.id = '';
      templateForm.name = template.name + ' - 副本';
      templateForm.description = template.description;
      templateForm.settings = { ...template.settings };
    };
    
    // 删除模板
    const deleteModalVisible = ref(false);
    const deleteLoading = ref(false);
    const currentTemplate = ref(null);

    // 显示删除确认框
    const showDeleteModal = (template) => {
      currentTemplate.value = template;
      deleteModalVisible.value = true;
    };

    // 确认删除
    const confirmDelete = async () => {
      if (!currentTemplate.value) return;
      
      deleteLoading.value = true;
      try {
        const res = await deleteAppTemplate(currentApp.value.id, currentTemplate.value.id);
        if (res.code === 200) {
          message.success('删除成功');
          deleteModalVisible.value = false;
          fetchTemplates(currentApp.value.id);
        }
      } catch (error) {
        console.error('删除模板失败:', error);
        message.error('删除模板失败');
      } finally {
        deleteLoading.value = false;
      }
    };

    // 取消删除
    const cancelDelete = () => {
      currentTemplate.value = null;
      deleteModalVisible.value = false;
    };

    // 保存模板
    const handleTemplatesSave = () => {
      templateFormRef.value?.validate().then(async () => {
        try {
          const res = await saveAppTemplate(currentApp.value.id, {
            id: templateForm.id,
            name: templateForm.name,
            description: templateForm.description,
            settings: templateForm.settings
          });
          if (res.code === 200) {
            message.success('保存成功');
            showTemplateForm.value = false;
            fetchTemplates(currentApp.value.id);
          }
        } catch (error) {
          console.error('保存模板失败:', error);
          message.error('保存模板失败');
        }
      });
    };
    
    // 取消模板表单
    const handleTemplateFormCancel = () => {
      showTemplateForm.value = false;
      templateForm.id = '';
      templateForm.name = '';
      templateForm.description = '';
      templateForm.settings = {};
    };

    // 渲染表单项
    const renderFormItem = (field) => {
      // 如果字段设置为隐藏，则不渲染
      if (field.hidden) {
        return null;
      }

      return h('a-form-item', {
        label: field.label,
        name: ['settings', field.field],
        rules: [{ required: field.required, message: '请输入' + field.label }],
        extra: field.description
      }, [
        // 文本输入
        field.type === 'text' && h('a-input', {
          modelValue: templateForm.settings[field.field],
          'onUpdate:modelValue': val => templateForm.settings[field.field] = val,
          placeholder: field.placeholder,
          readonly: field.readonly
        }),
        // 密码输入
        field.type === 'password' && h('a-input-password', {
          modelValue: templateForm.settings[field.field],
          'onUpdate:modelValue': val => templateForm.settings[field.field] = val,
          placeholder: field.placeholder
        }),
        // 开关
        field.type === 'switch' && h('a-switch', {
          modelValue: templateForm.settings[field.field],
          'onUpdate:modelValue': val => templateForm.settings[field.field] = val
        }),
        // 选择器
        field.type === 'select' && h('a-select', {
          modelValue: templateForm.settings[field.field],
          'onUpdate:modelValue': val => templateForm.settings[field.field] = val,
          options: field.options || [],
          placeholder: field.placeholder,
          getPopupContainer: getPopupContainer
        }),
        // 多选框
        field.type === 'checkbox' && h('a-checkbox-group', {
          modelValue: templateForm.settings[field.field],
          'onUpdate:modelValue': val => templateForm.settings[field.field] = val,
          options: field.options
        })
      ]);
    };

    // 复制接口
    const handleCopyApi = (template) => {
      const baseUrl = window.location.origin;
      const apiUrl = `${baseUrl}/open-api/179?key=${template.settings.key}`;
      navigator.clipboard.writeText(apiUrl).then(() => {
        message.success('接口地址已复制到剪贴板');
      }).catch(() => {
        message.error('复制失败，请手动复制');
      });
    };

    onMounted(() => {
      fetchApps();
    });

    return {
      appList,
      loading,
      currentApp,
      handleLogs,
      handleRefresh,
      handleDocs,
      getPopupContainer,
      templateModalVisible,
      templatesLoading,
      configTemplates,
      showTemplateForm,
      templateFormRef,
      templateForm,
      handleTemplates,
      handleAddTemplate,
      handleEditTemplate,
      handleCopyTemplate,
      handleTemplatesSave,
      handleTemplateFormCancel,
      templateColumns,
      formatDate,
      renderFormItem,
      handleCopyApi,
      deleteModalVisible,
      deleteLoading,
      showDeleteModal,
      confirmDelete,
      cancelDelete
    };
  }
});
</script>

<style lang="less" scoped>
.apps-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding: 16px 24px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);

    .left {
      .title {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: #1f2329;
      }

      .subtitle {
        margin-top: 4px;
        font-size: 14px;
        color: #86909c;
      }
    }
  }

  .app-list {
    .app-card {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      height: 100%;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }

      &.disabled {
        opacity: 0.75;
        background: #fafafa;
      }

      .app-header {
        padding: 20px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #f0f0f0;

        .app-icon {
          width: 48px;
          height: 48px;
          margin-right: 16px;
          border-radius: 8px;
          overflow: hidden;
          flex-shrink: 0;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .app-info {
          flex: 1;
          min-width: 0;

          .app-name {
            margin: 0 0 4px;
            font-size: 16px;
            font-weight: 600;
            color: #1f2329;
            line-height: 1.4;
          }

          .app-meta {
            display: flex;
            align-items: center;
            gap: 8px;

            .version {
              font-size: 12px;
              color: #86909c;
            }
          }
        }
      }

      .app-body {
        flex: 1;
        padding: 16px 20px;

        .app-desc {
          margin: 0 0 16px;
          font-size: 14px;
          color: #4e5969;
          line-height: 1.6;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .app-details {
          .detail-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 13px;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              color: #86909c;
              margin-right: 8px;
              flex-shrink: 0;
            }

            .value {
              color: #4e5969;
              flex: 1;
              min-width: 0;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }

      .app-footer {
        padding: 16px 20px;
        border-top: 1px solid #f0f0f0;
        display: flex;
        justify-content: flex-end;
        background: #fafafa;

        .action-btn {
          height: 32px;
          border-radius: 4px;
          font-size: 13px;
          padding: 0 12px;
          display: flex;
          align-items: center;
          border: 1px solid #e5e6eb;
          background: #fff;
          color: #4e5969;
          transition: all 0.3s;

          &:hover {
            color: #1677ff;
            border-color: #1677ff;
            background: #fff;
          }

          .anticon {
            font-size: 14px;
            margin-right: 4px;
          }

          &.ant-btn-primary {
            background: #1677ff;
            border-color: #1677ff;
            color: #fff;

            &:hover {
              background: #4096ff;
              border-color: #4096ff;
            }
          }
        }
      }
    }
  }

  .templates-container {
    .templates-list {
      margin-bottom: 24px;
      
      .list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        
        h3 {
          margin: 0;
          font-size: 16px;
        }
      }
      
      .template-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        
        .template-info {
          .template-name {
            font-size: 14px;
            font-weight: 500;
            color: #1f2329;
          }
          
          .template-desc {
            font-size: 12px;
            color: #86909c;
            margin-top: 4px;
          }
        }
      }
    }
  }
}
</style>
