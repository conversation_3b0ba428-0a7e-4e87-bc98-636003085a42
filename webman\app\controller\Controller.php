<?php

namespace app\controller;

use support\Request;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Exception;

class Controller
{
    /**
     * 从请求中获取当前登录用户ID
     * @param Request $request
     * @return int|null
     */
    protected function getUserId(Request $request)
    {
        try {
            $token = $request->header('Authorization');
            if (empty($token)) {
                return null;
            }

            // 如果token包含Bearer前缀，去掉它
            if (strpos($token, 'Bearer ') === 0) {
                $token = substr($token, 7);
            }

            // 验证token
            $key = config('app.jwt_key');
            if (empty($key)) {
                return null;
            }

            $decoded = JWT::decode($token, new Key($key, 'HS256'));
            return $decoded->data->uid ?? null;

        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * 从请求中获取当前登录用户信息
     * @param Request $request
     * @return object|null
     */
    protected function getUserInfo(Request $request)
    {
        try {
            $token = $request->header('Authorization');
            if (empty($token)) {
                return null;
            }

            // 如果token包含Bearer前缀，去掉它
            if (strpos($token, 'Bearer ') === 0) {
                $token = substr($token, 7);
            }

            // 验证token
            $key = config('app.jwt_key');
            if (empty($key)) {
                return null;
            }

            $decoded = JWT::decode($token, new Key($key, 'HS256'));
            return $decoded->data ?? null;

        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * 验证token是否有效
     * @param Request $request
     * @return bool
     */
    protected function verifyToken(Request $request)
    {
        try {
            $token = $request->header('Authorization');
            if (empty($token)) {
                return false;
            }

            // 如果token包含Bearer前缀，去掉它
            if (strpos($token, 'Bearer ') === 0) {
                $token = substr($token, 7);
            }

            // 验证token
            $key = config('app.jwt_key');
            if (empty($key)) {
                return false;
            }

            JWT::decode($token, new Key($key, 'HS256'));
            return true;

        } catch (Exception $e) {
            return false;
        }
    }
}