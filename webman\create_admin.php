<?php
require_once __DIR__ . '/vendor/autoload.php';

// 加载.env文件
if (is_file(__DIR__ . '/.env')) {
    $env = parse_ini_file(__DIR__ . '/.env', true);
    foreach ($env as $key => $val) {
        $name = strtoupper($key);
        if (!isset($_SERVER[$name])) {
            $_SERVER[$name] = $val;
        }
        if (!isset($_ENV[$name])) {
            $_ENV[$name] = $val;
        }
    }
}

// 加载数据库配置
$config = require __DIR__ . '/config/database.php';


// 配置数据库连接
use Illuminate\Database\Capsule\Manager as Capsule;
$capsule = new Capsule;
$capsule->addConnection($config['connections'][$config['default']]);
$capsule->setAsGlobal();
$capsule->bootEloquent();

// 创建管理员账户
use app\model\Member;

try {
    // 检查是否已存在管理员账户
    $admin = Member::where('username', 'admin')->first();
    if ($admin) {
        echo "管理员账户已存在！\n";
        exit(1);
    }

    // 创建新的管理员账户
    $member = new Member;
    $member->username = 'admin';
    $member->password = password_hash('admin321', PASSWORD_DEFAULT);
    $member->realname = '系统管理员';
    $member->status = 1;
    $member->save();

    echo "管理员账户创建成功！\n";
    echo "用户名: admin\n";
    echo "密码: admin321\n";
    echo "请登录后立即修改密码！\n";
    exit(0);
} catch (Exception $e) {
    echo "创建管理员账户失败：" . $e->getMessage() . "\n";
    exit(1);
}

