<?php
namespace app\model;

use support\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Role extends Model
{
    use SoftDeletes;

    protected $table = 'roles';
    
    protected $fillable = [
        'name',
        'code',
        'status',
        'remark'
    ];

    protected $casts = [
        'status' => 'integer'
    ];

    /**
     * 获取角色的所有权限
     */
    public function permissions()
    {
        return $this->belongsToMany(Permission::class, 'role_permissions', 'role_id', 'permission_id');
    }

    /**
     * 获取拥有此角色的用户
     */
    public function members()
    {
        return $this->belongsToMany(Member::class, 'member_roles', 'role_id', 'member_id');
    }
} 