import { useUserStore } from '@/stores/user';

export default {
  mounted(el, binding) {
    const userStore = useUserStore();
    const { value } = binding;
    
    // 支持数组形式传入多个权限码
    if (Array.isArray(value)) {
      const hasPermission = value.some(code => 
        userStore.hasButtonPermission(code)
      );
      if (!hasPermission) {
        el.parentNode?.removeChild(el);
      }
    }
    // 支持字符串形式传入单个权限码
    else if (typeof value === 'string') {
      if (!userStore.hasButtonPermission(value)) {
        el.parentNode?.removeChild(el);
      }
    }
    else {
      console.warn('v-permission指令需要传入权限码字符串或数组');
    }
  }
};
