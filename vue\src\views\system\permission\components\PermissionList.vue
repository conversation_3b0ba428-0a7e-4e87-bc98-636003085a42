<template>
  <div class="permission-list">
    <!-- 操作按钮 -->
    <div class="table-operations">
      <a-button type="primary" size="large" @click="showAddModal">
        <template #icon><PlusOutlined /></template>
        新增权限
      </a-button>
    </div>

    <!-- 权限树形表格 -->
    <a-table
      :columns="columns"
      :data-source="permissionList"
      :loading="loading"
      :pagination="false"
      :row-key="record => record.id"
      :default-expand-all="true"
      :indent-size="20"
      childrenColumnName="children"
    >
      <!-- 图标列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'icon'">
          <component :is="record.icon" v-if="record.icon" />
        </template>

        <!-- 类型列 -->
        <template v-if="column.key === 'type'">
          <a-tag :color="record.menu_type === 'menu' ? 'blue' : 'green'">
            {{ record.menu_type === 'menu' ? '菜单' : '按钮' }}
          </a-tag>
        </template>

        <!-- 状态列 -->
        <template v-if="column.key === 'status'">
          <a-tag :color="record.status ? 'success' : 'error'">
            {{ record.status ? '启用' : '禁用' }}
          </a-tag>
        </template>
        
        <!-- 操作列 -->
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" @click="showAddModal(record)">
              <template #icon><PlusOutlined /></template>
              新增子权限
            </a-button>
            <a-button type="link" @click="showEditModal(record)">
              <template #icon><EditOutlined /></template>
              编辑
            </a-button>
            <a-popconfirm
              title="确定要删除此权限吗？"
              @confirm="handleDelete(record.id)"
              okText="确定"
              cancelText="取消"
            >
              <a-button type="link" danger>
                <template #icon><DeleteOutlined /></template>
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 权限表单模态框 -->
    <a-modal
      :title="modalTitle"
      :open="modalVisible"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :confirmLoading="modalLoading"
      okText="确定"
      cancelText="取消"
      width="700px"
    >
      <div class="permission-tips">
        <a-alert
          message="权限配置说明"
          type="info"
          show-icon
          style="margin-bottom: 16px"
        >
          <template #description>
            <div>
              <p>1. 菜单权限：用于生成左侧导航菜单</p>
              <ul>
                <li>权限名称：菜单显示名称，如：用户管理</li>
                <li>权限编码：system:user:list（遵循 模块:菜单:操作 格式）</li>
                <li>路由地址：/system/user（前端路由路径，以 / 开头）</li>
                <li>组件路径：system/user/index （Vue组件相对路径）</li>
                <li>图标：菜单图标，参考 antdv 图标库</li>
              </ul>
              <p>2. 按钮权限：用于控制页面中按钮的显示/隐藏</p>
              <ul>
                <li>权限名称：按钮功能名称，如：添加用户</li>
                <li>权限编码：system:user:create（按钮操作权限编码）</li>
              </ul>
              <p>3. 常用示例：</p>
              <ul>
                <li>菜单权限：system:user:list（用户列表）</li>
                <li>按钮权限：create（新增）、update（编辑）、delete（删除）</li>
                <li>图标参考：UserOutlined、TeamOutlined、SafetyCertificateOutlined</li>
              </ul>
            </div>
          </template>
        </a-alert>
      </div>

      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="权限名称" name="name">
          <a-input v-model:value="formState.name" placeholder="请输入权限名称，如：用户管理" />
        </a-form-item>
        <a-form-item 
          label="权限编码" 
          name="permission_code"
          extra="遵循 模块:菜单:操作 格式，如：system:user:list"
        >
          <a-input 
            v-model:value="formState.permission_code" 
            placeholder="请输入权限编码，如：system:user:list"
            :disabled="!!editingId"
          />
        </a-form-item>
        <a-form-item label="权限类型" name="type">
          <a-radio-group
            v-model:value="formState.type"
            button-style="solid"
            @change="handleTypeChange"
          >
            <a-radio-button value="menu">菜单权限</a-radio-button>
            <a-radio-button value="button">按钮权限</a-radio-button>
          </a-radio-group>
        </a-form-item>
        <template v-if="formState.type === 'menu'">
          <a-form-item 
            label="路由地址" 
            name="path"
            :rules="[{ required: true, message: '请输入路由地址' }]"
            extra="前端路由路径，以 / 开头"
          >
            <a-input v-model:value="formState.path" placeholder="请输入路由地址，如：/system/user" />
          </a-form-item>
          <a-form-item 
            label="组件路径" 
            name="component"
            :rules="[{ required: true, message: '请输入组件路径' }]"
            extra="Vue组件相对路径，不需要带 .vue 后缀"
          >
            <a-input v-model:value="formState.component" placeholder="请输入组件路径，如：system/user/index" />
          </a-form-item>
          <a-form-item 
            label="图标" 
            name="icon"
            extra="输入Ant Design Vue的图标组件名称，如：UserOutlined"
          >
            <a-input v-model:value="formState.icon" placeholder="请输入图标组件名称" />
          </a-form-item>
        </template>
        <a-form-item label="排序" name="sort">
          <a-input-number
            v-model:value="formState.sort"
            :min="0"
            :max="999"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-switch v-model:checked="formState.status" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea 
            v-model:value="formState.remark" 
            placeholder="请输入备注信息"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, getCurrentInstance } from 'vue';
import { message } from 'ant-design-vue';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import {
  getPermissionTree,
  createPermission,
  updatePermission,
  deletePermission
} from '@/api/system/permission';

export default {
  name: 'PermissionList',
  components: {
    PlusOutlined,
    EditOutlined,
    DeleteOutlined
  },
  setup() {
    const { proxy } = getCurrentInstance();

    // 表格列定义
    const columns = [
      {
        title: '权限名称',
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: '权限编码',
        dataIndex: 'permission_code',
        key: 'permission_code'
      },
      {
        title: '图标',
        key: 'icon',
        width: 80
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
        width: 100
      },
      {
        title: '路由地址',
        dataIndex: 'path',
        key: 'path'
      },
      {
        title: '排序',
        dataIndex: 'sort',
        key: 'sort',
        width: 80
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100
      },
      {
        title: '操作',
        key: 'action',
        width: 280,
        fixed: 'right'
      }
    ];

    // 表格数据
    const permissionList = ref([]);
    const loading = ref(false);

    // 获取权限列表
    const fetchPermissionList = async () => {
      loading.value = true;
      try {
        const response = await getPermissionTree();
        if (response.code === 200) {
          permissionList.value = response.data;
        } else {
          message.error(response.msg || '获取权限列表失败');
        }
      } catch (error) {
        console.error('获取权限列表失败:', error);
        message.error('获取权限列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 表单相关
    const formRef = ref(null);
    const modalVisible = ref(false);
    const modalLoading = ref(false);
    const modalTitle = ref('新增权限');
    const editingId = ref(null);
    const parentId = ref(0);

    const formState = reactive({
      name: '',
      permission_code: '',
      type: 'menu',
      path: '',
      component: '',
      icon: '',
      sort: 0,
      status: true,
      remark: ''
    });

    const rules = {
      name: [{ required: true, message: '请输入权限名称' }],
      permission_code: [{ required: true, message: '请输入权限编码' }],
      type: [{ required: true, message: '请选择权限类型' }],
      path: [{ required: false }],
      component: [{ required: false }]
    };

    // 显示新增模态框
    const showAddModal = (record = null) => {
      editingId.value = null;
      parentId.value = record ? record.id : 0;
      modalTitle.value = record ? '新增子权限' : '新增权限';
      
      Object.assign(formState, {
        name: '',
        permission_code: '',
        type: 'menu',
        path: '',
        component: '',
        icon: '',
        sort: 0,
        status: true,
        remark: ''
      });
      
      modalVisible.value = true;
    };

    // 显示编辑模态框
    const showEditModal = (record) => {
      editingId.value = record.id;
      parentId.value = record.parent_id;
      modalTitle.value = '编辑权限';
      
      // 重置表单状态
      formState.name = record.name;
      formState.permission_code = record.permission_code;
      formState.type = record.menu_type;  // 注意这里是menu_type
      formState.path = record.path || '';
      formState.component = record.component || '';
      formState.icon = record.icon || '';
      formState.sort = record.sort || 0;
      formState.status = record.status === 1;
      formState.remark = record.remark || '';
      
      modalVisible.value = true;
    };

    // 处理模态框确认
    const handleModalOk = async () => {
      try {
        await formRef.value.validate();
        modalLoading.value = true;

        const data = {
          name: formState.name,
          permission_code: formState.permission_code,
          menu_type: formState.type,
          path: formState.path,
          component: formState.component,
          icon: formState.icon,
          sort: formState.sort,
          status: formState.status ? 1 : 0,
          remark: formState.remark,
          parent_id: parentId.value
        };

        let response;
        if (editingId.value) {
          response = await updatePermission(editingId.value, data);
        } else {
          response = await createPermission(data);
        }

        if (response.code === 200) {
          message.success(editingId.value ? '更新成功' : '创建成功');
          modalVisible.value = false;
          fetchPermissionList();
        } else {
          message.error(response.msg || (editingId.value ? '更新失败' : '创建失败'));
        }
      } catch (error) {
        console.error('保存权限失败:', error);
        message.error('保存失败');
      } finally {
        modalLoading.value = false;
      }
    };

    // 处理删除
    const handleDelete = async (id) => {
      try {
        const response = await deletePermission(id);
        if (response.code === 0) {
          message.success('删除成功');
          fetchPermissionList();
        } else {
          message.error(response.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除权限失败:', error);
        message.error('删除失败');
      }
    };

    // 处理类型改变
    const handleTypeChange = (e) => {
      const value = e.target.value;
      formState.type = value;
      if (value === 'button') {
        formState.path = '';
        formState.component = '';
        formState.icon = '';
      }
    };

    // 初始化加载数据
    fetchPermissionList();

    return {
      columns,
      permissionList,
      loading,
      formRef,
      formState,
      rules,
      modalVisible,
      modalLoading,
      modalTitle,
      editingId,
      showAddModal,
      showEditModal,
      handleModalOk,
      handleTypeChange,
      handleModalCancel: () => modalVisible.value = false,
      handleDelete
    };
  }
};
</script>

<style scoped>
.permission-list {
  min-height: 400px;
}

.table-operations {
  margin-bottom: 16px;
}

:deep(.ant-radio-button-wrapper) {
  min-width: 100px;
  text-align: center;
}

:deep(.ant-radio-button-wrapper-checked) {
  background: #272343 !important;
  border-color: #272343 !important;
}

:deep(.ant-radio-button-wrapper-checked)::before {
  background-color: #272343 !important;
}
</style> 