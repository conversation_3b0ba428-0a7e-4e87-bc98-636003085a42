<?php

namespace app\controller\system;

use app\model\Schedule;
use app\model\ScheduleLog;
use support\Request;
use support\Response;
use <PERSON>man\Crontab\Crontab;
use Cron\CronExpression;
use GuzzleHttp\Client;
use Exception;

class ScheduleController
{
    /**
     * 获取定时任务列表
     */
    public function list(Request $request)
    {
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);
        $name = $request->input('name');
        $status = $request->input('status');

        $query = Schedule::query();
        
        if ($name) {
            $query->where('name', 'like', "%{$name}%");
        }
        if ($status !== null) {
            $query->where('status', $status);
        }

        $total = $query->count();
        $list = $query->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->orderBy('id', 'desc')
            ->get();

        return json(['code' => 200, 'msg' => 'success', 'data' => [
            'list' => $list,
            'total' => $total
        ]]);
    }

    /**
     * 创建定时任务
     */
    public function create(Request $request)
    {
        $data = $request->post();
        $schedule = Schedule::create($data);
        
        // 如果任务是启用状态，则添加到Crontab
        if ($schedule->status) {
            $this->addToCrontab($schedule);
        }

        return json(['code' => 200, 'msg' => '创建成功', 'data' => $schedule]);
    }

    /**
     * 更新定时任务
     */
    public function update(Request $request, $id)
    {
        $data = $request->post();
        $schedule = Schedule::find($id);
        if (!$schedule) {
            return json(['code' => 404, 'msg' => '任务不存在']);
        }

        // 检查状态是否从启用变为禁用
        $statusChanged = isset($data['status']) && $schedule->status == 1 && $data['status'] == 0;
        
        // 如果状态改变为禁用，先移除任务
        if ($statusChanged) {
            $this->removeCrontab($schedule->id);
        }
        
        // 更新数据
        $schedule->update($data);

        // 如果当前是启用状态，重新添加任务
        if ($schedule->status) {
            // 先移除可能存在的旧任务
            $this->removeCrontab($schedule->id);
            // 添加新任务
            $this->addToCrontab($schedule);
        }

        return json(['code' => 200, 'msg' => '更新成功']);
    }

    /**
     * 删除定时任务
     */
    public function delete(Request $request, $id)
    {
        $schedule = Schedule::find($id);
        if (!$schedule) {
            return json(['code' => 404, 'msg' => '任务不存在']);
        }

        // 从Crontab中移除任务
        $this->removeCrontab($id);
        $schedule->delete();

        return json(['code' => 200, 'msg' => '删除成功']);
    }

    /**
     * 获取可用的类方法列表
     */
    public function getClassMethods()
    {
        $methods = Schedule::getAvailableClassMethods();
        return json(['code' => 200, 'msg' => 'success', 'data' => $methods]);
    }

    /**
     * 添加任务到Crontab
     */
    public function addToCrontab(Schedule $schedule)
    {
        // 如果任务是禁用状态，不添加到定时任务
        if (!$schedule->status) {
            return;
        }

        try {
            // 检查是否是N秒类型的任务
            if (strpos($schedule->cron, '* * * * *') !== false) {
                // 提取秒数
                preg_match('/\*\/(\d+)/', $schedule->cron, $matches);
                if (!empty($matches[1])) {
                    $seconds = (int)$matches[1];
                    // 使用定时器处理秒级任务，并保存定时器ID
                    $timer_id = \Workerman\Timer::add($seconds, function() use ($schedule) {
                        $this->executeTask($schedule);
                    });
                    // 将定时器ID保存到数据库中
                    $schedule->timer_id = $timer_id;
                    $schedule->save();
                    return;
                }
            }

            // 其他类型的任务使用Crontab
            $crontab = new Crontab($schedule->cron, function() use ($schedule) {
                $this->executeTask($schedule);
            });

            // 计算下次执行时间
            if (strpos($schedule->cron, '* * * * *') !== false) {
                // N秒任务
                preg_match('/\*\/(\d+)/', $schedule->cron, $matches);
                if (!empty($matches[1])) {
                    $seconds = (int)$matches[1];
                    $schedule->next_run_time = date('Y-m-d H:i:s', time() + $seconds);
                }
            } else {
                // 其他任务使用Crontab计算下次执行时间
                $cron = new \Cron\CronExpression($schedule->cron);
                $schedule->next_run_time = $cron->getNextRunDate()->format('Y-m-d H:i:s');
            }
            $schedule->save();
        } catch (\Exception $e) {
            error_log("Add to crontab failed: {$e->getMessage()}");
            throw $e;
        }
    }

    /**
     * 执行任务
     */
    private function executeTask(Schedule $schedule, $manual = false)
    {
        $log = new \app\model\ScheduleLog([
            'schedule_id' => $schedule->id,
            'name' => $schedule->name,
            'type' => $schedule->type,
            'target' => $schedule->target,
            'executed_at' => date('Y-m-d H:i:s')
        ]);

        try {
            $result = null;
            switch ($schedule->type) {
                case 'url':
                    $client = new \GuzzleHttp\Client();
                    $response = $client->get($schedule->target);
                    $result = '请求成功';
                    break;
                case 'backup':
                    $result = $this->backupDatabase();
                    break;
                case 'class':
                    $result = Schedule::callClassMethod($schedule->target);
                    break;
            }

            // 更新最后执行时间
            $schedule->last_run_time = date('Y-m-d H:i:s');
            
            // 计算下次执行时间
            if (strpos($schedule->cron, '* * * * *') !== false) {
                // N秒任务
                preg_match('/\*\/(\d+)/', $schedule->cron, $matches);
                if (!empty($matches[1])) {
                    $seconds = (int)$matches[1];
                    $schedule->next_run_time = date('Y-m-d H:i:s', time() + $seconds);
                }
            } else {
                // 其他任务使用Crontab计算下次执行时间
                $cron = \Cron\CronExpression::factory($schedule->cron);
                $schedule->next_run_time = $cron->getNextRunDate()->format('Y-m-d H:i:s');
            }
            
            $schedule->save();

            // 记录成功日志
            $log->status = 'success';
            $log->result = is_string($result) ? $result : json_encode($result, JSON_UNESCAPED_UNICODE);
            $log->save();
            if ($manual) {
                return $log->result;
            }
        } catch (\Exception $e) {
            $log->status = 'failed';
            $log->result = $e->getMessage();
            $log->save();
            if ($manual) {
                throw $e;
            }
        }
    }

    /**
     * 从Crontab中移除任务
     */
    private function removeCrontab($id)
    {
        $schedule = Schedule::find($id);
        if ($schedule) {
            try {
                // 移除定时器
                if ($schedule->timer_id) {
                    \Workerman\Timer::del((int)$schedule->timer_id);
                    $schedule->timer_id = null;
                    $schedule->save();
                }
                
                // 移除Crontab任务
                Crontab::remove($id);
                
                // 清除下次执行时间
                $schedule->next_run_time = null;
                $schedule->save();
                
                return true;
            } catch (\Exception $e) {
                error_log("Remove crontab failed: {$e->getMessage()}");
                return false;
            }
        }
        return false;
    }

    /**
     * 备份数据库
     */
    private function backupDatabase()
    {
        $config = config('database.connections.mysql');
        $backupDir = runtime_path() . '/backup';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }

        // 生成配置文件
        $cnfPath = $backupDir . '/.my.cnf';
        $cnfContent = sprintf(
            "[client]\nhost=%s\nport=%s\nuser=%s\npassword=%s\n",
            $config['host'],
            $config['port'],
            $config['username'],
            $config['password']
        );
        file_put_contents($cnfPath, $cnfContent);
        chmod($cnfPath, 0600);

        try {
            $filename = $backupDir . '/backup_' . date('Y-m-d_His') . '.sql';
            // 使用配置文件执行mysqldump
            $command = sprintf(
                'mysqldump --defaults-file=%s %s > %s',
                $cnfPath,
                $config['database'],
                $filename
            );

            $output = [];
            $returnVar = 0;
            exec($command . ' 2>&1', $output, $returnVar);

            // 删除临时配置文件
            unlink($cnfPath);

            if ($returnVar !== 0) {
                throw new \Exception('数据库备份失败: ' . implode("\n", $output));
            }

            // 检查备份文件是否生成且大小大于0
            if (!file_exists($filename) || filesize($filename) === 0) {
                throw new \Exception('备份文件生成失败');
            }

            // 压缩备份文件
            $gzFilename = $filename . '.gz';
            $gz = gzopen($gzFilename, 'w9');
            gzwrite($gz, file_get_contents($filename));
            gzclose($gz);

            // 删除原始SQL文件
            unlink($filename);

            // 清理旧的备份文件（保留最近30天的）
            $this->cleanOldBackups($backupDir, 30);

            return sprintf('数据库备份成功，文件：%s', basename($gzFilename));
        } catch (\Exception $e) {
            // 确保删除临时配置文件
            if (file_exists($cnfPath)) {
                unlink($cnfPath);
            }
            throw $e;
        }
    }

    /**
     * 清理旧的备份文件
     * @param string $backupDir 备份目录
     * @param int $keepDays 保留天数
     */
    private function cleanOldBackups($backupDir, $keepDays)
    {
        $files = glob($backupDir . '/backup_*.sql.gz');
        $now = time();
        
        foreach ($files as $file) {
            if (is_file($file)) {
                if ($now - filemtime($file) >= 86400 * $keepDays) {
                    unlink($file);
                }
            }
        }
    }

    /**
     * 获取任务执行日志
     */
    public function logs(Request $request, $id)
    {
        $page = $request->input('page', 1);
        $pageSize = $request->input('pageSize', 10);
        $status = $request->input('status');

        $query = \app\model\ScheduleLog::query()->where('schedule_id', $id);
        
        if ($status) {
            $query->where('status', $status);
        }

        $total = $query->count();
        $list = $query->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->orderBy('id', 'desc')
            ->get();

        return json(['code' => 200, 'msg' => 'success', 'data' => [
            'list' => $list,
            'total' => $total
        ]]);
    }

    /**
     * 获取定时任务进程状态
     */
    public function status()
    {
        try {
            // 检查心跳时间来判断进程是否正常运行
            $heartbeatFile = runtime_path() . '/schedule.heartbeat';
            $isRunning = false;
            
            if (file_exists($heartbeatFile)) {
                $lastHeartbeat = file_get_contents($heartbeatFile);
                // 如果最后心跳时间在60秒内，认为进程正常运行
                $isRunning = (time() - strtotime($lastHeartbeat)) < 60;
            }
            
            // 获取进程启动时间
            $startTime = file_exists(runtime_path() . '/schedule.start') ? file_get_contents(runtime_path() . '/schedule.start') : null;
            
            // 获取当前启用的任务数量
            $taskCount = Schedule::where('status', 1)->count();
            
            return json([
                'code' => 200,
                'msg' => 'success',
                'data' => [
                    'status' => $isRunning ? 'running' : 'stopped',
                    'start_time' => $startTime,
                    'last_heartbeat' => file_exists($heartbeatFile) ? file_get_contents($heartbeatFile) : null,
                    'task_count' => $taskCount
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => $e->getMessage()
            ]);
        }
    }

    /**
     * 手动执行定时任务
     */
    public function run(Request $request, $id)
    {
        $schedule = Schedule::find($id);
        if (!$schedule) {
            return json(['code' => 404, 'msg' => '任务不存在']);
        }
        try {
            $result = $this->executeTask($schedule, true); // 标记为手动执行
            return json(['code' => 200, 'msg' => '执行成功', 'data' => $result]);
        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '执行失败: ' . $e->getMessage()]);
        }
    }
} 