<template>
  <div class="system-container-main">
    <div class="page-header">
      <h2>系统管理</h2>
      <p class="subtitle">系统配置与管理功能</p>
    </div>

    <div class="system-grid">
      <div class="system-card" v-for="module in systemModules" :key="module.key" @click="navigateTo(module.path)">
        <div class="card-icon">
          <component :is="module.icon" />
        </div>
        <div class="card-content">
          <h3>{{ module.title }}</h3>
          <p>{{ module.description }}</p>
          <div class="card-status-wrapper">
            <template v-if="module.key === 'schedule'">
              <a-tooltip placement="right">
                <template #title>
                  <div v-if="processInfo">
                    <p>启动时间：{{ processInfo.start_time || '-' }}</p>
                    <p>最后心跳：{{ processInfo.last_heartbeat || '-' }}</p>
                    <p>管理任务：{{ processInfo.task_count || 0 }}个</p>
                  </div>
                </template>
                <div class="card-status" :class="module.status">{{ module.statusText }}</div>
              </a-tooltip>
            </template>
            <template v-else>
              <div class="card-status" :class="module.status">{{ module.statusText }}</div>
            </template>
          </div>
        </div>
        <div class="card-arrow">
          <RightOutlined />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  SafetyCertificateOutlined,
  DeploymentUnitOutlined,
  BookOutlined,
  FileSearchOutlined,
  FieldTimeOutlined,
  CloudDownloadOutlined,
  DesktopOutlined,
  SettingOutlined,
  ReloadOutlined,
  RightOutlined,
  SisternodeOutlined,
  ApiOutlined,
  CodeOutlined
} from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import { ref, onMounted, onBeforeUnmount } from 'vue';
import { getScheduleStatus } from '@/api/system/schedule';

export default {
  name: 'SystemIndex',
  components: {
    SafetyCertificateOutlined,
    DeploymentUnitOutlined,
    BookOutlined,
    FileSearchOutlined,
    FieldTimeOutlined,
    CloudDownloadOutlined,
    DesktopOutlined,
    SettingOutlined,
    ReloadOutlined,
    RightOutlined,
    SisternodeOutlined,
    ApiOutlined,
    CodeOutlined
  },
  setup() {
    const router = useRouter();
    const processInfo = ref(null);
    let statusTimer = null;

    const systemModules = ref([
      {
        key: 'enterprise',
        title: '企业管理',
        description: '电商平台、电商、物流、支付、申报企业管理',
        icon: DeploymentUnitOutlined,
        path: '/system/enterprise',
        status: 'normal',
        statusText: ''
      },
      {
        key: 'template',
        title: '申报模板',
        description: '各渠道申报模板管理',
        icon: SisternodeOutlined,
        path: '/system/template',
        status: 'normal',
        statusText: ''
      },
      {
        key: 'dict',
        title: '字典设置',
        description: '系统字典数据维护，管理系统基础数据',
        icon: BookOutlined,
        path: '/system/dict',
        status: 'normal',
        statusText: ''
      },
      {
        key: 'permission',
        title: '权限管理',
        description: '用户、角色及权限配置，管理系统访问权限',
        icon: SafetyCertificateOutlined,
        path: '/system/permission',
        status: 'normal',
        statusText: ''
      },
      {
        key: 'log',
        title: '日志管理',
        description: '系统操作日志查询，追踪系统使用记录',
        icon: FileSearchOutlined,
        path: '/system/log',
        status: 'warning',
        statusText: ''
      },
      {
        key: 'schedule',
        title: '定时任务',
        description: '系统定时任务配置，管理自动化任务',
        icon: FieldTimeOutlined,
        path: '/system/schedule',
        status: 'normal',
        statusText: '运行正常',
        tooltip: processInfo
      },
      {
        key: 'api',
        title: 'API密钥',
        description: 'API密钥管理，用于API接口调用',
        icon: ApiOutlined,
        path: '/system/apikey',
        status: 'normal',
        statusText: ''
      },
      {
        key: 'apikeytest',
        title: 'API测试',
        description: 'API测试，用于API接口调用',
        icon: CodeOutlined,
        path: '/system/apikey/test',
        status: 'normal',
        statusText: ''
      },
      {
        key: 'download',
        title: '下载中心',
        description: '系统文件下载管理，统一文件管理',
        icon: CloudDownloadOutlined,
        path: '/system/download',
        status: 'normal',
        statusText: ''
      },
      {
        key: 'info',
        title: '系统信息',
        description: '系统运行状态监控，查看系统信息',
        icon: DesktopOutlined,
        path: '/system/info',
        status: 'error',
        statusText: ''
      }
    ]);

    // 获取进程状态
    const fetchProcessStatus = async () => {
      try {
        const res = await getScheduleStatus();
        if (res.code === 200) {
          processInfo.value = res.data;
          // 更新定时任务模块的状态
          const scheduleModule = systemModules.value.find(m => m.key === 'schedule');
          if (scheduleModule) {
            scheduleModule.status = processInfo.value.status === 'running' ? 'normal' : 'error';
            scheduleModule.statusText = processInfo.value.status === 'running' ? '运行正常' : '已停止';
          }
        }
      } catch (error) {
        console.error('获取进程状态失败:', error);
      }
    };

    const navigateTo = (path) => {
      router.push(path);
    };

    const handleRefresh = () => {
      fetchProcessStatus();
    };

    // 定期更新状态
    onMounted(() => {
      fetchProcessStatus();
      statusTimer = setInterval(fetchProcessStatus, 30000);
    });

    // 组件销毁前清除定时器
    onBeforeUnmount(() => {
      if (statusTimer) {
        clearInterval(statusTimer);
      }
    });

    return {
      systemModules,
      navigateTo,
      handleRefresh,
      processInfo
    };
  }
};
</script>

<style scoped>
.system-container-main {
  padding: 24px;
  min-height: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #272343;
}

.subtitle {
  margin-top: 8px;
  color: #666;
  font-size: 14px;
}

.header-actions {
  margin-top: 16px;
  display: flex;
  gap: 12px;
}

.system-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.system-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid #f0f0f0;
}

.system-card:hover {
  border-color: #272343;
}

.card-icon {
  width: 40px;
  height: 40px;
  background: #f5f5f5;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #272343;
  flex-shrink: 0;
}

.card-content {
  flex: 1;
  min-width: 0;
}

.card-content h3 {
  margin: 0 0 8px;
  font-size: 16px;
  font-weight: 500;
  color: #272343;
}

.card-content p {
  margin: 0 0 12px;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.card-status {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.card-status.normal {
  background: #f6ffed;
  color: #52c41a;
}

.card-status.warning {
  background: #fffbe6;
  color: #faad14;
}

.card-status.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.card-arrow {
  color: #bfbfbf;
  font-size: 14px;
  transition: transform 0.3s;
  flex-shrink: 0;
  margin-top: 12px;
}

.system-card:hover .card-arrow {
  color: #272343;
  transform: translateX(4px);
}

@media screen and (max-width: 1200px) {
  .system-grid {
    grid-template-columns: 1fr;
  }
}

.card-status-wrapper {
  display: inline-block;
}
</style> 