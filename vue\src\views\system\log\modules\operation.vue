<template>
  <div class="operation-log">
    <!-- 搜索表单 -->
    <div class="table-page-search-wrapper">
      <a-form layout="inline">
        <a-row :gutter="48">
          <a-col :md="6" :sm="24">
            <a-form-item label="用户名">
              <a-input v-model:value="searchForm.username" placeholder="请输入用户名" allowClear />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="模块">
                <a-input v-model:value="searchForm.module" placeholder="请输入模块" allowClear />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="操作">
              <a-input v-model:value="searchForm.action" placeholder="请输入操作" allowClear />
            </a-form-item>
          </a-col>
          <a-col :md="6" :sm="24">
            <a-form-item label="时间范围">
              <a-range-picker 
                v-model:value="searchForm.timeRange"
                :show-time="{ format: 'HH:mm:ss' }"
                format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24" style="text-align: right">
            <a-space>
              <a-button type="primary" @click="handleSearch">
                <template #icon><SearchOutlined /></template>
                搜索
              </a-button>
              <a-button @click="handleReset">
                <template #icon><ReloadOutlined /></template>
                重置
              </a-button>
              <a-button danger @click="handleClear">
                <template #icon><DeleteOutlined /></template>
                清空
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <!-- 日志表格 -->
    <a-table
      :columns="columns"
      :data-source="data"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      :scroll="{ x: 1200 }"
    >
      <!-- 请求参数列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'params'">
          <a-button @click="showParams(record.params)">查看</a-button>
        </template>
        <!-- 操作结果列 -->
        <template v-if="column.key === 'result'">
          <a-button @click="showResult(record.result)">查看</a-button>
        </template>
        <!-- 时间列 -->
        <template v-if="column.key === 'created_at'">
          {{ formatDate(record.created_at) }}
        </template>
      </template>
    </a-table>

    <!-- 参数详情弹窗 -->
    <a-modal
      v-model:open="paramsVisible"
      title="请求参数"
      :footer="null"
      width="800px"
    >
      <pre>{{ formatJson(currentParams) }}</pre>
    </a-modal>

    <!-- 结果详情弹窗 -->
    <a-modal
      v-model:open="resultVisible"
      title="操作结果"
      :footer="null"
      width="800px"
    >
      <pre>{{ formatJson(currentResult) }}</pre>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { SearchOutlined, ReloadOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { getOperationLog, clearOperationLog } from '@/api/system/log/operation';
import { formatDate } from '@/utils/utils';
import dayjs from 'dayjs';

// 日志字段
const columns = [
  { title: '用户名', dataIndex: 'username', width: 120 },
  { title: '模块/控制器', dataIndex: 'module', width: 300 },
  { title: '操作', dataIndex: 'action', width: 120 },
  { title: '请求方法', dataIndex: 'method', width: 100 },
  { title: '请求URL', dataIndex: 'url', width: 200, ellipsis: true },
  { title: '请求参数', dataIndex: 'params', key: 'params', width: 100 },
  { title: 'IP', dataIndex: 'ip', width: 120 },
  { title: '位置', dataIndex: 'location', width: 150 },
  { title: '浏览器', dataIndex: 'browser', width: 150 },
  { title: '操作系统', dataIndex: 'os', width: 120 },
  { title: '操作结果', dataIndex: 'result', key: 'result', width: 100 },
  { title: '操作时间', dataIndex: 'created_at', key: 'created_at', width: 180 }
];

// 搜索表单
const searchForm = ref({
  username: '',
  module: '',
  action: '',
  timeRange: []
});

// 表格数据
const data = ref([]);
const loading = ref(false);
const pagination = ref({
  total: 0,
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条`
});

// 弹窗控制
const paramsVisible = ref(false);
const resultVisible = ref(false);
const currentParams = ref('');
const currentResult = ref('');

// 获取日志列表
const fetchLogs = async () => {
  loading.value = true;
  try {
    const [startTime, endTime] = searchForm.value.timeRange || [];
    const params = {
      page: pagination.value.current,
      pageSize: pagination.value.pageSize,
      username: searchForm.value.username,
      module: searchForm.value.module,
      action: searchForm.value.action,
      startTime: startTime ? dayjs(startTime).format('YYYY-MM-DD HH:mm:ss') : '',
      endTime: endTime ? dayjs(endTime).format('YYYY-MM-DD HH:mm:ss') : ''
    };

    const res = await getOperationLog(params);
    if (res.code === 200) {
      data.value = res.data.list;
      pagination.value.total = res.data.total;
    } else {
      message.error(res.message || '获取操作日志失败');
    }
  } catch (error) {
    console.error('获取操作日志失败:', error);
    message.error('获取操作日志失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.value.current = 1;
  fetchLogs();
};

// 重置搜索
const handleReset = () => {
  searchForm.value = {
    username: '',
    module: '',
    action: '',
    timeRange: []
  };
  handleSearch();
};

// 清空日志
const handleClear = async () => {
  Modal.confirm({
    title: '确认清空',
    content: '是否确认清空所有操作日志？此操作不可恢复！',
    okText: '确认',
    cancelText: '取消',
    okType: 'danger',
    async onOk() {
      try {
        const res = await clearOperationLog();
        if (res.code === 200) {
          message.success('清空成功');
          fetchLogs();
        } else {
          message.error(res.message || '清空失败');
        }
      } catch (error) {
        console.error('清空日志失败:', error);
        message.error('清空日志失败');
      }
    }
  });
};

// 表格变化
const handleTableChange = (pag) => {
  pagination.value.current = pag.current;
  pagination.value.pageSize = pag.pageSize;
  fetchLogs();
};

// 显示参数详情
const showParams = (params) => {
  try {
    currentParams.value = typeof params === 'string' ? JSON.parse(params) : params;
  } catch (e) {
    currentParams.value = params;
  }
  paramsVisible.value = true;
};

// 显示结果详情
const showResult = (result) => {
  try {
    currentResult.value = typeof result === 'string' ? JSON.parse(result) : result;
  } catch (e) {
    currentResult.value = result;
  }
  resultVisible.value = true;
};

// 格式化JSON
const formatJson = (json) => {
  try {
    return JSON.stringify(json, null, 2);
  } catch (e) {
    return json;
  }
};

// 初始化
onMounted(() => {
  fetchLogs();
});
</script>

<style scoped>
.operation-log {
  background: #fff;
  min-height: 100%;
}

.table-page-search-wrapper {
  margin-bottom: 16px;
  padding: 24px;
  background: #fff;
  border-radius: 2px;
  border: 1px solid #e8e8e8;
}

.table-page-search-wrapper :deep(.ant-form-item) {
  display: flex;
  margin-bottom: 16px;
  margin-right: 0;
}

.table-page-search-wrapper :deep(.ant-form-item-label) {
  width: 80px;
  min-width: 80px;
  padding-right: 8px;
}

.table-page-search-wrapper :deep(.ant-form-item-control) {
  flex: 1;
}

.table-page-search-wrapper :deep(.ant-space) {
  margin-bottom: 16px;
}

@media screen and (max-width: 768px) {
  .table-page-search-wrapper :deep(.ant-form-item-label) {
    width: auto;
  }
  
  .table-page-search-wrapper :deep(.ant-space) {
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
}

pre {
  margin: 0;
  padding: 12px;
  background: #f5f5f5;
  border-radius: 4px;
  max-height: 500px;
  overflow: auto;
}
</style> 