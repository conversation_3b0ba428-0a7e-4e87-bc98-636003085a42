<template>
  <div class="registered-products">
    <!-- 页面头部 -->
    <div class="page-header">
      <a-space>
        <a-button @click="goBack">
          <template #icon><left-outlined /></template>
          返回
        </a-button>
        <a-divider type="vertical" />
        <h2>备案商品</h2>
      </a-space>
    </div>

    <a-card :bordered="false">
      <!-- 搜索表单 -->
      <a-form layout="inline" class="table-search-form">
        <a-row :gutter="16" style="width: 100%; margin-bottom: 16px;">
          <a-col :span="6">
            <a-form-item label="商品类型" style="width: 100%;">
              <a-select v-model:value="searchForm.goods_type" placeholder="请选择商品类型" allowClear>
                <a-select-option :value="1">直邮</a-select-option>
                <a-select-option :value="2">保税仓</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="商品名称" style="width: 100%;">
              <a-input v-model:value="searchForm.goods_name" placeholder="请输入商品名称" allowClear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="商品编码" style="width: 100%;">
              <a-input v-model:value="searchForm.goods_no" placeholder="请输入商品编码" allowClear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="商品分类" style="width: 100%;">
              <a-select
                v-model:value="searchForm.category_id"
                placeholder="请选择分类"
                allowClear
                style="width: 100%;"
              >
                <a-select-option
                  v-for="category in categories"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16" style="width: 100%; margin-bottom: 16px;">
          <a-col :span="6">
            <a-form-item label="HS编码" style="width: 100%;">
              <a-input v-model:value="searchForm.hs_code" placeholder="请输入HS编码" allowClear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="条形码" style="width: 100%;">
              <a-input v-model:value="searchForm.bar_code" placeholder="请输入条形码" allowClear />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="商品状态" style="width: 100%;">
              <a-select v-model:value="searchForm.status" placeholder="请选择状态" allowClear>
                <a-select-option :value="1">上架</a-select-option>
                <a-select-option :value="0">下架</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item style="width: 100%;">
              <a-space>
                <a-button type="primary" @click="handleSearch">
                  <template #icon><search-outlined /></template>
                  查询
                </a-button>
                <a-button @click="handleReset">
                  <template #icon><reload-outlined /></template>
                  重置
                </a-button>
              </a-space>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>

      <!-- 操作按钮 -->
      <div class="table-operations">
        <a-button type="primary" @click="showAddModal">
          <template #icon><plus-outlined /></template>
          添加商品
        </a-button>
      </div>

      <!-- 商品列表表格 -->
      <a-table
        :columns="columns"
        :data-source="productList"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        :scroll="{ x: 1200 }"
        size="middle"
        :row-key="record => record.id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'goods_type'">
            <a-tag :color="record.goods_type === 1 ? 'blue' : 'green'">
                <template #icon v-if="record.goods_type === 1">
                    <rocket-outlined />
                </template>
                <template #icon v-else>
                    <bank-outlined />
                </template>
              {{ record.goods_type === 1 ? '直邮' : '保税仓' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'category'">
            {{ record.category }}
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'success' : 'error'">
              {{ record.status === 1 ? '已上架' : '已下架' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'created_at'">
            {{ formatDate(record.created_at) }}
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="handleEdit(record)" v-permission="'update'">
                <template #icon><EditOutlined /></template>
                编辑
              </a-button>
              <a-popconfirm
                title="确定要删除这个商品吗？"
                @confirm="handleDelete(record)"
              >
                <a-button v-permission="'delete'" type="link" danger>
                    <DeleteOutlined />
                    删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑商品模态框 -->
    <a-modal
      :title="modalTitle"
      v-model:open="modalVisible"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :maskClosable="false"
      width="720px"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-tabs>
          <!-- 基本信息 -->
          <a-tab-pane key="basic" tab="基本信息">
            <a-form-item label="商品类型" name="goods_type">
              <a-radio-group v-model:value="formState.goods_type">
                <a-radio :value="1">直邮</a-radio>
                <a-radio :value="2">保税仓</a-radio>
              </a-radio-group>
            </a-form-item>

            <template v-if="formState.goods_type === 2">
              <a-form-item label="序号" name="sequence_no" :rules="[{ required: true, message: '请输入序号' }]">
                <a-input v-model:value="formState.sequence_no" placeholder="请输入序号" />
              </a-form-item>
              <a-form-item label="账册备案料号" name="filing_no" :rules="[{ required: true, message: '请输入账册备案料号' }]">
                <a-input v-model:value="formState.filing_no" placeholder="请输入账册备案料号" />
              </a-form-item>
            </template>

            <a-form-item label="商品名称" name="goods_name">
              <a-input v-model:value="formState.goods_name" placeholder="请输入商品名称" />
            </a-form-item>

            <a-form-item label="商品编码" name="goods_no">
              <a-input v-model:value="formState.goods_no" placeholder="请输入商品编码" />
            </a-form-item>

            <a-form-item label="商品条形码" name="bar_code">
                <a-input-group compact style="display: flex;">
                    <a-input v-model:value="formState.bar_code" placeholder="请输入商品条形码，如无则填写'无'" />
                    <a-tooltip title="将自动填写无">
                        <a-button type="primary" @click="handleWnull">填写"无"</a-button>
                    </a-tooltip>
                </a-input-group>
            </a-form-item>

            <a-form-item label="HS编码" name="hs_code">
              <a-input v-model:value="formState.hs_code" placeholder="请输入HS编码" />
            </a-form-item>

            <a-form-item label="商品分类" name="category_id">
              <a-select
                :getPopupContainer="getPopupContainer"
                v-model:value="formState.category_id"
                placeholder="请选择商品分类"
                :options="categories.map(item => ({ value: item.id, label: item.name }))"
                style="width: 100%"
              />
            </a-form-item>
            
            <a-form-item label="商品价格" name="price">
              <a-input-number
                v-model:value="formState.price"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入商品价格"
              />
            </a-form-item>

            <a-form-item label="商品税率" name="goods_tax">
              <a-input-number
                v-model:value="formState.goods_tax"
                :min="0"
                :max="100"
                :precision="2"
                :step="0.01"
                style="width: 100%"
                placeholder="请输入商品税率"
                addon-after="%"
              />
            </a-form-item>

            <a-form-item label="净重(KG)" name="net_weight">
              <a-input-number
                v-model:value="formState.net_weight"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入净重"
              />
            </a-form-item>

            <a-form-item label="毛重(KG)" name="gross_weight">
              <a-input-number
                v-model:value="formState.gross_weight"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入毛重"
              />
            </a-form-item>
          </a-tab-pane>

          <!-- 规格信息 -->
          <a-tab-pane key="spec" tab="规格信息">
            <a-form-item label="商品规格型号" name="goods_model">
              <a-textarea
                v-model:value="formState.goods_model"
                :rows="4"
                placeholder="请输入商品规格型号，包括：品名、牌名、规格、型号、成份、含量、等级等"
              />
            </a-form-item>

            <a-form-item label="商品原产国" name="origin_country_id">
              <a-select
                :getPopupContainer="getPopupContainer"
                v-model:value="formState.origin_country_id"
                placeholder="请选择商品原产国"
                :options="countryOptions"
                style="width: 100%"
                show-search
                :filter-option="filterOption"
                :loading="countryLoading"
              />
            </a-form-item>

            <a-form-item label="计量单位" name="unit_id">
              <a-select
                :getPopupContainer="getPopupContainer"
                v-model:value="formState.unit_id"
                placeholder="请选择计量单位"
                :options="unitOptions"
                style="width: 100%"
                show-search
                :filter-option="filterOption"
                :loading="unitLoading"
              />
            </a-form-item>

            <a-form-item label="法定计量单位" name="unit1_id">
              <a-select
                :getPopupContainer="getPopupContainer"
                v-model:value="formState.unit1_id"
                placeholder="请选择法定计量单位"
                :options="unitOptions"
                style="width: 100%"
                show-search
                :filter-option="filterOption"
                :loading="unitLoading"
              />
            </a-form-item>

            <a-form-item label="法定数量" name="qty1">
              <a-input-number
                v-model:value="formState.qty1"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入法定数量"
              />
            </a-form-item>

            <a-form-item label="第二计量单位" name="unit2_id">
              <a-select
                :getPopupContainer="getPopupContainer"
                v-model:value="formState.unit2_id"
                placeholder="请选择第二计量单位"
                :options="unitOptions"
                style="width: 100%"
                show-search
                :filter-option="filterOption"
                :loading="unitLoading"
                allowClear
              />
            </a-form-item>

            <a-form-item label="第二数量" name="qty2">
              <a-input-number
                v-model:value="formState.qty2"
                :min="0"
                :precision="2"
                style="width: 100%"
                placeholder="请输入第二数量"
              />
            </a-form-item>
            
            <a-form-item label="商品状态" name="status">
              <a-radio-group v-model:value="formState.status">
                <a-radio :value="1">上架</a-radio>
                <a-radio :value="0">下架</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-tab-pane>

        </a-tabs>
      </a-form>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, h } from 'vue';
import { 
  PlusOutlined, 
  SearchOutlined, 
  ReloadOutlined, 
  LeftOutlined,
  RocketOutlined,
  BankOutlined,
  DeleteOutlined,
  EditOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { getProducts, createProduct, updateProduct, deleteProduct, getProductCategories } from '@/api/products';
import { getDictItems } from '@/api/system/dict';
import { formatDate } from '@/utils/utils';

export default defineComponent({
  name: 'RegisteredProducts',
  components: {
    PlusOutlined,
    SearchOutlined,
    ReloadOutlined,
    LeftOutlined,
    RocketOutlined,
    BankOutlined,
    DeleteOutlined,
    EditOutlined
  },
  setup() {
    const router = useRouter();
    const loading = ref(false);
    const categories = ref([]);
    const productList = ref([]);
    const modalVisible = ref(false);
    const modalTitle = ref('添加商品');
    const formRef = ref(null);

    // 表格列定义
    const columns = [
      {
        title: '商品类型',
        dataIndex: 'goods_type',
        key: 'goods_type',
        width: 100,
      },
      {
        title: '商品名称',
        dataIndex: 'goods_name',
        key: 'goods_name',
        width: 200,
      },
      {
        title: '商品编码',
        dataIndex: 'goods_no',
        key: 'goods_no',
        width: 120,
      },
      {
        title: '商品条形码',
        dataIndex: 'bar_code',
        key: 'bar_code',
        width: 120,
      },
      {
        title: 'HS编码',
        dataIndex: 'hs_code',
        key: 'hs_code',
        width: 120,
      },
      {
        title: '商品分类',
        dataIndex: 'category',
        key: 'category',
        width: 120,
      },
      {
        title: '商品税率',
        dataIndex: 'goods_tax',
        key: 'goods_tax',
        width: 100,
        customRender: ({ text }) => `${text}%`
      },
      {
        title: '价格',
        dataIndex: 'price',
        key: 'price',
        width: 120,
      },
      {
        title: '净重(KG)',
        dataIndex: 'net_weight',
        key: 'net_weight',
        width: 100,
      },
      {
        title: '毛重(KG)',
        dataIndex: 'gross_weight',
        key: 'gross_weight',
        width: 100,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 180,
      },
      {
        title: '操作',
        key: 'action',
        fixed: 'right',
        width: 200,
      },
    ];

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showTotal: total => `共 ${total} 条`,
      showSizeChanger: true,
      showQuickJumper: true,
    });

    // 搜索表单
    const searchForm = reactive({
      goods_type: undefined,
      goods_name: '',
      goods_no: '',
      category_id: undefined,
      hs_code: '',
      bar_code: '',
      status: undefined,
    });

    // 表单状态
    const formState = reactive({
      id: null,
      goods_type: 1,
      sequence_no: '',
      filing_no: '',
      goods_name: '',
      goods_no: '',
      bar_code: '无',
      goods_tax: 0,
      hs_code: '',
      goods_model: '',
      origin_country_id: undefined,
      unit_id: undefined,
      unit1_id: undefined,
      unit2_id: undefined,
      qty1: 0,
      qty2: 0,
      net_weight: 0,
      gross_weight: 0,
      category_id: undefined,
      price: 0,
      status: 1,
      description: ''
    });

    // 表单验证规则
    const rules = {
      goods_type: [
        { required: true, message: '请选择商品类型' }
      ],
      goods_name: [
        { required: true, message: '请输入商品名称' },
        { min: 2, max: 100, message: '长度在 2 到 100 个字符' }
      ],
      goods_no: [
        { required: true, message: '请输入商品编码' }
      ],
      bar_code: [
        { required: true, message: '请输入商品条形码' }
      ],
      goods_tax: [
        { required: true, message: '请输入商品税率' }
      ],
      hs_code: [
        { required: true, message: '请输入HS编码' }
      ],
      goods_model: [
        { required: true, message: '请输入商品规格型号' }
      ],
      origin_country_id: [
        { required: true, message: '请选择商品原产国' }
      ],
      unit_id: [
        { required: true, message: '请选择计量单位' }
      ],
      unit1_id: [
        { required: true, message: '请选择法定计量单位' }
      ],
      qty1: [
        { required: true, message: '请输入法定数量' }
      ],
      net_weight: [
        { required: true, message: '请输入净重' }
      ],
      gross_weight: [
        { required: true, message: '请输入毛重' }
      ],
      category_id: [
        { required: true, message: '请选择商品分类' }
      ],
      price: [
        { required: true, message: '请输入商品价格' }
      ],
      status: [
        { required: true, message: '请选择商品状态' }
      ]
    };

    const getPopupContainer = (triggerNode) => {
        return triggerNode.parentElement;
    };

    // 获取国家和单位选项
    const countryOptions = ref([]);
    const unitOptions = ref([]);
    const channelOptions = ref([]);
    const countryLoading = ref(false);
    const unitLoading = ref(false);

    // 下拉框搜索过滤
    const filterOption = (input, option) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    // 自动赋值条形码无
    const handleWnull = () => {
      formState.bar_code = '无';
    };

    // 获取国家选项
    const fetchCountryOptions = async () => {
      try {
        countryLoading.value = true;
        const res = await getDictItems(null, { 
          code: 'country_area',
          pageSize: 10000 // 获取全部数据
        });
        if (res.code === 200 && res.data.list) {
          countryOptions.value = res.data.list.map(item => ({
            value: item.id,
            label: item.label
          }));
        }
      } catch (error) {
        console.error('获取国家/地区选项失败:', error);
        message.error('获取国家/地区选项失败');
      } finally {
        countryLoading.value = false;
      }
    };

    // 获取单位选项
    const fetchUnitOptions = async () => {
      try {
        unitLoading.value = true;
        const res = await getDictItems(null, { 
          code: 'unit',
          pageSize: 10000 // 获取全部数据
        });
        if (res.code === 200 && res.data.list) {
          unitOptions.value = res.data.list.map(item => ({
            value: item.id,
            label: item.label
          }));
        }
      } catch (error) {
        console.error('获取单位选项失败:', error);
        message.error('获取单位选项失败');
      } finally {
        unitLoading.value = false;
      }
    };

    // 获取商品分类
    const fetchCategories = async () => {
      try {
        const res = await getProductCategories();
        if (res.code === 200) {
          categories.value = res.data || [];
        }
      } catch (error) {
        console.error('获取商品分类失败:', error);
        message.error('获取商品分类失败');
      }
    };

    // 商品分类改变
    const handleCategoryChange = (value) => {
        formState.category_id = value;
    };

    // 获取商品列表
    const fetchProducts = async () => {
      loading.value = true;
      try {
        const res = await getProducts({
          page: pagination.current,
          pageSize: pagination.pageSize,
          ...searchForm
        });
        if (res.code === 200) {
          productList.value = res.data.list || [];
          pagination.total = res.data.total || 0;
        }
      } catch (error) {
        console.error('获取商品列表失败:', error);
        message.error('获取商品列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 搜索
    const handleSearch = () => {
      pagination.current = 1;
      fetchProducts();
    };

    // 重置搜索
    const handleReset = () => {
      searchForm.goods_type = undefined;
      searchForm.goods_name = '';
      searchForm.goods_no = '';
      searchForm.category_id = undefined;
      searchForm.hs_code = '';
      searchForm.bar_code = '';
      searchForm.status = undefined;
      pagination.current = 1;
      fetchProducts();
    };

    // 表格变化
    const handleTableChange = (pag) => {
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;
      fetchProducts();
    };

    // 显示添加模态框
    const showAddModal = () => {
      modalTitle.value = '添加商品';
      formState.id = null;
      formState.goods_type = 1;
      formState.sequence_no = '';
      formState.filing_no = '';
      formState.goods_name = '';
      formState.goods_no = '';
      formState.bar_code = '';
      formState.goods_tax = 0;
      formState.hs_code = '';
      formState.goods_model = '';
      formState.origin_country_id = undefined;
      formState.unit_id = undefined;
      formState.unit1_id = undefined;
      formState.unit2_id = undefined;
      formState.qty1 = 0;
      formState.qty2 = 0;
      formState.net_weight = 0;
      formState.gross_weight = 0;
      formState.category_id = undefined;
      formState.price = 0;
      formState.status = 1;
      formState.description = '';
      modalVisible.value = true;
    };

    // 显示编辑模态框
    const handleEdit = (record) => {
      modalTitle.value = '编辑商品';
      Object.assign(formState, {
        id: record.id,
        goods_type: record.goods_type,
        sequence_no: record.sequence_no,
        filing_no: record.filing_no,
        goods_name: record.goods_name,
        goods_no: record.goods_no,
        bar_code: record.bar_code,
        goods_tax: record.goods_tax,
        hs_code: record.hs_code,
        goods_model: record.goods_model,
        origin_country_id: record.origin_country_id,
        unit_id: record.unit_id,
        unit1_id: record.unit1_id,
        unit2_id: record.unit2_id,
        qty1: record.qty1,
        qty2: record.qty2,
        net_weight: record.net_weight,
        gross_weight: record.gross_weight,
        category_id: record.category_id,
        price: record.price,
        status: record.status,
        description: record.description
      });
      modalVisible.value = true;
    };

    // 删除商品
    const handleDelete = async (record) => {
      try {
        const res = await deleteProduct(record.id);
        if (res.code === 200) {
          message.success('删除成功');
          if (productList.value.length === 1 && pagination.current > 1) {
            pagination.current -= 1;
          }
          fetchProducts();
        } else {
          message.error(res.message || '删除失败');
        }
      } catch (error) {
        console.error('删除商品失败:', error);
        message.error('删除商品失败');
      }
    };

    // 提交表单
    const handleModalOk = () => {
      formRef.value.validate().then(async () => {
        try {
          let res;
          if (formState.id) {
            res = await updateProduct(formState.id, formState);
          } else {
            res = await createProduct(formState);
          }
          
          if (res.code === 200) {
            message.success(`${modalTitle.value}成功`);
            modalVisible.value = false;
            fetchProducts();
          } else {
            message.error(res.message || `${modalTitle.value}失败`);
            // 提交失败时保持模态框打开
            modalVisible.value = true;
          }
        } catch (error) {
          console.error(`${modalTitle.value}失败:`, error);
          message.error(`${modalTitle.value}失败`);
          // 提交失败时保持模态框打开
          modalVisible.value = true;
        }
      });
    };

    // 取消表单
    const handleModalCancel = () => {
      formRef.value?.resetFields();
      modalVisible.value = false;
    };

    // 返回上一页
    const goBack = () => {
      router.push('/products');
    };

    onMounted(() => {
      fetchCategories();
      fetchCountryOptions();
      fetchUnitOptions();
      fetchProducts();
    });

    return {
      loading,
      categories,
      productList,
      columns,
      pagination,
      searchForm,
      modalVisible,
      modalTitle,
      formRef,
      formState,
      rules,
      handleSearch,
      handleReset,
      handleTableChange,
      showAddModal,
      handleEdit,
      handleDelete,
      handleModalOk,
      handleModalCancel,
      getPopupContainer,
      handleCategoryChange,
      goBack,
      countryOptions,
      unitOptions,
      channelOptions,
      filterOption,
      countryLoading,
      unitLoading,
      handleWnull,
      formatDate
    };
  }
});
</script>

<style lang="less" scoped>
.registered-products {
  .page-header {
    margin-bottom: 16px;
    display: flex;
    align-items: center;

    h2 {
      margin: 0;
      font-size: 20px;
      font-weight: 500;
      color: #272343;
    }
  }

  .table-operations {
    margin-bottom: 16px;
  }

  .table-search-form {
    :deep(.ant-form-item) {
      margin-bottom: 16px;
      
      .ant-form-item-label {
        width: 80px;
        text-align: right;
      }
    }
  }

  .text-danger {
    color: #ff4d4f;
  }
}
</style> 