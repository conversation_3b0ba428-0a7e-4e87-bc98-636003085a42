<template>
  <system-page title="API在线测试">
    <div class="api-test-container">
      <a-row :gutter="16">
        <!-- 左侧接口列表和文档 -->
        <a-col :span="8">
          <a-card :bordered="false" title="接口列表">
            <a-menu
              v-model:selectedKeys="selectedApi"
              mode="inline"
              @select="handleApiSelect"
            >
              <a-sub-menu key="orders" title="订单接口">
                <a-menu-item key="createOrder">
                  <template #icon><SendOutlined /></template>
                  创建订单
                </a-menu-item>
                <a-menu-item key="updateOrder">
                  <template #icon><FormOutlined /></template>
                  更新订单
                </a-menu-item>
              </a-sub-menu>
            </a-menu>

            <!-- 接口文档抽屉 -->
            <a-drawer
              v-model:open="docVisible"
              :title="currentApiDoc?.title || '接口文档'"
              width="720"
              placement="right"
            >
              <template v-if="currentApiDoc">
                <div class="api-doc">
                  <a-descriptions :column="1" bordered>
                    <a-descriptions-item label="接口名称">{{ currentApiDoc.title }}</a-descriptions-item>
                    <a-descriptions-item label="请求方法">{{ currentApiDoc.method }}</a-descriptions-item>
                    <a-descriptions-item label="请求路径">{{ currentApiDoc.path }}</a-descriptions-item>
                    <a-descriptions-item label="接口说明">{{ currentApiDoc.description }}</a-descriptions-item>
                  </a-descriptions>

                  <a-divider>认证方式</a-divider>
                  <a-typography-paragraph>
                    <pre class="doc-auth">{{ currentApiDoc.auth }}</pre>
                  </a-typography-paragraph>

                  <a-divider>请求参数</a-divider>
                  <a-typography-paragraph>
                    <pre class="doc-params">{{ currentApiDoc.params }}</pre>
                  </a-typography-paragraph>

                  <a-divider>返回示例</a-divider>
                  <a-typography-paragraph>
                    <pre class="doc-response">{{ currentApiDoc.response }}</pre>
                  </a-typography-paragraph>

                  <a-divider>错误码</a-divider>
                  <a-typography-paragraph>
                    <pre class="doc-error-codes">{{ currentApiDoc.errorCodes }}</pre>
                  </a-typography-paragraph>
                </div>
              </template>
            </a-drawer>
          </a-card>
        </a-col>

        <!-- 右侧测试区域 -->
        <a-col :span="16">
          <a-card :bordered="false">
            <!-- 接口信息 -->
            <div class="api-info">
              <div class="api-title">
                <h3>{{ currentApiDoc?.title || '请选择接口' }}</h3>
                <a-space>
                  <a-button type="link" @click="showDoc" v-if="currentApiDoc">
                    <template #icon><file-text-outlined /></template>
                    查看文档
                  </a-button>
                </a-space>
              </div>
              <a-alert
                v-if="currentApiDoc?.description"
                :message="currentApiDoc.description"
                type="info"
                show-icon
                style="margin-bottom: 16px"
              />
            </div>

            <!-- 认证信息 -->
            <a-form layout="vertical">
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item label="API Key">
                    <a-input-password v-model:value="authInfo.apikey" placeholder="请输入API Key" allowClear />
                  </a-form-item>
                </a-col>
              </a-row>
            </a-form>

            <!-- 请求信息 -->
            <a-divider>请求信息</a-divider>
            <a-form layout="vertical">
              <a-row :gutter="16">
                <a-col :span="8">
                  <a-form-item label="请求方法">
                    <a-input v-model:value="requestInfo.method" :disabled="true" />
                  </a-form-item>
                </a-col>
                <a-col :span="16">
                  <a-form-item label="请求路径">
                    <a-input-group compact>
                      <a-input
                        v-model:value="requestInfo.basePath"
                        :disabled="true"
                        style="width: 50%"
                      />
                      <a-input
                        v-if="currentApiDoc?.pathParams"
                        v-model:value="requestInfo.pathParams"
                        :placeholder="currentApiDoc.pathParams.join(', ')"
                        style="width: 50%"
                        @change="handlePathParamsChange"
                      />
                    </a-input-group>
                    <div class="path-tips" v-if="currentApiDoc?.pathParams">
                      <small>请按顺序输入：{{ currentApiDoc.pathParams.join(', ') }}</small>
                    </div>
                  </a-form-item>
                </a-col>
              </a-row>

              <a-form-item label="请求参数">
                    <a-textarea
                      v-model:value="requestInfo.params"
                      :rows="16"
                      placeholder="请输入JSON格式的请求参数"
                      @change="formatJson"
                    />
              </a-form-item>

              <a-form-item>
                <a-space>
                  <a-button type="primary" @click="handleTest" :loading="loading">
                    <template #icon><code-outlined /></template>
                    测试请求
                  </a-button>
                  <a-button @click="handleClear">
                    <template #icon><clear-outlined /></template>
                    清空
                  </a-button>
                  <a-button @click="handleFormat">
                    <template #icon><align-left-outlined /></template>
                    格式化
                  </a-button>
                  <a-button type="link" @click="loadSampleData" v-if="currentApiDoc?.sample">
                    加载示例数据
                  </a-button>
                </a-space>
              </a-form-item>
            </a-form>

            <!-- 响应信息 -->
            <a-divider>响应信息</a-divider>
            <a-descriptions :column="1" bordered>
              <a-descriptions-item label="状态码">
                <a-tag :color="responseInfo.code === 200 ? 'success' : 'error'">
                  {{ responseInfo.code }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="响应时间">
                {{ responseInfo.duration }}ms
              </a-descriptions-item>
              <a-descriptions-item label="响应内容">
                <a-textarea
                  v-model="responseInfo.content"
                  :rows="12"
                  readonly
                  :value="formatResponse(responseInfo.content)"
                />
              </a-descriptions-item>
            </a-descriptions>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </system-page>
</template>

<script>
import { defineComponent, ref, reactive } from 'vue';
import {
  CodeOutlined,
  ClearOutlined,
  AlignLeftOutlined,
  FileTextOutlined,
  SendOutlined,
  FormOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import SystemPage from '../components/SystemPage.vue';

// API 文档配置
const apiDocs = {
  createOrder: {
    title: '创建订单',
    method: 'POST',
    path: '/open-api/order/create',
    description: '创建新的订单接口',
    auth: `认证方式：API Key
请求头：
x-api-key: API密钥
x-timestamp: 当前时间戳（秒）`,
    params: `{
  "template_id": 1,                    // 申报模板ID（必填）
  "order_no": "2024021012345678",      // 订单号（必填）
  "order_time": "2024-02-10 12:00:00", // 下单时间（必填）
  "payment_method": "alipay",          // 支付方式(alipay/wechat)（必填）
  "transaction_id": "2024021012345678",// 支付流水号（必填）
  "freight_amount": 0,                 // 运杂费（选填，默认0）
  "discount_amount": 0,                // 优惠金额（选填，默认0）
  "tax_amount": 0,                     // 税费（选填，默认0）
  "pay_amount": 0,                     // 实际付款金额（选填，默认1）
  "items": [                           // 商品列表（必填）
    {
      "channel_product_name": "测试商品",  // 渠道商品名称（必填）
      "channel_product_no": "TEST001",   // 渠道商品编码（必填，用于关联平台备案商品）
      "unit_price": 6999.00,           // 商品单价（必填）
      "quantity": 1                    // 数量（必填）
    }
  ],
  "address": {                         // 地址信息（必填）
    "receiver_name": "张三",           // 收件人姓名（必填）
    "receiver_phone": "13800138000",   // 收件人手机号（必填）
    "receiver_address": "广东省深圳市南山区xx路xx号", // 收货地址（必填）
    "buyer_name": "张三",             // 订购人姓名（必填）
    "buyer_phone": "13800138000",     // 订购人手机号（必填）
    "buyer_id_number": "440301199001011234" // 订购人身份证号（必填）
  },
  "declaration": {                     // 跨境申报信息（选填, 179对接必填）
    "payment_request": "原始支付请求信息",  // 支付原始请求, 微信为xml,支付宝为url
    "payment_response": "原始支付返回信息"  // 支付原始返回, 微信为xml,其余传json数据
  }
}`,
    response: `{
  "code": 200,
  "message": "创建成功",
  "data": {
    "order_no": "2024021012345678"
  }
}`,
    errorCodes: `错误码说明：
400 10001 - 请选择申报模板
404 10002 - 申报模板不存在
400 10003 - 缺少订单号
400 10004 - 缺少下单时间
400 10005 - 请提供正确的支付方式
400 10006 - 请提供支付流水号
400 10007 - 请提供订单商品信息
400 10008 - 请提供订单地址信息
400 10009 - 订单号已存在
400 10010 - 请提供渠道商品名称
400 10011 - 请提供渠道商品编码
400 10012 - 请提供正确的商品单价
400 10013 - 请提供正确的商品数量
400 10014 - 请提供收件人姓名
400 10015 - 请提供收件人手机号
400 10016 - 请提供收货地址
400 10017 - 请提供订购人姓名
400 10018 - 请提供订购人手机号
400 10019 - 请提供订购人身份证号
500 50001 - 系统错误`,
    sample: {
      template_id: 1,
      order_no: "2024021012345678",
      order_time: "2024-02-10 12:00:00",
      payment_method: "alipay",
      transaction_id: "2024021012345678",
      freight_amount: 0,
      discount_amount: 0,
      tax_amount: 0,
      pay_amount: 10,
      items: [
        {
          channel_product_name: "测试商品",
          channel_product_no: "TEST001",
          unit_price: 10.00,
          quantity: 1
        }
      ],
      address: {
        receiver_name: "张三",
        receiver_phone: "13800138000",
        receiver_address: "广东省深圳市南山区xx路xx号",
        buyer_name: "张三",
        buyer_phone: "13800138000",
        buyer_id_number: "440301199001011234"
      },
      "declaration": {                     // 跨境申报信息（选填）
        "payment_request": "原始支付请求信息",  // 支付原始请求
        "payment_response": "原始支付返回信息"  // 支付原始返回
      }
    }
  },
  updateOrder: {
    title: '更新订单信息',
    method: 'POST',
    path: '/open-api/order/update',
    description: '更新订单的收件人信息、订购人信息、收货地址和相关金额',
    auth: `认证方式：API Key
请求头：
x-api-key: API密钥
x-timestamp: 当前时间戳（秒）`,
    params: `{
  "order_no": "2024021012345678",     // 订单号（必填）
  "freight_amount": 0,               // 运杂费（选填）
  "discount_amount": 0,              // 优惠金额（选填）
  "tax_amount": 0,                   // 税费（选填）
  "address": {                       // 地址信息（选填）
    "receiver_name": "张三",         // 收件人姓名
    "receiver_phone": "13800138000", // 收件人手机号
    "receiver_address": "广东省深圳市南山区xx路xx号", // 收货地址
    "buyer_name": "张三",           // 订购人姓名
    "buyer_phone": "13800138000",   // 订购人手机号
    "buyer_id_number": "440301199001011234" // 订购人身份证号
  }
}`,
    response: `{
  "code": 200,
  "message": "更新成功"
}`,
    errorCodes: `错误码说明：
404 20001 - 订单不存在
400 20005 - 无效的运杂费金额
400 20006 - 无效的优惠金额
400 20007 - 无效的税费金额
400 20008 - 收件人姓名不能为空
400 20009 - 收件人手机号不能为空
400 20010 - 收货地址不能为空
400 20011 - 订购人姓名不能为空
400 20012 - 订购人手机号不能为空
400 20013 - 订购人身份证号不能为空
500 50001 - 系统错误`,
    sample: {
      order_no: "2024021012345678",
      freight_amount: 10,
      discount_amount: 5,
      tax_amount: 0,
      address: {
        receiver_name: "张三",
        receiver_phone: "13800138000",
        receiver_address: "广东省深圳市南山区xx路xx号",
        buyer_name: "张三",
        buyer_phone: "13800138000",
        buyer_id_number: "440301199001011234"
      }
    }
  }
};

export default defineComponent({
  name: 'ApiTest',
  components: {
    SystemPage,
    CodeOutlined,
    ClearOutlined,
    AlignLeftOutlined,
    FileTextOutlined,
    SendOutlined,
    FormOutlined
  },
  setup() {
    const loading = ref(false);
    const selectedApi = ref([]);
    const currentApiDoc = ref(null);
    const docVisible = ref(false);

    // 认证信息
    const authInfo = reactive({
      apikey: ''
    });

    // 请求信息
    const requestInfo = reactive({
      method: '',
      basePath: '',
      pathParams: '',
      params: ''
    });

    // 响应信息
    const responseInfo = reactive({
      code: '',
      duration: 0,
      content: ''
    });

    // 显示文档
    const showDoc = () => {
      docVisible.value = true;
    };

    // 选择接口
    const handleApiSelect = ({ key }) => {
      const apiDoc = apiDocs[key];
      if (apiDoc) {
        currentApiDoc.value = apiDoc;
        requestInfo.method = apiDoc.method;
        requestInfo.basePath = apiDoc.path;
        requestInfo.pathParams = '';
        requestInfo.path = apiDoc.path;
        requestInfo.params = '';
      }
    };

    // 格式化JSON
    const formatJson = () => {
      try {
        if (requestInfo.params) {
          const obj = JSON.parse(requestInfo.params);
          requestInfo.params = JSON.stringify(obj, null, 2);
        }
      } catch (error) {
        // 如果不是有效的JSON，不进行格式化
      }
    };

    // 格式化响应内容
    const formatResponse = (content) => {
      try {
        if (content) {
          const obj = JSON.parse(content);
          return JSON.stringify(obj, null, 2);
        }
        return content;
      } catch (error) {
        return content;
      }
    };

    // 处理测试请求
    const handleTest = async () => {
      if (!authInfo.apikey) {
        message.error('请输入API Key');
        return;
      }

      try {
        loading.value = true;
        const startTime = Date.now();

        // 构建请求参数
        let params = {};
        try {
          params = requestInfo.params ? JSON.parse(requestInfo.params) : {};
        } catch (error) {
          message.error('请求参数JSON格式错误');
          return;
        }

        // 构建完整的请求URL
        const baseUrl = import.meta.env.VITE_API_URL || '';
        let url = `${baseUrl}${requestInfo.basePath}`;
        if (requestInfo.pathParams) {
          const pathParamValues = requestInfo.pathParams.split(',');
          currentApiDoc.value.pathParams.forEach((param, index) => {
            url = url.replace(`{${param}}`, pathParamValues[index]);
          });
        }

        // 发送请求
        const response = await fetch(url, {
          method: requestInfo.method,
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': authInfo.apikey,
            'x-timestamp': Math.floor(Date.now() / 1000).toString()
          },
          body: requestInfo.method !== 'GET' ? JSON.stringify(params) : undefined
        });

        const data = await response.json();
        const endTime = Date.now();

        // 更新响应信息
        responseInfo.code = response.status;
        responseInfo.duration = endTime - startTime;
        responseInfo.content = JSON.stringify(data, null, 2);

      } catch (error) {
        console.error('请求失败:', error);
        message.error('请求失败: ' + error.message);
      } finally {
        loading.value = false;
      }
    };

    // 清空表单
    const handleClear = () => {
      requestInfo.params = '';
      responseInfo.code = '';
      responseInfo.duration = '';
      responseInfo.content = '';
    };

    // 格式化
    const handleFormat = () => {
      formatJson();
    };

    // 加载示例数据
    const loadSampleData = () => {
      if (currentApiDoc.value?.sample) {
        requestInfo.params = JSON.stringify(currentApiDoc.value.sample, null, 2);
      }
    };

    // 处理路径参数变化
    const handlePathParamsChange = () => {
      if (!currentApiDoc.value?.pathParams) return;
      
      let path = requestInfo.basePath;
      const params = requestInfo.pathParams.split(',').map(p => p.trim());
      
      currentApiDoc.value.pathParams.forEach((param, index) => {
        path = path.replace(`{${param}}`, params[index] || `{${param}}`);
      });
      
      requestInfo.path = path;
    };

    return {
      loading,
      selectedApi,
      currentApiDoc,
      docVisible,
      authInfo,
      requestInfo,
      responseInfo,
      formatJson,
      formatResponse,
      handleTest,
      handleClear,
      handleFormat,
      loadSampleData,
      handleApiSelect,
      handlePathParamsChange,
      showDoc
    };
  }
});
</script>

<style lang="less" scoped>
.api-test-container {
  .api-info {
    margin-bottom: 24px;

    .api-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }

  .path-tips {
    margin-top: 4px;
    color: #666;
  }

  .param-desc {
    height: 100%;
    padding: 16px;
    background: #fafafa;
    border-radius: 4px;

    pre {
      margin: 0;
      white-space: pre-wrap;
      word-wrap: break-word;
      font-size: 13px;
      line-height: 1.5;
    }
  }

  :deep(.ant-menu-inline) {
    border-right: none;
  }

  .api-doc {
    .doc-params,
    .doc-response,
    .doc-error-codes,
    .doc-auth {
      padding: 16px;
      background: #fafafa;
      border-radius: 4px;
      font-size: 13px;
      line-height: 1.5;
      white-space: pre-wrap;
      word-wrap: break-word;
    }

    .doc-error-codes {
      color: #cf1322;
    }

    .doc-auth {
      color: #08979c;
    }
  }
}
</style> 