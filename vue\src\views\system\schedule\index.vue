<template>
  <system-page title="定时任务">
    <div class="schedule-container">
      <!-- 操作按钮 -->
      <div class="table-operations">
        <a-button type="primary" @click="showAddModal">
          <template #icon><PlusOutlined /></template>
          新增任务
        </a-button>
      </div>

      <!-- 任务列表 -->
      <a-table
        :columns="columns"
        :data-source="scheduleList"
        :loading="loading"
        :pagination="{ 
          total: total,
          current: current,
          pageSize: pageSize,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: total => `共 ${total} 条`
        }"
        @change="handleTableChange"
        :scroll="{ x: 1200 }"
      >
        <!-- 状态列 -->
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status ? 'success' : 'error'">
              {{ record.status ? '启用' : '禁用' }}
            </a-tag>
          </template>
          
          <!-- 任务类型列 -->
          <template v-if="column.key === 'type'">
            <a-tag :color="getTypeColor(record.type)">
              {{ getTypeText(record.type) }}
            </a-tag>
          </template>

          <!-- 邮件通知状态列 -->
          <template v-if="column.key === 'email_notification'">
            <a-tag :color="record.email_notification ? 'blue' : 'default'">
              {{ record.email_notification ? '已启用' : '未启用' }}
            </a-tag>
          </template>

          <!-- 操作列 -->
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" @click="showEditModal(record)">
                <template #icon><EditOutlined /></template>
                编辑
              </a-button>
              <a-button type="link" @click="showLogModal(record)">
                <template #icon><HistoryOutlined /></template>
                日志
              </a-button>
              <a-button type="link" @click="handleRun(record)">
                <template #icon><ReloadOutlined /></template>
                手动执行
              </a-button>
              <a-button
                type="link"
                @click="handleTestEmailFromList(record)"
                v-if="record.email_notification"
                :loading="testEmailLoading"
              >
                <template #icon><MailOutlined /></template>
                测试邮件
              </a-button>
              <a-popconfirm
                title="确定要删除此任务吗？"
                @confirm="handleDelete(record.id)"
                okText="确定"
                cancelText="取消"
              >
                <a-button type="link" danger>
                  <template #icon><DeleteOutlined /></template>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>

      <!-- 任务表单模态框 -->
      <a-modal
        :title="modalTitle"
        v-model:open="modalVisible"
        @ok="handleModalOk"
        @cancel="handleModalCancel"
        :confirmLoading="modalLoading"
        width="600px"
        :maskClosable="false"
      >
        <a-form
          ref="formRef"
          :model="formState"
          :rules="rules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <a-form-item label="任务名称" name="name">
            <a-input v-model:value="formState.name" placeholder="请输入任务名称" />
          </a-form-item>
          <a-form-item label="任务类型" name="type">
            <a-select
              :getPopupContainer="getPopupContainer"
              v-model:value="formState.type"
              :options="typeOptions"
              placeholder="请选择任务类型"
              @change="handleTypeChange"
              allowClear
            />
          </a-form-item>
          <a-form-item 
            label="任务目标" 
            name="target"
            v-if="formState.type === 'url' || formState.type === 'class'"
          >
            <template v-if="formState.type === 'url'">
              <a-input v-model:value="formState.target" placeholder="请输入URL地址" />
            </template>
            <template v-else-if="formState.type === 'class'">
              <a-select
                :getPopupContainer="getPopupContainer"
                v-model:value="formState.target"
                :options="classMethods"
                placeholder="请选择类方法"
                :loading="methodsLoading"
                allowClear
              />
            </template>
          </a-form-item>
          <a-form-item label="执行周期" name="period_type">
            <a-select
              :getPopupContainer="getPopupContainer"
              v-model:value="formState.period_type"
              :options="periodTypeOptions"
              placeholder="请选择执行周期"
              @change="handlePeriodTypeChange"
              allowClear
            />
          </a-form-item>

          <!-- 根据不同的执行周期类型显示不同的表单项 -->
          <template v-if="formState.period_type">
            <!-- N天 -->
            <a-form-item v-if="formState.period_type === 'n_days'" label="间隔天数" name="days">
              <a-input-number v-model:value="formState.days" :min="1" style="width: 100%" placeholder="请输入间隔天数" />
            </a-form-item>

            <!-- N小时 -->
            <a-form-item v-if="formState.period_type === 'n_hours'" label="间隔小时" name="hours">
              <a-input-number v-model:value="formState.hours" :min="1" style="width: 100%" placeholder="请输入间隔小时" />
            </a-form-item>

            <!-- N分钟 -->
            <a-form-item v-if="formState.period_type === 'n_minutes'" label="间隔分钟" name="minutes">
              <a-input-number v-model:value="formState.minutes" :min="1" style="width: 100%" placeholder="请输入间隔分钟" />
            </a-form-item>

            <!-- N秒 -->
            <a-form-item v-if="formState.period_type === 'n_seconds'" label="间隔秒数" name="seconds">
              <a-input-number v-model:value="formState.seconds" :min="1" style="width: 100%" placeholder="请输入间隔秒数" />
            </a-form-item>

            <!-- 每周 -->
            <template v-if="formState.period_type === 'weekly'">
              <a-form-item label="星期" name="week_day">
                <a-select
                  :getPopupContainer="getPopupContainer"
                  v-model:value="formState.week_day"
                  :options="weekDayOptions"
                  placeholder="请选择星期"
                  allowClear
                />
              </a-form-item>
            </template>

            <!-- 每月 -->
            <template v-if="formState.period_type === 'monthly'">
              <a-form-item label="日期" name="month_day">
                <a-input-number v-model:value="formState.month_day" :min="1" :max="31" style="width: 100%" placeholder="请输入日期" />
              </a-form-item>
            </template>

            <!-- 需要时分的类型：每天、N天、每周、每月 -->
            <template v-if="['daily', 'n_days', 'weekly', 'monthly'].includes(formState.period_type)">
              <a-form-item label="时间" required>
                <a-row :gutter="8">
                  <a-col :span="12">
                    <a-form-item name="hour">
                      <a-input-number v-model:value="formState.hour" :min="0" :max="23" style="width: 100%" placeholder="时" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="12">
                    <a-form-item name="minute">
                      <a-input-number v-model:value="formState.minute" :min="0" :max="59" style="width: 100%" placeholder="分" />
                    </a-form-item>
                  </a-col>
                </a-row>
              </a-form-item>
            </template>

            <!-- 只需要分钟的类型：每小时 -->
            <template v-if="formState.period_type === 'hourly'">
              <a-form-item label="分钟" name="minute">
                <a-input-number v-model:value="formState.minute" :min="0" :max="59" style="width: 100%" placeholder="请输入分钟" />
              </a-form-item>
            </template>
          </template>
          <a-form-item label="状态" name="status">
            <a-switch v-model:checked="formState.status" />
          </a-form-item>
          <a-form-item label="备注" name="remark">
            <a-textarea v-model:value="formState.remark" placeholder="请输入备注信息" :rows="4" />
          </a-form-item>

          <!-- 邮件通知配置 -->
          <a-divider>邮件通知配置</a-divider>

          <a-form-item label="启用邮件通知" name="email_notification">
            <a-switch v-model:checked="formState.email_notification" />
          </a-form-item>

          <template v-if="formState.email_notification">
            <a-form-item label="邮件接收人" name="email_recipients" :rules="emailRecipientsRules">
              <a-select
                v-model:value="formState.email_recipients_array"
                mode="tags"
                placeholder="请输入邮件地址，支持多个收件人"
                :token-separators="[',', ';', ' ']"
                style="width: 100%"
                @change="handleEmailRecipientsChange"
              >
              </a-select>
              <div class="form-help-text">
                支持多个邮箱地址，用逗号、分号或空格分隔
              </div>
            </a-form-item>

            <a-form-item label="通知条件">
              <a-checkbox-group v-model:value="formState.email_conditions">
                <a-checkbox value="success">任务执行成功时通知</a-checkbox>
                <a-checkbox value="failure">任务执行失败时通知</a-checkbox>
              </a-checkbox-group>
            </a-form-item>

            <a-form-item label="邮件主题" name="email_subject">
              <a-input
                v-model:value="formState.email_subject"
                placeholder="自定义邮件主题（可选，留空使用默认模板）"
              />
              <div class="form-help-text">
                支持变量：{task_name}、{status}、{date}、{time}
              </div>
            </a-form-item>

            <a-form-item label="邮件内容" name="email_content">
              <a-textarea
                v-model:value="formState.email_content"
                placeholder="自定义邮件内容（可选，留空使用默认模板）"
                :rows="4"
              />
              <div class="form-help-text">
                支持变量：{task_name}、{status}、{result}、{date}、{time}、{datetime}
              </div>
            </a-form-item>

            <a-form-item>
              <a-button type="link" @click="handleTestEmail" :loading="testEmailLoading">
                <template #icon><MailOutlined /></template>
                发送测试邮件
              </a-button>
            </a-form-item>
          </template>
        </a-form>
      </a-modal>

      <!-- 日志查看模态框 -->
      <a-modal
        title="执行日志"
        v-model:open="logModalVisible"
        :footer="null"
        width="800px"
      >
        <div class="log-header">
          <a-space>
            <a-select
              :getPopupContainer="getPopupContainer"
              v-model:value="logStatus"
              style="width: 120px"
              @change="handleLogStatusChange"
            >
              <a-select-option value="">全部状态</a-select-option>
              <a-select-option value="success">成功</a-select-option>
              <a-select-option value="failed">失败</a-select-option>
            </a-select>
            <a-button type="primary" @click="refreshLogs">
              <template #icon><ReloadOutlined /></template>
              刷新
            </a-button>
          </a-space>
        </div>

        <a-table
          :columns="logColumns"
          :data-source="logList"
          :loading="logLoading"
          :pagination="{ 
            total: logTotal,
            current: logCurrent,
            pageSize: logPageSize,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: total => `共 ${total} 条`
          }"
          @change="handleLogTableChange"
        >
          <template #bodyCell="{ column, record }">
            <!-- 状态列 -->
            <template v-if="column.key === 'status'">
              <a-tag :color="record.status === 'success' ? 'success' : 'error'">
                {{ record.status === 'success' ? '成功' : '失败' }}
              </a-tag>
            </template>
            
            <!-- 结果列 -->
            <template v-if="column.key === 'result'">
              <a-typography-paragraph
                :ellipsis="{ rows: 2, expandable: true }"
                :content="formatResult(record.result)"
                @click="showResultModal(record.result)"
              />
            </template>
          </template>
        </a-table>
      </a-modal>

      <!-- 结果查看模态框 -->
      <a-modal
        title="查看结果"
        v-model:open="resultModalVisible"
        @cancel="resultModalVisible = false"
        width="600px"
        :footer="null"
        :maskClosable="false"
      >
        <div style="max-height: 600px; overflow-y: auto;">
          <pre>{{ parsedResult }}</pre>
        </div>
      </a-modal>
    </div>
  </system-page>
</template>

<style scoped>
.form-help-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.schedule-container .table-operations {
  margin-bottom: 16px;
}

.log-header {
  margin-bottom: 16px;
}
</style>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  HistoryOutlined,
  ReloadOutlined,
  MailOutlined
} from '@ant-design/icons-vue';
import SystemPage from '../components/SystemPage.vue';
import {
  getScheduleList,
  createSchedule,
  updateSchedule,
  deleteSchedule,
  getClassMethods,
  getScheduleLogs,
  runSchedule,
  testScheduleEmail
} from '@/api/system/schedule';

// 表格列定义
const columns = [
  {
    title: '任务名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '任务类型',
    dataIndex: 'type',
    key: 'type'
  },
  {
    title: '任务目标',
    dataIndex: 'target',
    key: 'target',
    ellipsis: true
  },
  {
    title: 'CRON表达式',
    dataIndex: 'cron',
    key: 'cron'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status'
  },
  {
    title: '最后执行时间',
    dataIndex: 'last_run_time',
    key: 'last_run_time'
  },
  {
    title: '下次执行时间',
    dataIndex: 'next_run_time',
    key: 'next_run_time'
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark',
    ellipsis: true
  },
  {
    title: '邮件通知',
    dataIndex: 'email_notification',
    key: 'email_notification',
    width: 100
  },
  {
    title: '操作',
    key: 'action',
    width: 450,
    fixed: 'right'
  }
];

// 表格数据
const scheduleList = ref([]);
const loading = ref(false);
const total = ref(0);
const current = ref(1);
const pageSize = ref(10);

// 表单相关
const formRef = ref(null);
const modalVisible = ref(false);
const modalLoading = ref(false);
const modalTitle = ref('新增任务');
const editingId = ref(null);
const methodsLoading = ref(false);
const classMethods = ref([]);
const testEmailLoading = ref(false);

const formState = reactive({
  name: '',
  type: undefined,
  target: undefined,
  period_type: undefined,
  days: undefined,
  hours: undefined,
  minutes: undefined,
  seconds: undefined,
  week_day: undefined,
  month_day: undefined,
  hour: undefined,
  minute: undefined,
  status: true,
  remark: '',
  // 邮件通知相关字段
  email_notification: false,
  email_recipients: '',
  email_recipients_array: [],
  email_subject: '',
  email_content: '',
  email_conditions: [],
  email_on_success: false,
  email_on_failure: true
});

const getPopupContainer = (triggerNode) => {
  return triggerNode.parentElement;
};

// 定义选项数据
const typeOptions = [
  { value: 'url', label: 'URL访问' },
  { value: 'backup', label: '数据库备份' },
  { value: 'class', label: '类方法调用' }
];

const periodTypeOptions = [
  { value: 'daily', label: '每天' },
  { value: 'n_days', label: 'N天' },
  { value: 'hourly', label: '每小时' },
  { value: 'n_hours', label: 'N小时' },
  { value: 'n_minutes', label: 'N分钟' },
  { value: 'weekly', label: '每周' },
  { value: 'monthly', label: '每月' },
  { value: 'n_seconds', label: 'N秒' }
];

const weekDayOptions = [
  { value: '1', label: '星期一' },
  { value: '2', label: '星期二' },
  { value: '3', label: '星期三' },
  { value: '4', label: '星期四' },
  { value: '5', label: '星期五' },
  { value: '6', label: '星期六' },
  { value: '0', label: '星期日' }
];

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入任务名称' }],
  type: [{ required: true, message: '请选择任务类型' }],
  target: [{ required: true, message: '请输入任务目标', trigger: 'change' }],
  period_type: [{ required: true, message: '请选择执行周期' }],
  days: [{
    required: true,
    message: '请输入间隔天数',
    trigger: 'change',
    async validator(rule, value) {
      if (formState.period_type === 'n_days' && (value === undefined || value <= 0)) {
        throw new Error('请输入大于0的间隔天数');
      }
    }
  }],
  hours: [{
    required: true,
    message: '请输入间隔小时',
    trigger: 'change',
    async validator(rule, value) {
      if (formState.period_type === 'n_hours' && (value === undefined || value <= 0)) {
        throw new Error('请输入大于0的间隔小时');
      }
    }
  }],
  minutes: [{
    required: true,
    message: '请输入间隔分钟',
    trigger: 'change',
    async validator(rule, value) {
      if (formState.period_type === 'n_minutes' && (value === undefined || value <= 0)) {
        throw new Error('请输入大于0的间隔分钟');
      }
    }
  }],
  seconds: [{
    required: true,
    message: '请输入间隔秒数',
    trigger: 'change',
    async validator(rule, value) {
      if (formState.period_type === 'n_seconds' && (value === undefined || value <= 0)) {
        throw new Error('请输入大于0的间隔秒数');
      }
    }
  }],
  week_day: [{
    required: true,
    message: '请选择星期',
    trigger: 'change',
    async validator(rule, value) {
      if (formState.period_type === 'weekly' && value === undefined) {
        throw new Error('请选择星期');
      }
    }
  }],
  month_day: [{
    required: true,
    message: '请输入日期',
    trigger: 'change',
    async validator(rule, value) {
      if (formState.period_type === 'monthly') {
        if (value === undefined || value <= 0 || value > 31) {
          throw new Error('请输入1-31之间的日期');
        }
      }
    }
  }],
  hour: [{
    required: true,
    message: '请输入小时',
    trigger: 'change',
    async validator(rule, value) {
      const needsHour = ['daily', 'n_days', 'weekly', 'monthly'].includes(formState.period_type);
      if (needsHour && (value === undefined || value < 0 || value > 23)) {
        throw new Error('请输入0-23之间的小时');
      }
    }
  }],
  minute: [{
    required: true,
    message: '请输入分钟',
    trigger: 'change',
    async validator(rule, value) {
      const needsMinute = ['daily', 'n_days', 'weekly', 'monthly', 'hourly'].includes(formState.period_type);
      if (needsMinute && (value === undefined || value < 0 || value > 59)) {
        throw new Error('请输入0-59之间的分钟');
      }
    }
  }]
};

// 邮件接收人验证规则
const emailRecipientsRules = [{
  required: true,
  message: '请输入邮件接收人',
  trigger: 'change',
  async validator(rule, value) {
    if (formState.email_notification && (!formState.email_recipients_array || formState.email_recipients_array.length === 0)) {
      throw new Error('启用邮件通知时必须配置邮件接收人');
    }

    // 验证邮箱格式
    if (formState.email_recipients_array && formState.email_recipients_array.length > 0) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      for (const email of formState.email_recipients_array) {
        if (!emailRegex.test(email.trim())) {
          throw new Error(`邮箱格式不正确: ${email}`);
        }
      }
    }
  }
}];

// 获取任务列表
const fetchScheduleList = async (params = {}) => {
  loading.value = true;
  try {
    const response = await getScheduleList({
      page: current.value,
      pageSize: pageSize.value,
      ...params
    });
    if (response.code === 200) {
      scheduleList.value = response.data.list;
      total.value = response.data.total;
    }
  } catch (error) {
    console.error('获取任务列表失败:', error);
    message.error('获取任务列表失败');
  } finally {
    loading.value = false;
  }
};

// 表格变化处理
const handleTableChange = (pagination) => {
  current.value = pagination.current;
  pageSize.value = pagination.pageSize;
  fetchScheduleList();
};

// 获取可用的类方法
const fetchClassMethods = async () => {
  methodsLoading.value = true;
  try {
    const response = await getClassMethods();
    if (response.code === 200) {
      classMethods.value = response.data;
    }
  } catch (error) {
    console.error('获取类方法列表失败:', error);
    message.error('获取类方法列表失败');
  } finally {
    methodsLoading.value = false;
  }
};

// 任务类型变化处理
const handleTypeChange = (value) => {
  formState.target = '';
  if (value === 'class') {
    fetchClassMethods();
  }
};

// 获取任务类型文本
const getTypeText = (type) => {
  const types = {
    url: 'URL访问',
    backup: '数据库备份',
    class: '类方法调用'
  };
  return types[type] || type;
};

// 获取任务类型颜色
const getTypeColor = (type) => {
  const colors = {
    url: 'blue',
    backup: 'green',
    class: 'purple'
  };
  return colors[type] || 'default';
};

// 显示新增模态框
const showAddModal = () => {
  editingId.value = null;
  modalTitle.value = '新增任务';
  formState.name = '';
  formState.type = undefined;
  formState.target = '';
  formState.period_type = undefined;
  formState.days = undefined;
  formState.hours = undefined;
  formState.minutes = undefined;
  formState.seconds = undefined;
  formState.week_day = undefined;
  formState.month_day = undefined;
  formState.hour = undefined;
  formState.minute = undefined;
  formState.status = true;
  formState.remark = '';
  // 重置邮件通知字段
  formState.email_notification = false;
  formState.email_recipients = '';
  formState.email_recipients_array = [];
  formState.email_subject = '';
  formState.email_content = '';
  formState.email_conditions = [];
  formState.email_on_success = false;
  formState.email_on_failure = true;
  modalVisible.value = true;
};

// 显示编辑模态框
const showEditModal = (record) => {
  editingId.value = record.id;
  modalTitle.value = '编辑任务';
  
  // 根据 CRON 表达式解析时间配置
  const cronConfig = parseCronExpression(record.cron);
  
  // 重置表单状态
  Object.keys(formState).forEach(key => {
    formState[key] = undefined;
  });
  
  // 基础信息赋值
  formState.name = record.name;
  formState.type = record.type;
  formState.target = record.target;
  formState.status = Boolean(record.status);
  formState.remark = record.remark;

  // 设置邮件通知字段
  formState.email_notification = Boolean(record.email_notification);
  formState.email_recipients = record.email_recipients || '';
  formState.email_recipients_array = record.email_recipients ? record.email_recipients.split(',').map(email => email.trim()) : [];
  formState.email_subject = record.email_subject || '';
  formState.email_content = record.email_content || '';
  formState.email_on_success = Boolean(record.email_on_success);
  formState.email_on_failure = Boolean(record.email_on_failure);

  // 设置邮件条件数组
  formState.email_conditions = [];
  if (record.email_on_success) formState.email_conditions.push('success');
  if (record.email_on_failure) formState.email_conditions.push('failure');

  // 设置执行周期和相关参数
  formState.period_type = cronConfig.period_type;
  if (cronConfig.days) formState.days = cronConfig.days;
  if (cronConfig.hours) formState.hours = cronConfig.hours;
  if (cronConfig.minutes) formState.minutes = cronConfig.minutes;
  if (cronConfig.seconds) formState.seconds = cronConfig.seconds;
  if (cronConfig.week_day) formState.week_day = cronConfig.week_day;
  if (cronConfig.month_day) formState.month_day = cronConfig.month_day;
  if (cronConfig.hour !== undefined) formState.hour = cronConfig.hour;
  if (cronConfig.minute !== undefined) formState.minute = cronConfig.minute;
  
  // 如果是类方法调用，获取可用的类方法列表
  if (record.type === 'class') {
    fetchClassMethods();
  }
  
  modalVisible.value = true;
};

// 解析 CRON 表达式
const parseCronExpression = (cron) => {
  const config = {
    period_type: undefined,
    days: undefined,
    hours: undefined,
    minutes: undefined,
    seconds: undefined,
    week_day: undefined,
    month_day: undefined,
    hour: undefined,
    minute: undefined
  };

  if (!cron) {
    return config;
  }

  try {
    const parts = cron.split(' ');
    
    // 处理秒级任务
    if (parts.length === 6) {
      const secondsPart = parts[0];
      if (secondsPart.includes('*/')) {
        config.period_type = 'n_seconds';
        config.seconds = parseInt(secondsPart.replace('*/', ''));
        return config;
      }
    }
    
    // 处理其他类型任务
    const [minute, hour, dayOfMonth, , dayOfWeek] = parts;
    
    // 解析分钟
    if (minute.includes('*/')) {
      config.period_type = 'n_minutes';
      config.minutes = parseInt(minute.replace('*/', ''));
    } else if (minute !== '*') {
      config.minute = parseInt(minute);
    }
    
    // 解析小时
    if (hour.includes('*/')) {
      config.period_type = 'n_hours';
      config.hours = parseInt(hour.replace('*/', ''));
    } else if (hour !== '*') {
      config.hour = parseInt(hour);
    }
    
    // 解析日期
    if (dayOfMonth.includes('*/')) {
      config.period_type = 'n_days';
      config.days = parseInt(dayOfMonth.replace('*/', ''));
    } else if (dayOfMonth !== '*') {
      config.period_type = 'monthly';
      config.month_day = parseInt(dayOfMonth);
    }
    
    // 解析星期
    if (dayOfWeek !== '*') {
      config.period_type = 'weekly';
      config.week_day = dayOfWeek;
    }
    
    // 如果只有具体的时分，则为每天执行
    if (!config.period_type && config.hour !== undefined && config.minute !== undefined) {
      config.period_type = 'daily';
    }
    
    // 如果只有分钟，则为每小时执行
    if (!config.period_type && config.minute !== undefined && hour === '*') {
      config.period_type = 'hourly';
    }
    
    return config;
  } catch (error) {
    console.error('解析CRON表达式失败:', error);
    return config;
  }
};

// 生成CRON表达式
const generateCron = (data) => {
  const { period_type, days, hours, minutes, seconds, week_day, month_day, hour, minute } = data;
  
  try {
    switch (period_type) {
      case 'daily':
        if (minute === undefined || hour === undefined) return '';
        return `${minute} ${hour} * * *`; // 每天
      case 'n_days':
        if (minute === undefined || hour === undefined || !days) return '';
        return `${minute} ${hour} */${days} * *`; // N天
      case 'hourly':
        if (minute === undefined) return '';
        return `${minute} * * * *`; // 每小时
      case 'n_hours':
        if (!hours) return '';
        return `0 */${hours} * * *`; // N小时
      case 'n_minutes':
        if (!minutes) return '';
        return `*/${minutes} * * * *`; // N分钟
      case 'weekly':
        if (minute === undefined || hour === undefined || week_day === undefined) return '';
        return `${minute} ${hour} * * ${week_day}`; // 每周
      case 'monthly':
        if (minute === undefined || hour === undefined || !month_day) return '';
        return `${minute} ${hour} ${month_day} * *`; // 每月
      case 'n_seconds':
        if (!seconds) return '';
        return `*/${seconds} * * * * *`; // N秒
      default:
        return '';
    }
  } catch (error) {
    console.error('生成CRON表达式失败:', error);
    return '';
  }
};

// 处理执行周期类型变化
const handlePeriodTypeChange = (value) => {
  // 重置相关字段
  formState.days = undefined;
  formState.hours = undefined;
  formState.minutes = undefined;
  formState.seconds = undefined;
  formState.week_day = undefined;
  formState.month_day = undefined;
  formState.hour = undefined;
  formState.minute = undefined;
};

// 处理模态框确认前的数据处理
const handleModalOk = async () => {
  try {
    await formRef.value.validate();
    modalLoading.value = true;

    // 生成CRON表达式
    const cron = generateCron(formState);
    if (!cron) {
      throw new Error('无效的执行周期配置');
    }

    // 处理邮件接收人数据
    const emailRecipients = formState.email_recipients_array.join(',');
    const emailOnSuccess = formState.email_conditions.includes('success');
    const emailOnFailure = formState.email_conditions.includes('failure');

    // 处理提交数据
    const submitData = {
      name: formState.name,
      type: formState.type,
      target: formState.target || '',
      cron: cron,
      status: formState.status ? 1 : 0,
      remark: formState.remark || '',
      // 邮件通知相关字段
      email_notification: formState.email_notification ? 1 : 0,
      email_recipients: emailRecipients,
      email_subject: formState.email_subject || '',
      email_content: formState.email_content || '',
      email_on_success: emailOnSuccess ? 1 : 0,
      email_on_failure: emailOnFailure ? 1 : 0
    };

    // 根据任务类型验证目标
    if (submitData.type === 'url' && !submitData.target.startsWith('http')) {
      throw new Error('URL地址必须以http或https开头');
    }

    let response;
    if (editingId.value) {
      response = await updateSchedule(editingId.value, submitData);
    } else {
      response = await createSchedule(submitData);
    }

    if (response.code === 200) {
      message.success(editingId.value ? '任务更新成功' : '任务添加成功');
      modalVisible.value = false;
      fetchScheduleList();
    } else {
      throw new Error(response.msg || '操作失败');
    }
  } catch (error) {
    console.error('操作失败:', error);
    message.error(error.message || '操作失败');
  } finally {
    modalLoading.value = false;
  }
};

// 处理模态框取消
const handleModalCancel = () => {
  modalVisible.value = false;
};

// 处理邮件接收人变化
const handleEmailRecipientsChange = (value) => {
  formState.email_recipients_array = value;
  formState.email_recipients = value.join(',');
};

// 测试邮件发送
const handleTestEmail = async () => {
  if (!editingId.value) {
    message.warning('请先保存任务后再测试邮件发送');
    return;
  }

  if (!formState.email_notification) {
    message.warning('请先启用邮件通知');
    return;
  }

  if (!formState.email_recipients_array || formState.email_recipients_array.length === 0) {
    message.warning('请先配置邮件接收人');
    return;
  }

  testEmailLoading.value = true;
  try {
    const response = await testScheduleEmail(editingId.value);
    if (response.code === 200) {
      message.success('测试邮件发送成功');
    } else {
      throw new Error(response.msg || '测试邮件发送失败');
    }
  } catch (error) {
    console.error('测试邮件发送失败:', error);
    message.error(error.message || '测试邮件发送失败');
  } finally {
    testEmailLoading.value = false;
  }
};

// 从列表中测试邮件发送
const handleTestEmailFromList = async (record) => {
  if (!record.email_notification) {
    message.warning('该任务未启用邮件通知');
    return;
  }

  if (!record.email_recipients) {
    message.warning('该任务未配置邮件接收人');
    return;
  }

  testEmailLoading.value = true;
  try {
    const response = await testScheduleEmail(record.id);
    if (response.code === 200) {
      message.success('测试邮件发送成功');
    } else {
      throw new Error(response.msg || '测试邮件发送失败');
    }
  } catch (error) {
    console.error('测试邮件发送失败:', error);
    message.error(error.message || '测试邮件发送失败');
  } finally {
    testEmailLoading.value = false;
  }
};

// 处理删除
const handleDelete = async (id) => {
  try {
    await deleteSchedule(id);
    message.success('删除成功');
    fetchScheduleList();
  } catch (error) {
    console.error('删除失败:', error);
    message.error('删除失败');
  }
};

// 日志相关数据
const logModalVisible = ref(false);
const logLoading = ref(false);
const logList = ref([]);
const logTotal = ref(0);
const logCurrent = ref(1);
const logPageSize = ref(10);
const logStatus = ref('');
const currentTaskId = ref(null);

// 日志表格列定义
const logColumns = [
  {
    title: '执行时间',
    dataIndex: 'executed_at',
    key: 'executed_at',
    width: 180
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '执行结果',
    dataIndex: 'result',
    key: 'result'
  }
];

// 显示日志模态框
const showLogModal = (record) => {
  currentTaskId.value = record.id;
  logModalVisible.value = true;
  logCurrent.value = 1;
  logStatus.value = '';
  fetchLogs();
};

// 获取日志列表
const fetchLogs = async () => {
  if (!currentTaskId.value) return;
  
  logLoading.value = true;
  try {
    const response = await getScheduleLogs(currentTaskId.value, {
      page: logCurrent.value,
      pageSize: logPageSize.value,
      status: logStatus.value
    });
    
    if (response.code === 200) {
      logList.value = response.data.list;
      logTotal.value = response.data.total;
    }
  } catch (error) {
    console.error('获取日志列表失败:', error);
    message.error('获取日志列表失败');
  } finally {
    logLoading.value = false;
  }
};

// 处理日志表格变化
const handleLogTableChange = (pagination) => {
  logCurrent.value = pagination.current;
  logPageSize.value = pagination.pageSize;
  fetchLogs();
};

// 处理日志状态变化
const handleLogStatusChange = () => {
  logCurrent.value = 1;
  fetchLogs();
};

// 刷新日志
const refreshLogs = () => {
  fetchLogs();
};

// 结果查看相关数据
const resultModalVisible = ref(false);
const parsedResult = ref({});

const showResultModal = (result) => {
  parsedResult.value = JSON.parse(result); // 解析 JSON
  resultModalVisible.value = true;
};

const formatResult = (result) => {
  // 如果结果过长，返回部分内容
  if (result.length > 100) {
    return result.substring(0, 100) + '...'; // 只显示前100个字符
  }
  return result;
};

// 在<script setup>中添加handleRun方法
const handleRun = async (record) => {
  try {
    const res = await runSchedule(record.id);
    if (res.code === 200) {
      message.success('执行成功：' + (res.data || ''));
    } else {
      message.error('执行失败：' + (res.msg || ''));
    }
  } catch (e) {
    message.error('执行失败：' + (e.msg || e.message || '未知错误'));
  }
};

// 初始化加载数据
onMounted(() => {
  fetchScheduleList();
});
</script> 

<style scoped>
.schedule-container {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  overflow: hidden;
}

.table-operations {
  margin-bottom: 16px;
}

.log-header {
  margin-bottom: 16px;
}

:deep(.ant-table-wrapper) {
  overflow: hidden;
}
</style> 