<template>
  <div class="role-list">
    <!-- 操作按钮 -->
    <div class="table-operations">
      <a-button type="primary" size="large" @click="showAddModal">
        <template #icon><PlusOutlined /></template>
        新增角色
      </a-button>
    </div>

    <!-- 角色列表 -->
    <a-table
      :columns="columns"
      :data-source="roleList"
      :loading="loading"
      :row-key="record => record.id"
      :pagination="{ 
        total: total,
        current: current,
        pageSize: pageSize,
        showSizeChanger: true,
        showQuickJumper: true,
        showTotal: total => `共 ${total} 条`
      }"
      @change="handleTableChange"
    >
      <!-- 状态列 -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="record.status ? 'success' : 'error'">
            {{ record.status ? '启用' : '禁用' }}
          </a-tag>
        </template>
        
        <!-- 操作列 -->
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" @click="showPermissionModal(record)">
              <template #icon><SafetyCertificateOutlined /></template>
              分配权限
            </a-button>
            <a-button type="link" @click="showEditModal(record)">
              <template #icon><EditOutlined /></template>
              编辑
            </a-button>
            <a-popconfirm
              title="确定要删除此角色吗？"
              @confirm="handleDelete(record.id)"
              okText="确定"
              cancelText="取消"
            >
              <a-button type="link" danger>
                <template #icon><DeleteOutlined /></template>
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 角色表单模态框 -->
    <a-modal
      :title="modalTitle"
      :open="modalVisible"
      @ok="handleModalOk"
      @cancel="handleModalCancel"
      :confirmLoading="modalLoading"
      okText="确定"
      cancelText="取消"
    >
      <a-form
        ref="formRef"
        :model="formState"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 16 }"
      >
        <a-form-item label="角色名称" name="name">
          <a-input v-model:value="formState.name" placeholder="请输入角色名称" />
        </a-form-item>
        <a-form-item label="角色编码" name="code">
          <a-input 
            v-model:value="formState.code" 
            placeholder="请输入角色编码"
            :disabled="!!editingId"
          />
        </a-form-item>
        <a-form-item label="状态" name="status">
          <a-switch v-model:checked="formState.status" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea 
            v-model:value="formState.remark" 
            placeholder="请输入备注信息"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 分配权限模态框 -->
    <a-modal
      :title="`分配权限 - ${selectedRole?.name || ''}`"
      :open="permissionModalVisible"
      @ok="handlePermissionModalOk"
      @cancel="handlePermissionModalCancel"
      :confirmLoading="permissionModalLoading"
      okText="确定"
      cancelText="取消"
      width="400px"
    >
      <a-tree
        v-model:checked-keys="selectedPermissionIds"
        v-model:selected-keys="selectedKeys"
        :treeData="permissionTree"
        :fieldNames="{
          title: 'name',
          key: 'id',
          children: 'children'
        }"
        checkable
        autoExpandParent
        :defaultExpandAll="true"
        @check="handleCheck"
      />
    </a-modal>
  </div>
</template>

<script>
import { ref, reactive, getCurrentInstance } from 'vue';
import { message } from 'ant-design-vue';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SafetyCertificateOutlined
} from '@ant-design/icons-vue';
import {
  getRoleList,
  createRole,
  updateRole,
  deleteRole,
  getRolePermissions,
  assignRolePermissions
} from '@/api/system/role';
import { getPermissionTree } from '@/api/system/permission';

export default {
  name: 'RoleList',
  components: {
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    SafetyCertificateOutlined
  },
  setup() {
    const { proxy } = getCurrentInstance();

    // 表格列定义
    const columns = [
      {
        title: '角色名称',
        dataIndex: 'name',
        key: 'name'
      },
      {
        title: '角色编码',
        dataIndex: 'code',
        key: 'code'
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100
      },
      {
        title: '备注',
        dataIndex: 'remark',
        key: 'remark',
        ellipsis: true
      },
      // {
      //   title: '创建时间',
      //   dataIndex: 'createTime',
      //   key: 'createTime',
      //   width: 180
      // },
      {
        title: '操作',
        key: 'action',
        width: 350,
        fixed: 'right'
      }
    ];

    // 表格数据
    const roleList = ref([]);
    const loading = ref(false);
    const total = ref(0);
    const current = ref(1);
    const pageSize = ref(10);

    // 获取角色列表
    const fetchRoleList = async (params = {}) => {
      loading.value = true;
      try {
        const response = await getRoleList(params);
        if (response.code === 200) {
          roleList.value = response.data.list;
          total.value = response.data.total;
        } else {
          message.error(response.msg || '获取角色列表失败');
        }
      } catch (error) {
        console.error('获取角色列表失败:', error);
        message.error('获取角色列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 表单相关
    const formRef = ref(null);
    const modalVisible = ref(false);
    const modalLoading = ref(false);
    const modalTitle = ref('新增角色');
    const editingId = ref(null);

    // 表单状态
    const formState = reactive({
      name: '',
      code: '',
      status: true,
      remark: ''
    });

    // 重置表单
    const resetForm = () => {
      formState.name = '';
      formState.code = '';
      formState.status = true;
      formState.remark = '';
      formRef.value?.resetFields();
    };

    const rules = {
      name: [{ required: true, message: '请输入角色名称' }],
      code: [{ required: true, message: '请输入角色编码' }]
    };

    // 权限树相关
    const permissionModalVisible = ref(false);
    const permissionModalLoading = ref(false);
    const selectedRole = ref(null);
    const permissionTree = ref([]);
    const selectedPermissionIds = ref([]);
    const selectedPermissionIds2 = ref([]);
    const selectedKeys = ref([]);

    // 获取权限树数据
    const fetchPermissionTree = async () => {
      try {
        const response = await getPermissionTree();
        if (response.code === 200) {
          permissionTree.value = response.data;
        } else {
          message.error(response.msg || '获取权限树失败');
        }
      } catch (error) {
        console.error('获取权限树失败:', error);
        message.error('获取权限树失败');
      }
    };

    // 获取角色权限
    const fetchRolePermissions = async (roleId) => {
      try {
        const response = await getRolePermissions(roleId);
        if (response.code === 200) {
          const assignedPermissions = response.data.map(item => item.id);
          
          // 处理全选和半选状态
          const checkedKeys = [];
          const halfCheckedKeys = [];
          
          // 遍历权限树
          const checkNode = (node) => {
            if (node.children) {
              const childrenIds = node.children.map(child => child.id);
              const allChildrenAssigned = childrenIds.every(id => assignedPermissions.includes(id));
              const someChildrenAssigned = childrenIds.some(id => assignedPermissions.includes(id));
              
              if (allChildrenAssigned) {
                checkedKeys.push(node.id);
              } else if (someChildrenAssigned) {
                halfCheckedKeys.push(node.id);
              }
              
              node.children.forEach(checkNode);
            } else if (assignedPermissions.includes(node.id)) {
              checkedKeys.push(node.id);
            }
          };
          
          permissionTree.value.forEach(checkNode);
          
          selectedPermissionIds.value = checkedKeys;
          selectedPermissionIds2.value = [...checkedKeys, ...halfCheckedKeys];
        } else {
          message.error(response.msg || '获取角色权限失败');
        }
      } catch (error) {
        console.error('获取角色权限失败:', error);
        message.error('获取角色权限失败');
      }
    };

    // 显示分配权限模态框
    const showPermissionModal = async (role) => {
      selectedRole.value = role;
      permissionModalVisible.value = true;
      await fetchPermissionTree();
      await fetchRolePermissions(role.id);
    };

    // 处理分配权限
    const handlePermissionModalOk = async () => {
      if (!selectedRole.value) return;
      
      permissionModalLoading.value = true;
      try {
        const response = await assignRolePermissions(selectedRole.value.id, {
          permissions: selectedPermissionIds2.value
        });
        
        if (response.code === 200) {
          message.success('权限分配成功');
          permissionModalVisible.value = false;
        } else {
          message.error(response.msg || '权限分配失败');
        }
      } catch (error) {
        console.error('分配权限失败:', error);
        message.error('分配权限失败');
      } finally {
        permissionModalLoading.value = false;
      }
    };

    // 处理表格分页、排序、筛选变化
    const handleTableChange = (pagination) => {
      current.value = pagination.current;
      pageSize.value = pagination.pageSize;
      fetchRoleList({
        page: pagination.current,
        pageSize: pagination.pageSize
      });
    };

    // 显示新增模态框
    const showAddModal = () => {
      editingId.value = null;
      modalTitle.value = '新增角色';
      resetForm();
      modalVisible.value = true;
    };

    // 显示编辑模态框
    const showEditModal = (record) => {
      editingId.value = record.id;
      modalTitle.value = '编辑角色';
      
      // 重置表单状态
      formState.name = record.name;
      formState.code = record.code;
      formState.status = record.status === 1;
      formState.remark = record.remark || '';
      
      modalVisible.value = true;
    };

    // 处理模态框确认
    const handleModalOk = async () => {
      try {
        await formRef.value.validate();
        modalLoading.value = true;

        const data = {
          name: formState.name,
          code: formState.code,
          status: formState.status ? 1 : 0,  // 将布尔值转换为数字
          remark: formState.remark
        };

        let response;
        if (editingId.value) {
          response = await updateRole(editingId.value, data);
        } else {
          response = await createRole(data);
        }

        if (response.code === 200) {
          message.success(editingId.value ? '更新成功' : '创建成功');
          modalVisible.value = false;
          fetchRoleList({
            page: current.value,
            pageSize: pageSize.value
          });
        } else {
          message.error(response.msg || (editingId.value ? '更新失败' : '创建失败'));
        }
      } catch (error) {
        console.error('保存角色失败:', error);
        message.error('保存失败');
      } finally {
        modalLoading.value = false;
      }
    };

    // 处理模态框取消
    const handleModalCancel = () => {
      resetForm();
      modalVisible.value = false;
    };

    // 处理删除
    const handleDelete = async (id) => {
      try {
        const response = await deleteRole(id);
        if (response.code === 0) {
          message.success('删除成功');
          fetchRoleList({
            page: current.value,
            pageSize: pageSize.value
          });
        } else {
          message.error(response.msg || '删除失败');
        }
      } catch (error) {
        console.error('删除角色失败:', error);
        message.error('删除失败');
      }
    };

    // 处理权限选中
    const handleCheck = (checkedKeys, e) => {
      // 子节点选中时，父节点的值也需要传递
      if (e.halfCheckedKeys.length > 0) {
        selectedPermissionIds2.value = checkedKeys.concat(e.halfCheckedKeys);
      } else {
        selectedPermissionIds2.value = checkedKeys;
      }
      selectedPermissionIds.value = checkedKeys;
    };

    // 初始化加载数据
    fetchRoleList({
      page: current.value,
      pageSize: pageSize.value
    });

    return {
      columns,
      roleList,
      loading,
      total,
      current,
      pageSize,
      formRef,
      formState,
      rules,
      modalVisible,
      modalLoading,
      modalTitle,
      editingId,
      permissionModalVisible,
      permissionModalLoading,
      selectedRole,
      permissionTree,
      selectedPermissionIds,
      selectedPermissionIds2,
      selectedKeys,
      handleTableChange,
      showAddModal,
      showEditModal,
      handleModalOk,
      handleModalCancel,
      handleDelete,
      showPermissionModal,
      handlePermissionModalOk,
      handlePermissionModalCancel: () => permissionModalVisible.value = false,
      handleCheck
    };
  }
};
</script>

<style scoped>
.role-list {
  min-height: 400px;
}

.table-operations {
  margin-bottom: 16px;
}
</style>
