import request from '@/utils/request';

// 获取字典列表
export function getDictList(params) {
  return request({
    url: '/system/dict/list',
    method: 'get',
    params
  });
}

// 获取字典项列表
export function getDictionaryItems(dictCode) {
  return request({
    url: '/system/dict/items',
    method: 'get',
    params: {
      dict_code: dictCode
    }
  });
}

// 创建字典
export function createDict(data) {
  return request({
    url: '/system/dict',
    method: 'post',
    data
  });
}

// 更新字典
export function updateDict(id, data) {
  return request({
    url: `/system/dict/${id}`,
    method: 'put',
    data
  });
}

// 删除字典
export function deleteDict(id) {
  return request({
    url: `/system/dict/${id}`,
    method: 'delete'
  });
}

// 创建字典项
export function createDictItem(data) {
  return request({
    url: '/system/dict/item',
    method: 'post',
    data
  });
}

// 更新字典项
export function updateDictItem(id, data) {
  return request({
    url: `/system/dict/item/${id}`,
    method: 'put',
    data
  });
}

// 删除字典项
export function deleteDictItem(id) {
  return request({
    url: `/system/dict/item/${id}`,
    method: 'delete'
  });
} 