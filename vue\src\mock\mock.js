import Mock from 'mockjs';

// 设置延迟时间
Mock.setup({
  timeout: '100-600'
});

// 模拟成员数据存储
let members = [
  {
    id: 1,
    name: '张三',
    nickname: '小张',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Felix'
  },
  {
    id: 2,
    name: '李四',
    nickname: '阿李',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Aneka'
  },
  {
    id: 3,
    name: '王五',
    nickname: '老王',
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Jasper'
  }
];

// 获取成员列表
Mock.mock('/api/members', 'get', () => {
  return {
    code: 200,
    message: '获取成功',
    data: members
  };
});

// 添加成员
Mock.mock('/api/members', 'post', (options) => {
  const newMember = JSON.parse(options.body);
  const id = members.length ? Math.max(...members.map(m => m.id)) + 1 : 1;
  
  const memberData = {
    id,
    name: newMember.name,
    nickname: newMember.nickname,
    avatar: newMember.avatar
  };
  
  members.push(memberData);
  
  return {
    code: 200,
    message: '添加成功',
    data: memberData
  };
});

// 更新成员
Mock.mock(new RegExp('/api/members/\\d+'), 'put', (options) => {
  const id = parseInt(options.url.match(/\/api\/members\/(\d+)/)[1]);
  const updateData = JSON.parse(options.body);
  
  const index = members.findIndex(m => m.id === id);
  if (index > -1) {
    members[index] = {
      ...members[index],
      ...updateData
    };
    
    return {
      code: 200,
      message: '更新成功',
      data: members[index]
    };
  }
  
  return {
    code: 404,
    message: '成员不存在'
  };
});

// 删除成员
Mock.mock(new RegExp('/api/members/\\d+'), 'delete', (options) => {
  const id = parseInt(options.url.match(/\/api\/members\/(\d+)/)[1]);
  const index = members.findIndex(m => m.id === id);
  
  if (index > -1) {
    members.splice(index, 1);
    return {
      code: 200,
      message: '删除成功'
    };
  }
  
  return {
    code: 404,
    message: '成员不存在'
  };
});

// 登录接口
Mock.mock('/api/login', 'post', (options) => {
  const { username, password } = JSON.parse(options.body);
  if (username === 'admin' && password === 'password') {
    return {
      code: 200,
      message: 'success',
      data: {
        token: 'mock-token-xxxxx',
        userInfo: {
          id: 1,
          username: 'admin',
          name: '张三',
          nickname: '管理员',
          avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
          roles: ['admin'],
          permissions: ['*']
        }
      }
    };
  }
  return {
    code: 401,
    message: '用户名或密码错误'
  };
});

// 获取用户信息接口
Mock.mock('/api/user/info', 'get', () => {
  return {
    code: 200,
    message: 'success',
    data: {
      id: 1,
      username: 'admin',
      name: '张三',
      nickname: '管理员',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
      roles: ['admin'],
      permissions: ['*']
    }
  };
});

// 退出登录接口
Mock.mock('/api/logout', 'post', {
  code: 200,
  message: '退出成功'
});

// 模拟字典数据存储
let dictList = [
  {
    id: 1,
    name: '性别',
    code: 'sex',
    status: true,
    remark: '用户性别',
    createTime: '2024-01-01 12:00:00'
  },
  {
    id: 2,
    name: '状态',
    code: 'status',
    status: true,
    remark: '通用状态',
    createTime: '2024-01-01 12:00:00'
  }
];

// 模拟字典项数据存储
let dictItemsList = [
  {
    id: 1,
    dictId: 1,
    label: '男',
    value: '1',
    sort: 1,
    status: true
  },
  {
    id: 2,
    dictId: 1,
    label: '女',
    value: '2',
    sort: 2,
    status: true
  },
  {
    id: 3,
    dictId: 2,
    label: '启用',
    value: 'true',
    sort: 1,
    status: true
  },
  {
    id: 4,
    dictId: 2,
    label: '禁用',
    value: 'false',
    sort: 2,
    status: true
  }
];

// 获取字典列表
Mock.mock(/\/system\/dict\/list/, 'get', (options) => {
  const params = getQueryParams(options.url);
  const { page = 1, pageSize = 10 } = params;
  
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const list = dictList.slice(startIndex, endIndex);
  
  return {
    code: 200,
    message: '获取成功',
    data: {
      list,
      total: dictList.length
    }
  };
});

// 新增字典
Mock.mock('/system/dict', 'post', (options) => {
  const dict = JSON.parse(options.body);
  const id = dictList.length ? Math.max(...dictList.map(d => d.id)) + 1 : 1;
  
  const newDict = {
    ...dict,
    id,
    createTime: new Date().toLocaleString()
  };
  
  dictList.push(newDict);
  
  return {
    code: 200,
    message: '添加成功',
    data: newDict
  };
});

// 更新字典
Mock.mock(/\/system\/dict\/\d+/, 'put', (options) => {
  const id = parseInt(options.url.match(/\/system\/dict\/(\d+)/)[1]);
  const updateData = JSON.parse(options.body);
  
  const index = dictList.findIndex(d => d.id === id);
  if (index > -1) {
    dictList[index] = {
      ...dictList[index],
      ...updateData
    };
    
    return {
      code: 200,
      message: '更新成功',
      data: dictList[index]
    };
  }
  
  return {
    code: 404,
    message: '字典不存在'
  };
});

// 删除字典
Mock.mock(/\/system\/dict\/\d+/, 'delete', (options) => {
  const id = parseInt(options.url.match(/\/system\/dict\/(\d+)/)[1]);
  const index = dictList.findIndex(d => d.id === id);
  
  if (index > -1) {
    dictList.splice(index, 1);
    // 同时删除关联的字典项
    dictItemsList = dictItemsList.filter(item => item.dictId !== id);
    return {
      code: 200,
      message: '删除成功'
    };
  }
  
  return {
    code: 404,
    message: '字典不存在'
  };
});

// 获取字典项列表
Mock.mock(/\/system\/dict\/items\/\d+/, 'get', (options) => {
  const dictId = parseInt(options.url.match(/\/system\/dict\/items\/(\d+)/)[1]);
  const items = dictItemsList.filter(item => item.dictId === dictId);
  
  return {
    code: 200,
    message: '获取成功',
    data: items
  };
});

// 新增字典项
Mock.mock('/system/dict/items', 'post', (options) => {
  const item = JSON.parse(options.body);
  const id = dictItemsList.length ? Math.max(...dictItemsList.map(i => i.id)) + 1 : 1;
  
  const newItem = {
    ...item,
    id
  };
  
  dictItemsList.push(newItem);
  
  return {
    code: 200,
    message: '添加成功',
    data: newItem
  };
});

// 更新字典项
Mock.mock(/\/system\/dict\/items\/\d+/, 'put', (options) => {
  const id = parseInt(options.url.match(/\/system\/dict\/items\/(\d+)/)[1]);
  const updateData = JSON.parse(options.body);
  
  const index = dictItemsList.findIndex(i => i.id === id);
  if (index > -1) {
    dictItemsList[index] = {
      ...dictItemsList[index],
      ...updateData
    };
    
    return {
      code: 200,
      message: '更新成功',
      data: dictItemsList[index]
    };
  }
  
  return {
    code: 404,
    message: '字典项不存在'
  };
});

// 删除字典项
Mock.mock(/\/system\/dict\/items\/\d+/, 'delete', (options) => {
  const id = parseInt(options.url.match(/\/system\/dict\/items\/(\d+)/)[1]);
  const index = dictItemsList.findIndex(i => i.id === id);
  
  if (index > -1) {
    dictItemsList.splice(index, 1);
    return {
      code: 200,
      message: '删除成功'
    };
  }
  
  return {
    code: 404,
    message: '字典项不存在'
  };
});

// 模拟成员数据存储
let memberList = [
  {
    id: 1,
    username: 'admin',
    name: '张三',
    nickname: '小张',
    // avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Felix',
    email: '<EMAIL>',
    mobile: '13800138000',
    status: true,
    createTime: '2024-01-01 12:00:00',
    remark: '超级管理员'
  },
  {
    id: 2,
    username: 'test',
    name: '李四',
    nickname: '阿李',
    // avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Aneka',
    email: '<EMAIL>',
    mobile: '13800138001',
    status: true,
    createTime: '2024-01-01 12:00:00',
    remark: null
  }
];

// 模拟成员角色关联数据
let memberRoles = [
  { memberId: 1, roleIds: [1] }  // admin 用户拥有超级管理员角色
];

// 获取成员列表
Mock.mock(/\/system\/member\/list(\?.+)?$/, 'get', (options) => {
  const params = getQueryParams(options.url);
  const page = parseInt(params.page) || 1;
  const pageSize = parseInt(params.pageSize) || 10;
  
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const list = memberList.slice(start, end);
  
  return {
    code: 200,
    message: 'success',
    data: {
      list,
      total: memberList.length
    }
  };
});

// 新增成员
Mock.mock('/system/member', 'post', (options) => {
  const member = JSON.parse(options.body);
  
  // 检查用户名是否已存在
  if (memberList.some(m => m.username === member.username)) {
    return {
      code: 400,
      message: '用户名已存在'
    };
  }

  const newMember = {
    id: memberList.length + 1,
    ...member,
    createTime: new Date().toLocaleString(),
    status: member.status ?? true
  };

  memberList.push(newMember);

  return {
    code: 200,
    message: 'success',
    data: newMember
  };
});

// 更新成员
Mock.mock(/\/system\/member\/\d+$/, 'put', (options) => {
  const id = parseInt(options.url.match(/\/system\/member\/(\d+)/)[1]);
  const member = JSON.parse(options.body);
  
  const index = memberList.findIndex(m => m.id === id);
  if (index === -1) {
    return {
      code: 404,
      message: '成员不存在'
    };
  }

  memberList[index] = {
    ...memberList[index],
    ...member
  };

  return {
    code: 200,
    message: 'success'
  };
});

// 删除成员
Mock.mock(/\/system\/member\/\d+$/, 'delete', (options) => {
  const id = parseInt(options.url.match(/\/system\/member\/(\d+)/)[1]);
  
  const index = memberList.findIndex(m => m.id === id);
  if (index === -1) {
    return {
      code: 404,
      message: '成员不存在'
    };
  }

  memberList.splice(index, 1);
  // 同时删除角色关联
  memberRoles = memberRoles.filter(mr => mr.memberId !== id);

  return {
    code: 200,
    message: 'success'
  };
});

// 修改密码
Mock.mock(/\/system\/member\/\d+\/password$/, 'put', (options) => {
  const id = parseInt(options.url.match(/\/system\/member\/(\d+)/)[1]);
  const { password } = JSON.parse(options.body);
  
  const member = memberList.find(m => m.id === id);
  if (!member) {
    return {
      code: 404,
      message: '成员不存在'
    };
  }

  // 实际应用中需要对密码进行加密
  member.password = password;

  return {
    code: 200,
    message: 'success'
  };
});

// 获取成员角色
Mock.mock(/\/system\/member\/\d+\/roles$/, 'get', (options) => {
  const id = parseInt(options.url.match(/\/system\/member\/(\d+)/)[1]);
  
  const memberRole = memberRoles.find(mr => mr.memberId === id);
  
  return {
    code: 200,
    message: 'success',
    data: memberRole ? memberRole.roleIds : []
  };
});

// 分配角色
Mock.mock(/\/system\/member\/\d+\/roles$/, 'put', (options) => {
  const id = parseInt(options.url.match(/\/system\/member\/(\d+)/)[1]);
  const { roleIds } = JSON.parse(options.body);
  
  const index = memberRoles.findIndex(mr => mr.memberId === id);
  if (index === -1) {
    memberRoles.push({ memberId: id, roleIds });
  } else {
    memberRoles[index].roleIds = roleIds;
  }

  return {
    code: 200,
    message: 'success'
  };
});

// 模拟角色数据
let roleList = [
  {
    id: 1,
    name: '超级管理员',
    code: 'admin',
    status: true,
    createTime: '2024-01-01 12:00:00',
    remark: '系统超级管理员'
  },
  {
    id: 2,
    name: '普通用户',
    code: 'user',
    status: true,
    createTime: '2024-01-01 12:00:00',
    remark: '普通用户'
  }
];

// 获取角色列表（不分页）
Mock.mock('/api/system/role/list', 'get', () => {
  return {
    code: 200,
    message: 'success',
    data: roleList  // 直接返回列表，不包装在 list 字段中
  };
});

// 获取角色列表（分页）
Mock.mock(/\/api\/system\/role\/list(\?.+)?$/, 'get', (options) => {
  const params = getQueryParams(options.url);
  const page = parseInt(params.page) || 1;
  const pageSize = parseInt(params.pageSize) || 10;
  
  const start = (page - 1) * pageSize;
  const end = start + pageSize;
  const list = roleList.slice(start, end);
  
  return {
    code: 200,
    message: 'success',
    data: {
      list,
      total: roleList.length
    }
  };
});

// 新增角色
Mock.mock('/system/role', 'post', (options) => {
  const role = JSON.parse(options.body);
  
  // 检查角色编码是否已存在
  if (roleList.some(r => r.code === role.code)) {
    return {
      code: 400,
      message: '角色编码已存在'
    };
  }

  const newRole = {
    id: roleList.length + 1,
    ...role,
    createTime: new Date().toLocaleString()
  };

  roleList.push(newRole);

  return {
    code: 200,
    message: 'success',
    data: newRole
  };
});

// 更新角色
Mock.mock(/\/system\/role\/\d+$/, 'put', (options) => {
  const id = parseInt(options.url.match(/\/system\/role\/(\d+)/)[1]);
  const role = JSON.parse(options.body);
  
  const index = roleList.findIndex(r => r.id === id);
  if (index === -1) {
    return {
      code: 404,
      message: '角色不存在'
    };
  }

  roleList[index] = {
    ...roleList[index],
    ...role
  };

  return {
    code: 200,
    message: 'success'
  };
});

// 删除角色
Mock.mock(/\/system\/role\/\d+$/, 'delete', (options) => {
  const id = parseInt(options.url.match(/\/system\/role\/(\d+)/)[1]);
  
  const index = roleList.findIndex(r => r.id === id);
  if (index === -1) {
    return {
      code: 404,
      message: '角色不存在'
    };
  }

  roleList.splice(index, 1);

  return {
    code: 200,
    message: 'success'
  };
});

// 获取角色权限
Mock.mock(/\/system\/role\/\d+\/permissions$/, 'get', (options) => {
  const id = parseInt(options.url.match(/\/system\/role\/(\d+)/)[1]);
  
  // 模拟超级管理员拥有所有权限
  if (id === 1) {
    return {
      code: 200,
      message: 'success',
      data: [1, 2, 3, 4] // 所有权限的ID
    };
  }

  return {
    code: 200,
    message: 'success',
    data: [] // 其他角色默认无权限
  };
});

// 分配角色权限
Mock.mock(/\/system\/role\/\d+\/permissions$/, 'put', (options) => {
  const id = parseInt(options.url.match(/\/system\/role\/(\d+)/)[1]);
  const { permissionIds } = JSON.parse(options.body);
  
  return {
    code: 200,
    message: 'success'
  };
});

// 模拟权限数据
let permissionList = [
  {
  id: 1,
    name: '系统管理',
    code: 'system',
    type: 'menu',
    parentId: 0,
    path: '/system',
    component: 'system/index',
    icon: 'SettingOutlined',
    sort: 1,
    status: true,
    createTime: '2024-01-01 12:00:00',
    children: [
      {
        id: 2,
        name: '成员管理',
        code: 'system:member',
        type: 'menu',
        parentId: 1,
        path: '/system/member',
        component: 'system/member/index',
        icon: 'UserOutlined',
        sort: 1,
        status: true,
        createTime: '2024-01-01 12:00:00',
        children: [
          {
            id: 6,
            name: '添加成员',
            code: 'system:member:add',
            type: 'button',
            parentId: 2,
            path: null,
            component: null,
            icon: null,
            sort: 1,
            status: true,
            createTime: '2024-01-01 12:00:00'
          },
          {
            id: 7,
            name: '编辑成员',
            code: 'system:member:edit',
            type: 'button',
            parentId: 2,
            path: null,
            component: null,
            icon: null,
            sort: 2,
            status: true,
            createTime: '2024-01-01 12:00:00'
          }
        ]
      },
      {
        id: 3,
        name: '角色管理',
        code: 'system:role',
        type: 'menu',
        parentId: 1,
        path: '/system/role',
        component: 'system/role/index',
        icon: 'TeamOutlined',
        sort: 2,
        status: true,
        createTime: '2024-01-01 12:00:00',
        children: [
          {
            id: 8,
            name: '添加角色',
            code: 'system:role:add',
            type: 'button',
            parentId: 3,
            path: null,
            component: null,
            icon: null,
            sort: 1,
            status: true,
            createTime: '2024-01-01 12:00:00'
          },
          {
            id: 9,
            name: '编辑角色',
            code: 'system:role:edit',
            type: 'button',
            parentId: 3,
            path: null,
            component: null,
            icon: null,
            sort: 2,
            status: true,
            createTime: '2024-01-01 12:00:00'
          }
        ]
      },
      {
        id: 4,
        name: '权限管理',
        code: 'system:permission',
        type: 'menu',
        parentId: 1,
        path: '/system/permission',
        component: 'system/permission/index',
        icon: 'SafetyCertificateOutlined',
        sort: 3,
        status: true,
        createTime: '2024-01-01 12:00:00'
      },
      {
        id: 5,
        name: '字典管理',
        code: 'system:dict',
        type: 'menu',
        parentId: 1,
        path: '/system/dict',
        component: 'system/dict/index',
        icon: 'BookOutlined',
        sort: 4,
        status: true,
        createTime: '2024-01-01 12:00:00'
      }
    ]
  }
];

// 获取权限树
Mock.mock('/api/system/permission/tree', 'get', () => {
  return {
    code: 200,
    message: 'success',
    data: permissionList
  };
});

// 获取权限列表（扁平化）
Mock.mock('/system/permission/list', 'get', () => {
  // 将树形结构扁平化
  const flattenPermissions = (list) => {
    let result = [];
    list.forEach(item => {
      const { children, ...rest } = item;
      result.push(rest);
      if (children) {
        result = result.concat(flattenPermissions(children));
      }
    });
    return result;
  };

  return {
    code: 200,
    message: 'success',
    data: {
      list: flattenPermissions(permissionList),
      total: flattenPermissions(permissionList).length
    }
  };
});

// 新增权限
Mock.mock('/api/system/permission', 'post', (options) => {
  const permission = JSON.parse(options.body);
  
  // 生成新的ID
  const generateNewId = (list) => {
    let maxId = 0;
    const findMaxId = (items) => {
      items.forEach(item => {
        maxId = Math.max(maxId, item.id);
        if (item.children) {
          findMaxId(item.children);
        }
      });
    };
    findMaxId(list);
    return maxId + 1;
  };

  const newPermission = {
    id: generateNewId(permissionList),
    ...permission,
    createTime: new Date().toLocaleString()
  };

  // 添加到父节点的children中
  const addToParent = (list, parentId, newItem) => {
    for (let item of list) {
      if (item.id === parentId) {
        if (!item.children) {
          item.children = [];
        }
        item.children.push(newItem);
        return true;
      }
      if (item.children && addToParent(item.children, parentId, newItem)) {
        return true;
      }
    }
    return false;
  };

  if (permission.parentId === 0) {
    permissionList.push(newPermission);
  } else {
    addToParent(permissionList, permission.parentId, newPermission);
  }

  return {
    code: 200,
    message: 'success',
    data: newPermission
  };
});

// 更新权限
Mock.mock(/\/api\/system\/permission\/\d+$/, 'put', (options) => {
  const id = parseInt(options.url.match(/\/api\/system\/permission\/(\d+)/)[1]);
  const permission = JSON.parse(options.body);
  
  const updateNode = (list, id, data) => {
    for (let i = 0; i < list.length; i++) {
      if (list[i].id === id) {
        list[i] = { ...list[i], ...data };
        return true;
      }
      if (list[i].children && updateNode(list[i].children, id, data)) {
        return true;
      }
    }
    return false;
  };

  if (updateNode(permissionList, id, permission)) {
    return {
      code: 200,
      message: 'success'
    };
  }

  return {
    code: 404,
    message: '权限不存在'
  };
});

// 删除权限
Mock.mock(/\/api\/system\/permission\/\d+$/, 'delete', (options) => {
  const id = parseInt(options.url.match(/\/api\/system\/permission\/(\d+)/)[1]);
  
  const deleteNode = (list, id) => {
    for (let i = 0; i < list.length; i++) {
      if (list[i].id === id) {
        list.splice(i, 1);
        return true;
      }
      if (list[i].children && deleteNode(list[i].children, id)) {
        return true;
      }
    }
    return false;
  };

  if (deleteNode(permissionList, id)) {
    return {
      code: 200,
      message: 'success'
    };
  }

  return {
    code: 404,
    message: '权限不存在'
  };
});

// 辅助函数：解析查询参数
function getQueryParams(url) {
  const params = {};
  const queryString = url.split('?')[1];
  if (queryString) {
    queryString.split('&').forEach(param => {
      const [key, value] = param.split('=');
      params[key] = value;
    });
  }
  return params;
}