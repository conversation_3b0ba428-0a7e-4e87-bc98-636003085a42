import request from '@/utils/request'

// 获取申报模板列表
export function getDeclarationTemplates(params) {
  return request({
    url: '/system/declaration-templates',
    method: 'get',
    params
  })
}

// 获取表单选项数据
export function getFormOptions() {
  return request({
    url: '/system/declaration-templates/form-options',
    method: 'get'
  })
}

// 创建申报模板
export function createDeclarationTemplate(data) {
  return request({
    url: '/system/declaration-templates',
    method: 'post',
    data
  })
}

// 更新申报模板
export function updateDeclarationTemplate(id, data) {
  return request({
    url: `/system/declaration-templates/${id}`,
    method: 'put',
    data
  })
}

// 删除申报模板
export function deleteDeclarationTemplate(id) {
  return request({
    url: `/system/declaration-templates/${id}`,
    method: 'delete'
  })
} 