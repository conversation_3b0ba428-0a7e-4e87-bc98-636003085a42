<?php
namespace app\model;

use support\Model;

class OrderMessage extends Model
{
    protected $table = 'order_messages';
    
    protected $fillable = [
        'order_id',
        'order_no',
        'message_type',
        'message_status',
        'message_content',
        'created_at',
        'updated_at'
    ];

    // 报文类型常量
    const TYPE_ORDER = '311';
    const TYPE_MANIFEST = '621';

    /**
     * 关联订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    /**
     * 获取报文类型文本
     */
    public function getTypeText()
    {
        $typeMap = [
            self::TYPE_ORDER => '订单报文',
            self::TYPE_MANIFEST => '清单报文'
        ];
        return $typeMap[$this->message_type] ?? '未知类型';
    }
} 