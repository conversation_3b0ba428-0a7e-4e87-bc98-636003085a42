<template>
  <div class="forbidden-container">
    <div class="forbidden-content">
      <div class="forbidden-icon">
        <StopOutlined />
      </div>
      <h1>403</h1>
      <h2>抱歉，您没有访问该页面的权限</h2>
      <p>请联系管理员获取相应的访问权限</p>
      <div class="action-buttons">
        <a-button type="primary" @click="goBack">
          <template #icon><RollbackOutlined /></template>
          返回上一页
        </a-button>
        <a-button @click="goHome">
          <template #icon><HomeOutlined /></template>
          返回首页
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { StopOutlined, RollbackOutlined, HomeOutlined } from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const goBack = () => {
  router.back();
};

const goHome = () => {
  router.push('/');
};
</script>

<style scoped>
.forbidden-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.forbidden-content {
  text-align: center;
  padding: 48px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.forbidden-icon {
  font-size: 48px;
  color: #ff4d4f;
  margin-bottom: 24px;
}

h1 {
  font-size: 72px;
  font-weight: bold;
  color: #ff4d4f;
  margin: 0 0 16px;
  line-height: 1;
}

h2 {
  font-size: 24px;
  color: #272343;
  margin: 0 0 16px;
}

p {
  font-size: 16px;
  color: #666;
  margin: 0 0 32px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.action-buttons .ant-btn {
  min-width: 120px;
}
</style> 