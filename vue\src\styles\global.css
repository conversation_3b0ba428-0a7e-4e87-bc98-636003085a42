/* 主按钮样式 */
.ant-btn-primary {
  background: #272343 !important;
  border-color: #272343 !important;
}

.ant-btn-primary:hover {
  background: #494866 !important;
  border-color: #494866 !important;
}

/* 链接按钮样式 */
.ant-btn-link {
  color: #272343 !important;
}

.ant-btn-link:hover {
  color: #494866 !important;
}

/* 危险按钮保持红色 */
.ant-btn-link.ant-btn-dangerous {
  color: #ff4d4f !important;
}

.ant-btn-link.ant-btn-dangerous:hover {
  color: #ff7875 !important;
}

/* 其他按钮悬浮样式 */
.ant-btn:not(.ant-btn-primary):not(.ant-btn-link):hover {
  color: #272343 !important;
  border-color: #272343 !important;
} 

/* 分页 */
.ant-pagination-item-active {
  /* background-color: #272343 !important; */
  border-color: #272343 !important;
}

.ant-pagination-item-active a {
  color: #272343 !important;
}

/* 调整弹框层级 */
.ant-modal-wrap {
  z-index: 1100 !important;
}

.ant-modal-mask {
  z-index: 1050 !important;
}

/* 调整消息提示层级 */
.ant-message {
  z-index: 1150 !important;
}

/* 调整气泡确认框层级 */
.ant-popover {
  z-index: 1060 !important;
}

/* 调整下拉菜单层级 */
.ant-select-dropdown {
  z-index: 1070 !important;
}

/* 调整日期选择器层级 */
.ant-picker-dropdown {
  z-index: 1080 !important;
}

/* tabs */
.ant-tabs-tab {
  color: #272343 !important;
}

.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
  color: #272343 !important;
}

/* tabs下划线 */
.ant-tabs-ink-bar {
  background-color: #272343 !important;
}

/* 表单边距 */
.ant-modal-body {
  padding: 20px 0 !important;
}

/* 鼠标手形 */
.clickable {
  cursor: pointer; /* 将光标设置为小手形状 */
}