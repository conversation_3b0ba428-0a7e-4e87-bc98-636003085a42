import request from '@/utils/request';

// 获取成员列表
export function getMemberList(params) {
  return request({
    url: '/member/list',
    method: 'get',
    params
  });
}

// 创建成员
export function createMember(data) {
  return request({
    url: '/member/create',
    method: 'post',
    data
  });
}

// 更新成员
export function updateMember(data) {
  return request({
    url: `/member/update/${data.id}`,
    method: 'put',
    data
  });
}

// 删除成员
export function deleteMember(data) {
  return request({
    url: `/member/delete/${data.id}`,
    method: 'delete',
    data
  });
}

// 更新密码
export function updatePassword(data) {
  return request({
    url: `/member/password/${data.id}`,
    method: 'put',
    data
  });
}

// 获取成员角色
export function getMemberRoles(id) {
  return request({
    url: `/member/getRoles/${id}`,
    method: 'get'
  });
}

// 分配成员角色
export function assignMemberRoles(id, data) {
  return request({
    url: `/member/assignRoles/${id}`,
    method: 'post',
    data
  });
}
