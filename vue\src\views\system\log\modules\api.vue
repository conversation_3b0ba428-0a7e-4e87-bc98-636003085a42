<template>
  <div class="api-log">
    <!-- 搜索表单 -->
    <a-form  class="table-search-form">
      <a-row :gutter="48">
        <a-col :md="8" :sm="12">
          <a-form-item label="应用">
            <a-select
              v-model:value="searchForm.app_id"
              placeholder="请选择应用"
              allowClear
            >
              <a-select-option
                v-for="app in appList"
                :key="app.id"
                :value="app.id"
              >
                {{ app.name }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="8" :sm="12">
          <a-form-item label="状态">
            <a-select
              v-model:value="searchForm.status"
              placeholder="请选择状态"
              allowClear
            >
              <a-select-option :value="1">成功</a-select-option>
              <a-select-option :value="0">失败</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :md="8" :sm="24">
            <a-form-item label="时间范围">
              <a-range-picker 
                v-model:value="searchForm.timeRange"
                :show-time="{ format: 'HH:mm:ss' }"
                format="YYYY-MM-DD HH:mm:ss"
              />
            </a-form-item>
          </a-col>
      </a-row>
      <a-space>
          <a-button type="primary" @click="handleSearch">
          <template #icon><SearchOutlined /></template>
          搜索
          </a-button>
          <a-button @click="handleResetSearch">
          <template #icon><ReloadOutlined /></template>
          重置
          </a-button>
          <a-button danger @click="handleClear">
          <template #icon><DeleteOutlined /></template>
          清空
          </a-button>
      </a-space>
    </a-form>

    <!-- 日志列表 -->
    <a-table
      :columns="columns"
      :data-source="logList"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
      :row-key="record => record.id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'status'">
          <a-tag :color="record.status ? 'success' : 'error'">
            {{ record.status ? '成功' : '失败' }}
          </a-tag>
        </template>
        <template v-if="column.key === 'method'">
          <a-tag :color="getMethodColor(record.method)">
            {{ record.method }}
          </a-tag>
        </template>
        <template v-if="column.key === 'request_source'">
          {{ record.request_source || '-' }}
        </template>
        <template v-if="column.key === 'template_name'">
          {{ record.template?.name || '-' }}
        </template>
        <template v-if="column.key === 'created_at'">
          {{ formatDate(record.created_at) }}
        </template>
        <template v-if="column.key === 'action'">
          <a-space>
            <a-button type="link" @click="handleViewDetail(record)">
              <template #icon><eye-outlined /></template>
              详情
            </a-button>
            <a-popconfirm
              title="确定要删除这条日志吗？"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" danger>
                <template #icon><delete-outlined /></template>
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
    </a-table>

    <!-- 详情弹窗 -->
    <a-modal
      v-model:open="detailModalVisible"
      title="日志详情"
      :footer="null"
      width="680px"
      class="api-log-detail-modal"
      :bodyStyle="modalBodyStyle"
      @cancel="handleDetailModalClose"
    >
      <a-descriptions :column="1" bordered>
        <a-descriptions-item label="应用名称" :labelStyle="labelStyle">
          {{ currentLog?.request_source || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="模板名称" :labelStyle="labelStyle">
          {{ currentLog?.template?.name || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="请求方法" :labelStyle="labelStyle">
          <a-tag :color="getMethodColor(currentLog?.method)">
            {{ currentLog?.method }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="请求URL" :labelStyle="labelStyle" :contentStyle="{ wordBreak: 'break-all' }">
          {{ currentLog?.url }}
        </a-descriptions-item>
        <a-descriptions-item label="请求IP" :labelStyle="labelStyle">
          {{ currentLog?.ip }}
        </a-descriptions-item>
        <a-descriptions-item label="状态" :labelStyle="labelStyle">
          <a-tag :color="currentLog?.status ? 'success' : 'error'">
            {{ currentLog?.status ? '成功' : '失败' }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="创建时间" :labelStyle="labelStyle" >
          {{ formatDate(currentLog?.created_at) }}
        </a-descriptions-item>
        <a-descriptions-item label="错误信息" v-if="!currentLog?.status" :labelStyle="labelStyle" :contentStyle="{ wordBreak: 'break-all' }">
          <span style="color: #ff4d4f">{{ currentLog?.error_message }}</span>
        </a-descriptions-item>
        <a-descriptions-item label="请求头" :labelStyle="labelStyle" >
          <div class="code-block">
            <pre>{{ formatJson(currentLog?.headers) }}</pre>
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="请求数据" :labelStyle="labelStyle" >
          <div class="code-block">
            <pre>{{ formatJson(currentLog?.request_data) }}</pre>
          </div>
        </a-descriptions-item>
        <a-descriptions-item label="响应数据" :labelStyle="labelStyle" >
          <div class="code-block">
            <pre>{{ formatJson(currentLog?.response_data) }}</pre>
          </div>
        </a-descriptions-item>
      </a-descriptions>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import {
  SearchOutlined,
  ReloadOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { getApiLogs, deleteApiLog, clearApiLogs } from '@/api/system/log/api-log';
import { getApps } from '@/api/system/app';
import { formatDate } from '@/utils/utils';

// 应用列表
const appList = ref([]);
const logList = ref([]);
const loading = ref(false);

// 搜索表单
const searchForm = reactive({
  app_id: undefined,
  status: undefined,
  time: []
});

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: total => `共 ${total} 条`,
  showSizeChanger: true,
  showQuickJumper: true
});

// 表格列定义
const columns = [
  {
    title: '请求来源',
    key: 'request_source',
    width: 150
  },
  {
    title: '模板名称',
    key: 'template_name',
    width: 150
  },
  {
    title: '请求方法',
    key: 'method',
    width: 100
  },
  {
    title: '请求URL',
    dataIndex: 'url',
    ellipsis: true
  },
  {
    title: '请求IP',
    dataIndex: 'ip',
    width: 120
  },
  {
    title: '状态',
    key: 'status',
    width: 100
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 250,
    fixed: 'right'
  }
];

// 详情弹窗
const detailModalVisible = ref(false);
const currentLog = ref(null);

// 详情弹窗样式配置
const labelStyle = {
  width: '200px',
  fontWeight: '500',
  padding: '12px 24px'
};

const modalBodyStyle = {
  maxHeight: '80vh',
  overflowY: 'auto'
};

// 获取应用列表
const fetchApps = async () => {
  try {
    const res = await getApps();
    if (res.code === 200) {
      appList.value = res.data;
    }
  } catch (error) {
    console.error('获取应用列表失败:', error);
  }
};

// 获取日志列表
const fetchLogs = async () => {
  loading.value = true;
  try {
    const params = {
      page: pagination.current,
      pageSize: pagination.pageSize,
      app_id: searchForm.app_id,
      status: searchForm.status,
      start_time: searchForm.time[0]?.format('YYYY-MM-DD HH:mm:ss'),
      end_time: searchForm.time[1]?.format('YYYY-MM-DD HH:mm:ss')
    };

    const res = await getApiLogs(params);
    if (res.code === 200) {
      logList.value = res.data.list;
      pagination.total = res.data.total;
    }
  } catch (error) {
    console.error('获取日志列表失败:', error);
    message.error('获取日志列表失败');
  } finally {
    loading.value = false;
  }
};

// 搜索
const handleSearch = () => {
  pagination.current = 1;
  fetchLogs();
};

// 重置搜索
const handleResetSearch = () => {
  searchForm.app_id = undefined;
  searchForm.status = undefined;
  searchForm.time = [];
  handleSearch();
};

// 表格变化
const handleTableChange = (pag) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  fetchLogs();
};

// 获取请求方法的颜色
const getMethodColor = (method) => {
  const colors = {
    GET: 'blue',
    POST: 'green',
    PUT: 'orange',
    DELETE: 'red'
  };
  return colors[method] || 'default';
};

// 查看详情
const handleViewDetail = (record) => {
  currentLog.value = record;
  detailModalVisible.value = true;
};

// 删除日志
const handleDelete = async (record) => {
  try {
    const res = await deleteApiLog(record.id);
    if (res.code === 200) {
      message.success('删除成功');
      fetchLogs();
    }
  } catch (error) {
    console.error('删除日志失败:', error);
    message.error('删除日志失败');
  }
};

// 清空日志
const handleClear = async () => {
  try {
    const res = await clearApiLogs();
    if (res.code === 200) {
      message.success('清空成功');
      fetchLogs();
    }
  } catch (error) {
    console.error('清空日志失败:', error);
    message.error('清空日志失败');
  }
};

// 格式化JSON
const formatJson = (value) => {
  if (!value) return '';
  try {
    return JSON.stringify(value, null, 2);
  } catch (e) {
    return value;
  }
};

// 处理详情弹窗关闭
const handleDetailModalClose = () => {
  currentLog.value = null;
  detailModalVisible.value = false;
};

onMounted(() => {
  fetchApps();
  fetchLogs();
});
</script>

<style lang="less" scoped>
.api-log {
  .table-search-form {
    margin-bottom: 24px;
    padding: 24px;
    background: #fff;
    border-radius: 10px;
    border: 1px solid #e8e8e8;

    :deep(.ant-form-item) {
      margin-bottom: 16px;
      display: flex;
      margin-right: 0;
    }
  }

  .table-operations {
    margin-bottom: 16px;
  }

  :deep(.api-log-detail-modal) {
    .ant-descriptions-bordered {
      .ant-descriptions-item-content {
        padding: 12px 24px;
      }
    }

    .code-block {
      margin: -12px -24px;
      background: #f5f7fa;
      
      pre {
        margin: 0;
        padding: 12px 24px;
        font-family: Consolas, Monaco, 'Andale Mono', monospace;
        font-size: 12px;
        line-height: 1.5;
        white-space: pre-wrap;
        word-break: break-all;
        max-height: 300px;
        overflow: auto;
      }
    }
  }
}
</style> 