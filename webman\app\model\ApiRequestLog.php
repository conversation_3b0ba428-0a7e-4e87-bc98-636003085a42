<?php
namespace app\model;

use support\Model;

class ApiRequestLog extends Model
{
    protected $table = 'api_request_logs';
    
    // 禁用自动时间戳
    public $timestamps = false;
    
    protected $fillable = [
        'app_id',
        'template_id',
        'method',
        'url',
        'headers',
        'request_data',
        'response_data',
        'ip',
        'status',
        'error_message',
        'created_at',
        'updated_at'
    ];

    /**
     * 关联应用
     */
    public function app()
    {
        return $this->belongsTo(App::class, 'app_id');
    }

    /**
     * 关联模板
     */
    public function template()
    {
        return $this->belongsTo(AppConfigTemplate::class, 'template_id');
    }

    /**
     * 获取请求头
     */
    public function getHeadersAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 设置请求头
     */
    public function setHeadersAttribute($value)
    {
        $this->attributes['headers'] = json_encode($value);
    }

    /**
     * 获取请求数据
     */
    public function getRequestDataAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 设置请求数据
     */
    public function setRequestDataAttribute($value)
    {
        $this->attributes['request_data'] = json_encode($value);
    }

    /**
     * 获取响应数据
     */
    public function getResponseDataAttribute($value)
    {
        return $value ? json_decode($value, true) : [];
    }

    /**
     * 设置响应数据
     */
    public function setResponseDataAttribute($value)
    {
        $this->attributes['response_data'] = json_encode($value);
    }
} 