import request from '@/utils/request'

// 获取API密钥列表
export function getApiKeys(params) {
  return request({
    url: '/system/apikey',
    method: 'get',
    params
  })
}

// 创建API密钥
export function createApiKey(data) {
  return request({
    url: '/system/apikey',
    method: 'post',
    data
  })
}

// 更新API密钥
export function updateApiKey(id, data) {
  return request({
    url: `/system/apikey/${id}`,
    method: 'put',
    data
  })
}

// 删除API密钥
export function deleteApiKey(id) {
  return request({
    url: `/system/apikey/${id}`,
    method: 'delete'
  })
}

// 重置API密钥
export function resetApiKey(id) {
  return request({
    url: `/system/apikey/${id}/reset`,
    method: 'post'
  })
} 