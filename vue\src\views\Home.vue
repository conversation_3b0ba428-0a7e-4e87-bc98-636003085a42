<template>
  <div class="home-container">
    <!-- 数据概览 -->
    <a-row :gutter="[16, 16]" class="data-overview">
      <a-col :span="6">
        <a-card>
          <statistic
            title="今日订单数"
            :value="todayStats.orderCount"
            :precision="0"
          >
            <template #suffix>
              <span class="stat-suffix">单</span>
            </template>
          </statistic>
          <div class="stat-trend">
            <span :class="['trend-value', todayStats.orderTrend > 0 ? 'up' : 'down']">
              {{ Math.abs(todayStats.orderTrend) }}%
            </span>
            <span class="trend-label">较昨日</span>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <statistic
            title="今日销售额"
            :value="todayStats.salesAmount"
            :precision="2"
            :value-style="{ color: '#272343' }"
          >
            <template #prefix>
              <span class="stat-prefix">¥</span>
            </template>
          </statistic>
          <div class="stat-trend">
            <span :class="['trend-value', todayStats.salesTrend > 0 ? 'up' : 'down']">
              {{ Math.abs(todayStats.salesTrend) }}%
            </span>
            <span class="trend-label">较昨日</span>
          </div>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <statistic
            title="待处理订单"
            :value="todayStats.pendingCount"
            :precision="0"
          >
            <template #suffix>
              <span class="stat-suffix">单</span>
            </template>
          </statistic>
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <statistic
            title="客单价"
            :value="todayStats.averageAmount"
            :precision="2"
            :value-style="{ color: '#272343' }"
          >
            <template #prefix>
              <span class="stat-prefix">¥</span>
            </template>
          </statistic>
          <div class="stat-trend">
            <span :class="['trend-value', todayStats.averageTrend > 0 ? 'up' : 'down']">
              {{ Math.abs(todayStats.averageTrend) }}%
            </span>
            <span class="trend-label">较昨日</span>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 图表区域 -->
    <a-row :gutter="[16, 16]" class="chart-section">
      <a-col :span="16">
        <a-card title="订单趋势">
          <div id="orderTrendChart" ref="orderTrendChartRef" style="width: 100%; height: 300px"></div>
        </a-card>
      </a-col>
      <a-col :span="8">
        <a-card title="热销商品排行">
          <a-list
            size="small"
            :data-source="hotProducts"
            :pagination="false"
          >
            <template #renderItem="{ item, index }">
              <a-list-item>
                <div class="hot-product-item">
                  <span class="rank" :class="{ 'top-3': index < 3 }">{{ index + 1 }}</span>
                  <span class="name">{{ item.name }}</span>
                  <span class="sales">{{ item.sales }}件</span>
                  <span :class="['trend', item.trend > 0 ? 'up' : 'down']">
                    {{ Math.abs(item.trend) }}%
                  </span>
                </div>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
    </a-row>

    <!-- 功能入口 -->
    <a-row :gutter="[16, 16]" class="feature-entrance">
      <a-col :span="6">
        <a-card hoverable class="entrance-card" @click="goToOrderCenter">
          <template #cover>
            <div class="entrance-icon">
              <OrderedListOutlined />
            </div>
          </template>
          <a-card-meta title="订单中心" description="订单管理与处理中心" />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card hoverable class="entrance-card" @click="goToDataScreen">
          <template #cover>
            <div class="entrance-icon">
              <FundOutlined />
            </div>
          </template>
          <a-card-meta title="数据大屏" description="实时订单数据可视化" />
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { defineComponent, onMounted, onUnmounted, ref, nextTick } from 'vue';
import { Statistic } from 'ant-design-vue';
import { OrderedListOutlined, FundOutlined } from '@ant-design/icons-vue';
import * as echarts from 'echarts/core';
import { LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { useRouter } from 'vue-router';
import { getRealTimeData, getTrend, getHotProductsWithTrend } from '@/api/order';

// 注册必需的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  LineChart,
  CanvasRenderer
]);

export default defineComponent({
  name: 'Home',
  components: {
    Statistic,
    OrderedListOutlined,
    FundOutlined
  },
  setup() {
    const router = useRouter();
    let orderTrendChart = null;
    const orderTrendChartRef = ref(null);

    // 统计数据
    const todayStats = ref({
      orderCount: 0,
      orderTrend: 0,
      salesAmount: 0,
      salesTrend: 0,
      pendingCount: 0,
      averageAmount: 0,
      averageTrend: 0
    });

    const hotProducts = ref([]);

    // 获取实时数据
    const fetchRealTimeData = async () => {
      try {
        const res = await getRealTimeData();
        if (res.code === 200) {
          todayStats.value = res.data;
        }
      } catch (error) {
        console.error('获取实时数据失败:', error);
      }
    };

    // 获取热销商品数据
    const fetchHotProducts = async () => {
      try {
        const res = await getHotProductsWithTrend();
        if (res.code === 200) {
          hotProducts.value = res.data;
        }
      } catch (error) {
        console.error('获取热销商品数据失败:', error);
      }
    };

    // 初始化订单趋势图
    const initOrderTrendChart = async () => {
      try {
        await nextTick();
        if (orderTrendChart) {
          orderTrendChart.dispose();
        }
        const chartDom = document.getElementById('orderTrendChart');
        if (!chartDom) return;
        
        orderTrendChart = echarts.init(chartDom);
        
        // 获取趋势数据
        const res = await getTrend({ type: 'hour' });
        if (res.code !== 200) return;

        const option = {
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: res.data.channels
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: res.data.times
          },
          yAxis: {
            type: 'value'
          },
          series: res.data.series
        };

        orderTrendChart.setOption(option);
      } catch (error) {
        console.error('趋势图表初始化失败:', error);
      }
    };

    // 处理窗口大小变化
    const handleResize = () => {
      orderTrendChart?.resize();
    };

    // 定时刷新数据
    let refreshTimer = null;
    const startRefreshTimer = () => {
      refreshTimer = setInterval(async () => {
        await Promise.all([
          fetchRealTimeData(),
          fetchHotProducts(),
          initOrderTrendChart()
        ]);
      }, 60000); // 每30秒刷新一次
    };

    // 页面跳转
    const goToOrderCenter = () => {
      router.push('/orders/center');
    };

    const goToDataScreen = () => {
      router.push('/orders/screen');
    };

    onMounted(async () => {
      await Promise.all([
        fetchRealTimeData(),
        fetchHotProducts(),
        initOrderTrendChart()
      ]);
      window.addEventListener('resize', handleResize);
      startRefreshTimer();
    });

    onUnmounted(() => {
      if (refreshTimer) {
        clearInterval(refreshTimer);
      }
      window.removeEventListener('resize', handleResize);
      orderTrendChart?.dispose();
    });

    return {
      todayStats,
      hotProducts,
      orderTrendChartRef,
      goToOrderCenter,
      goToDataScreen
    };
  }
});
</script>

<style lang="less" scoped>
.home-container {
  padding: 24px;

  .data-overview {
    margin-bottom: 24px;
  }

  .chart-section {
    margin-bottom: 24px;
  }

  .stat-trend {
    margin-top: 8px;
    font-size: 13px;

    .trend-value {
      &.up {
        color: #52c41a;
        &::before {
          content: '↑';
          margin-right: 4px;
        }
      }
      &.down {
        color: #ff4d4f;
        &::before {
          content: '↓';
          margin-right: 4px;
        }
      }
    }

    .trend-label {
      margin-left: 8px;
      color: #8c8c8c;
    }
  }

  .hot-product-item {
    display: flex;
    align-items: center;
    width: 100%;

    .rank {
      width: 24px;
      height: 24px;
      line-height: 24px;
      text-align: center;
      background: #f0f0f0;
      border-radius: 12px;
      margin-right: 8px;
      font-size: 12px;

      &.top-3 {
        background: #272343;
        color: white;
      }
    }

    .name {
      flex: 1;
      margin-right: 8px;
    }

    .sales {
      color: #8c8c8c;
      margin-right: 8px;
    }

    .trend {
      font-size: 12px;
      &.up {
        color: #52c41a;
        &::before {
          content: '↑';
          margin-right: 2px;
        }
      }
      &.down {
        color: #ff4d4f;
        &::before {
          content: '↓';
          margin-right: 2px;
        }
      }
    }
  }

  .feature-entrance {
    .entrance-card {
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: translateY(-2px);
      }

      .entrance-icon {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 60px;
        background: #f5f5f5;
        font-size: 24px;
        color: #272343;
      }

      :deep(.ant-card-meta-title) {
        font-size: 14px;
        margin-bottom: 4px !important;
      }

      :deep(.ant-card-meta-description) {
        font-size: 12px;
        color: #999;
      }
    }
  }
}
</style>