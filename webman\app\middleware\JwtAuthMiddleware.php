<?php
namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Exception;

class JwtAuthMiddleware implements MiddlewareInterface
{
    public function process(Request $request, callable $handler): Response
    {
        // 路由白名单
        $whiteList = [
            '/passport/login',
            '/passport/logout',
            '/passport/refresh-token',  // 添加刷新token的路由到白名单
            '/open-api/*',
        ];

        // 检查请求路径是否在白名单中
        $path = $request->path();
        $inWhiteList = false;
        foreach ($whiteList as $pattern) {
            // 如果是通配符结尾的路由
            if (str_ends_with($pattern, '/*')) {
                $prefix = rtrim($pattern, '/*');
                if (str_starts_with($path, $prefix)) {
                    $inWhiteList = true;
                    break;
                }
            }
            // 精确匹配
            else if ($path === $pattern) {
                $inWhiteList = true;
                break;
            }
        }

        // 如果在白名单中，则跳过验证
        if ($inWhiteList) {
            return $handler($request);
        }

        // 获取token
        $token = $request->header('Authorization');
        if (!$token) {
            return json(['code' => 401, 'message' => '请先登录']);
        }

        // 如果token包含Bearer前缀，去掉它
        if (strpos($token, 'Bearer ') === 0) {
            $token = substr($token, 7);
        }

        try {
            // 验证token
            $key = config('app.jwt_key');
            if (empty($key)) {
                throw new Exception('JWT密钥未配置');
            }
            
            $decoded = JWT::decode($token, new Key($key, 'HS256'));

            // 验证是否是access_token
            if (!isset($decoded->data->type) || $decoded->data->type !== 'access_token') {
                return json(['code' => 401, 'message' => '无效的访问令牌']);
            }
            
            // 将解码后的用户信息添加到请求中
            $request->member = $decoded->data;
            
            // 继续处理请求
            return $handler($request);
        } catch (Exception $e) {
            // token过期或无效
            return json(['code' => 401, 'message' => '登录已过期，请重新登录']);
        }
    }
} 