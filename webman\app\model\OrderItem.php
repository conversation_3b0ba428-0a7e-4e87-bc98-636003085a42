<?php
namespace app\model;

use support\Model;

class OrderItem extends Model
{
    protected $table = 'order_items';
    
    protected $fillable = [
        'order_id',
        'order_no',
        'channel_product_name',
        'channel_product_no',
        'product_id',
        'unit_price',
        'quantity',
        'total_price',
        'created_at',
        'updated_at'
    ];

    /**
     * 关联订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    /**
     * 关联备案商品
     */
    public function product()
    {
        return $this->belongsTo(RegisteredProduct::class, 'product_id');
    }
} 