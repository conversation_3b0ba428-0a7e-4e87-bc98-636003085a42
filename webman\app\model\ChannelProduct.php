<?php
namespace app\model;

use support\Model;
use app\model\RegisteredProduct;
use app\model\DictionaryItem;

class ChannelProduct extends Model
{
    protected $table = 'channel_products';
    
    protected $fillable = [
        'channel_id',
        'channel_product_name',
        'channel_product_no',
        'product_id',
        'created_at',
        'updated_at'
    ];

    // 关联商品
    public function product()
    {
        return $this->belongsTo(RegisteredProduct::class, 'product_id');
    }

    // 关联渠道
    public function channel()
    {
        return $this->belongsTo(DictionaryItem::class, 'channel_id');
    }
} 