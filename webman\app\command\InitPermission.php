<?php
namespace app\command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use app\model\Permission;
use support\Container;

class InitPermission extends Command
{
    protected static $defaultName = 'init:permission';
    protected static $defaultDescription = '初始化权限数据';

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $permissions = config('permission');
        $this->syncPermissions($permissions);

        $output->writeln('权限初始化完成');
        return Command::SUCCESS;
    }

    protected function syncPermissions(array $permissions, int $parentId = 0, string $prefix = '')
    {
        foreach ($permissions as $key => $item) {
            // 创建或更新权限
            $permissionCode = $prefix ? "{$prefix}:{$key}" : $key;
            $permission = Permission::updateOrCreate(
                ['permission_code' => $permissionCode],
                [
                    'name' => $item['name'],
                    'icon' => $item['icon'] ?? null,
                    'sort' => $item['sort'] ?? 0,
                    'menu_type' => 'menu',
                    'parent_id' => $parentId,
                    'status' => 1
                ]
            );

            // 处理子权限
            if (!empty($item['children'])) {
                $this->syncPermissions($item['children'], $permission->id, $permissionCode);
            }

            // 处理操作权限
            if (!empty($item['actions'])) {
                foreach ($item['actions'] as $action => $actionItem) {
                    Permission::updateOrCreate(
                        ['permission_code' => "{$permissionCode}:{$action}"],
                        [
                            'name' => $actionItem['name'],
                            'menu_type' => $actionItem['type'],
                            'parent_id' => $permission->id,
                            'status' => 1
                        ]
                    );
                }
            }
        }
    }
} 