<?php

namespace plugins\email;

use app\model\AppLog;
use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

class Service
{
    protected $config;
    protected $appId;
    protected $mailer;
    
    public function __construct($appId, array $config)
    {
        $this->appId = $appId;
        $this->config = $config;
        $this->initMailer();
    }
    
    /**
     * 初始化PHPMailer
     */
    protected function initMailer()
    {
        $this->mailer = new PHPMailer(true);
        
        try {
            // 服务器设置
            $this->mailer->isSMTP();
            $this->mailer->Host = $this->config['smtp_host'] ?? '';
            $this->mailer->SMTPAuth = true;
            $this->mailer->Username = $this->config['smtp_username'] ?? '';
            $this->mailer->Password = $this->config['smtp_password'] ?? '';
            $this->mailer->Port = (int)($this->config['smtp_port'] ?? 587);
            
            // 加密设置
            $encryption = $this->config['smtp_encryption'] ?? 'tls';
            if ($encryption === 'ssl') {
                $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
            } elseif ($encryption === 'tls') {
                $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            }
            
            // 其他设置
            $this->mailer->Timeout = (int)($this->config['timeout'] ?? 30);
            $this->mailer->SMTPDebug = (int)($this->config['debug_level'] ?? 0);
            $this->mailer->CharSet = 'UTF-8';
            
            // 发件人设置
            $fromEmail = $this->config['from_email'] ?? '';
            $fromName = $this->config['from_name'] ?? '报关系统';
            if ($fromEmail) {
                $this->mailer->setFrom($fromEmail, $fromName);
            }
            
        } catch (Exception $e) {
            $this->log('error', 'PHPMailer初始化失败', [
                'error' => $e->getMessage(),
                'config' => $this->maskSensitiveConfig()
            ]);
            throw new \Exception('邮件服务初始化失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 发送邮件
     * 
     * @param string|array $to 收件人邮箱，支持字符串或数组
     * @param string $subject 邮件主题
     * @param string $body 邮件内容
     * @param bool $isHtml 是否为HTML格式，默认true
     * @param array $attachments 附件列表
     * @return bool
     */
    public function sendMail($to, $subject, $body, $isHtml = true, $attachments = [])
    {
        try {
            $this->log('info', '开始发送邮件', [
                'to' => is_array($to) ? $to : [$to],
                'subject' => $subject
            ]);
            
            // 清除之前的收件人
            $this->mailer->clearAddresses();
            $this->mailer->clearAttachments();
            
            // 添加收件人
            if (is_array($to)) {
                foreach ($to as $email) {
                    if ($this->isValidEmail($email)) {
                        $this->mailer->addAddress(trim($email));
                    }
                }
            } else {
                if ($this->isValidEmail($to)) {
                    $this->mailer->addAddress(trim($to));
                }
            }
            
            // 检查是否有有效收件人
            if (empty($this->mailer->getToAddresses())) {
                throw new Exception('没有有效的收件人邮箱地址');
            }
            
            // 邮件内容
            $this->mailer->isHTML($isHtml);
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $body;
            
            // 添加附件
            if (!empty($attachments)) {
                foreach ($attachments as $attachment) {
                    if (is_array($attachment)) {
                        $this->mailer->addAttachment($attachment['path'], $attachment['name'] ?? '');
                    } else {
                        $this->mailer->addAttachment($attachment);
                    }
                }
            }
            
            // 发送邮件
            $result = $this->mailer->send();
            
            $this->log('info', '邮件发送成功', [
                'to' => is_array($to) ? $to : [$to],
                'subject' => $subject,
                'message_id' => $this->mailer->getLastMessageID()
            ]);
            
            return true;
            
        } catch (Exception $e) {
            $this->log('error', '邮件发送失败', [
                'to' => is_array($to) ? $to : [$to],
                'subject' => $subject,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw new \Exception('邮件发送失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 发送定时任务通知邮件
     * 
     * @param array $taskInfo 任务信息
     * @param string $status 任务状态：success/failed
     * @param string $result 任务结果
     * @param array $recipients 收件人列表
     * @return bool
     */
    public function sendTaskNotification($taskInfo, $status, $result = '', $recipients = [])
    {
        if (empty($recipients)) {
            return false;
        }
        
        $isSuccess = $status === 'success';
        $subject = $this->buildTaskSubject($taskInfo, $isSuccess);
        $body = $this->buildTaskBody($taskInfo, $isSuccess, $result);
        
        return $this->sendMail($recipients, $subject, $body);
    }
    
    /**
     * 构建任务通知邮件主题
     */
    protected function buildTaskSubject($taskInfo, $isSuccess)
    {
        $status = $isSuccess ? '执行成功' : '执行失败';
        $taskName = $taskInfo['name'] ?? '未知任务';
        
        return "【报关系统】定时任务{$status} - {$taskName}";
    }
    
    /**
     * 构建任务通知邮件内容
     */
    protected function buildTaskBody($taskInfo, $isSuccess, $result)
    {
        $status = $isSuccess ? '成功' : '失败';
        $statusColor = $isSuccess ? '#52c41a' : '#ff4d4f';
        $taskName = $taskInfo['name'] ?? '未知任务';
        $taskType = $taskInfo['type'] ?? '';
        $taskTarget = $taskInfo['target'] ?? '';
        $executedAt = date('Y-m-d H:i:s');
        
        $html = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <div style='background: #f5f5f5; padding: 20px; border-radius: 8px;'>
                <h2 style='color: #333; margin-top: 0;'>定时任务执行通知</h2>
                
                <div style='background: white; padding: 20px; border-radius: 6px; margin: 20px 0;'>
                    <table style='width: 100%; border-collapse: collapse;'>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold; width: 120px;'>任务名称：</td>
                            <td style='padding: 8px 0;'>{$taskName}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold;'>执行状态：</td>
                            <td style='padding: 8px 0;'>
                                <span style='color: {$statusColor}; font-weight: bold;'>{$status}</span>
                            </td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold;'>任务类型：</td>
                            <td style='padding: 8px 0;'>{$taskType}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold;'>任务目标：</td>
                            <td style='padding: 8px 0; word-break: break-all;'>{$taskTarget}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold;'>执行时间：</td>
                            <td style='padding: 8px 0;'>{$executedAt}</td>
                        </tr>";
        
        if (!empty($result)) {
            $html .= "
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold; vertical-align: top;'>执行结果：</td>
                            <td style='padding: 8px 0;'>
                                <pre style='background: #f8f8f8; padding: 10px; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word;'>{$result}</pre>
                            </td>
                        </tr>";
        }
        
        $html .= "
                    </table>
                </div>
                
                <div style='text-align: center; color: #666; font-size: 12px; margin-top: 20px;'>
                    <p>此邮件由报关系统自动发送，请勿回复</p>
                    <p>发送时间：{$executedAt}</p>
                </div>
            </div>
        </div>";
        
        return $html;
    }
    
    /**
     * 测试邮件连接
     */
    public function testConnection()
    {
        try {
            $this->mailer->smtpConnect();
            $this->mailer->smtpClose();
            
            $this->log('info', '邮件连接测试成功', [
                'smtp_host' => $this->config['smtp_host'],
                'smtp_port' => $this->config['smtp_port']
            ]);
            
            return ['success' => true, 'message' => '邮件服务连接成功'];
            
        } catch (Exception $e) {
            $this->log('error', '邮件连接测试失败', [
                'error' => $e->getMessage(),
                'config' => $this->maskSensitiveConfig()
            ]);
            
            return ['success' => false, 'message' => '连接失败: ' . $e->getMessage()];
        }
    }
    
    /**
     * 发送测试邮件
     */
    public function sendTestMail($to, $customSubject = '', $customBody = '')
    {
        $subject = $customSubject ?: '【报关系统】邮件服务测试';
        $body = $customBody ?: $this->buildTestMailBody();
        
        return $this->sendMail($to, $subject, $body);
    }
    
    /**
     * 构建测试邮件内容
     */
    protected function buildTestMailBody()
    {
        $time = date('Y-m-d H:i:s');
        
        return "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <div style='background: #f5f5f5; padding: 20px; border-radius: 8px;'>
                <h2 style='color: #333; margin-top: 0;'>邮件服务测试</h2>
                
                <div style='background: white; padding: 20px; border-radius: 6px; margin: 20px 0;'>
                    <p>恭喜！您的邮件服务配置正确，可以正常发送邮件。</p>
                    
                    <div style='background: #e6f7ff; border: 1px solid #91d5ff; padding: 15px; border-radius: 4px; margin: 15px 0;'>
                        <h4 style='margin: 0 0 10px 0; color: #1890ff;'>测试信息</h4>
                        <p style='margin: 5px 0;'><strong>发送时间：</strong>{$time}</p>
                        <p style='margin: 5px 0;'><strong>SMTP服务器：</strong>{$this->config['smtp_host']}:{$this->config['smtp_port']}</p>
                        <p style='margin: 5px 0;'><strong>发件人：</strong>{$this->config['from_email']}</p>
                    </div>
                    
                    <p>现在您可以在定时任务中启用邮件通知功能了。</p>
                </div>
                
                <div style='text-align: center; color: #666; font-size: 12px; margin-top: 20px;'>
                    <p>此邮件由报关系统自动发送，请勿回复</p>
                </div>
            </div>
        </div>";
    }
    
    /**
     * 验证邮箱格式
     */
    protected function isValidEmail($email)
    {
        return filter_var(trim($email), FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * 屏蔽敏感配置信息用于日志记录
     */
    protected function maskSensitiveConfig()
    {
        $config = $this->config;
        if (isset($config['smtp_password'])) {
            $config['smtp_password'] = '***';
        }
        return $config;
    }
    
    /**
     * 记录日志
     */
    protected function log($level, $message, array $context = [])
    {
        AppLog::create([
            'app_id' => $this->appId,
            'level' => $level,
            'message' => $message,
            'context' => $context,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
}
