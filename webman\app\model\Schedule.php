<?php

namespace app\model;

use support\Model;

class Schedule extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'schedule';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',           // 任务名称
        'type',          // 任务类型：url、backup、class
        'target',        // 目标：URL地址、类名::方法名
        'cron',          // CRON表达式
        'status',        // 状态：1启用、0禁用
        'remark',        // 备注
        'last_run_time', // 最后执行时间
        'next_run_time', // 下次执行时间
        'timer_id'       // 定时器ID
    ];

    /**
     * 获取所有可用的类方法
     * 任务类名必须以Task结尾，例如：ExampleTask.php
     * 任务方法名必须以task开头，例如：taskSendSystemReport()
     * @return array
     */
    /**
     * 获取所有可用的类方法
     * 任务类名必须以Task结尾，例如：ExampleTask.php
     * 任务方法名必须以task开头，例如：taskSendSystemReport()
     * @return array
     */
    /**
     * 获取所有可用的类方法
     * 任务类名必须以Task结尾，例如：ExampleTask.php
     * 任务方法名必须以task开头，例如：taskSendSystemReport()
     * @return array
     */
    public static function getAvailableClassMethods()
    {
        $methods = [];
        $path = app_path() . '/task';
        if (!is_dir($path)) {
            return $methods;
        }

        $files = glob($path . '/*.php');
        foreach ($files as $file) {
            $className = 'app\\task\\' . basename($file, '.php');
            if (class_exists($className)) {
                $reflector = new \ReflectionClass($className);
                $classMethods = $reflector->getMethods(\ReflectionMethod::IS_PUBLIC);
                foreach ($classMethods as $method) {
                    // 只添加以'task'开头的方法
                    if (!$method->isConstructor() && strpos($method->getName(), 'task') === 0) {
                        $methods[] = [
                            'class' => $className,
                            'method' => $method->getName(),
                            'value' => $className . '::' . $method->getName()
                        ];
                    }
                }
            }
        }
        return $methods;
    }

    /**
     * 根据 target 字符串调用类方法
     */
    public static function callClassMethod($target)
    {
        list($class, $method) = explode('::', $target);
        if (class_exists($class)) {
            $instance = new $class();
            if (method_exists($instance, $method)) {
                return call_user_func([$instance, $method]);
            } else {
                throw new \Exception("Method {$method} not found in class {$class}");
            }
        } else {
            throw new \Exception("Class {$class} not found");
        }
    }
} 