<?php

namespace app\model;

use support\Model;

class Schedule extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'schedule';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',           // 任务名称
        'type',          // 任务类型：url、backup、class
        'target',        // 目标：URL地址、类名::方法名
        'cron',          // CRON表达式
        'status',        // 状态：1启用、0禁用
        'remark',        // 备注
        'last_run_time', // 最后执行时间
        'next_run_time', // 下次执行时间
        'timer_id',      // 定时器ID
        'email_notification', // 是否启用邮件通知
        'email_recipients',   // 邮件接收人列表
        'email_subject',      // 邮件主题模板
        'email_content',      // 邮件内容模板
        'email_on_success',   // 任务成功时发送邮件
        'email_on_failure'    // 任务失败时发送邮件
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'status' => 'boolean',
        'email_notification' => 'boolean',
        'email_on_success' => 'boolean',
        'email_on_failure' => 'boolean'
    ];

    /**
     * 获取邮件接收人数组
     */
    public function getEmailRecipientsArrayAttribute()
    {
        if (empty($this->email_recipients)) {
            return [];
        }

        return array_filter(array_map('trim', explode(',', $this->email_recipients)));
    }

    /**
     * 设置邮件接收人
     */
    public function setEmailRecipientsAttribute($value)
    {
        if (is_array($value)) {
            $this->attributes['email_recipients'] = implode(',', array_filter($value));
        } else {
            $this->attributes['email_recipients'] = $value;
        }
    }

    /**
     * 检查是否需要发送邮件通知
     */
    public function shouldSendEmailNotification($isSuccess = true)
    {
        if (!$this->email_notification) {
            return false;
        }

        if (empty($this->email_recipients)) {
            return false;
        }

        return $isSuccess ? $this->email_on_success : $this->email_on_failure;
    }

    /**
     * 发送任务执行通知邮件
     */
    public function sendTaskNotification($isSuccess, $result = '', $attachments = [])
    {
        if (!$this->shouldSendEmailNotification($isSuccess)) {
            return false;
        }

        try {
            // 获取邮件服务
            $app = \app\model\App::where('code', 'email')
                                ->where('status', 1)
                                ->first();

            if (!$app) {
                throw new \Exception('邮件服务未配置或未启用');
            }

            $emailService = new \plugins\email\Service($app->id, $app->settings);

            // 构建邮件内容
            $subject = $this->buildEmailSubject($isSuccess);
            $body = $this->buildEmailBody($isSuccess, $result, $attachments);
            $recipients = $this->getEmailRecipientsArrayAttribute();

            // 发送邮件（包含附件）
            return $emailService->sendMail($recipients, $subject, $body, true, $attachments);

        } catch (\Exception $e) {
            // 记录错误日志但不抛出异常，避免影响主任务执行
            error_log("发送任务通知邮件失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 构建邮件主题
     */
    protected function buildEmailSubject($isSuccess)
    {
        if (!empty($this->email_subject)) {
            $subject = $this->email_subject;
            $subject = str_replace('{task_name}', $this->name, $subject);
            $subject = str_replace('{status}', $isSuccess ? '成功' : '失败', $subject);
            $subject = str_replace('{date}', date('Y-m-d'), $subject);
            $subject = str_replace('{time}', date('H:i:s'), $subject);
            return $subject;
        }

        $status = $isSuccess ? '执行成功' : '执行失败';
        return "【报关系统】定时任务{$status} - {$this->name}";
    }

    /**
     * 构建邮件内容
     */
    protected function buildEmailBody($isSuccess, $result, $attachments = [])
    {
        if (!empty($this->email_content)) {
            $content = $this->email_content;
            $content = str_replace('{task_name}', $this->name, $content);
            $content = str_replace('{status}', $isSuccess ? '成功' : '失败', $content);
            $content = str_replace('{result}', $result, $content);
            $content = str_replace('{date}', date('Y-m-d'), $content);
            $content = str_replace('{time}', date('H:i:s'), $content);
            $content = str_replace('{datetime}', date('Y-m-d H:i:s'), $content);

            // 添加附件信息
            $attachmentInfo = '';
            if (!empty($attachments)) {
                $attachmentInfo = '附件：' . count($attachments) . ' 个文件';
            }
            $content = str_replace('{attachments}', $attachmentInfo, $content);

            return $content;
        }

        // 使用默认模板
        return $this->buildDefaultEmailBody($isSuccess, $result, $attachments);
    }

    /**
     * 构建默认邮件内容
     */
    protected function buildDefaultEmailBody($isSuccess, $result, $attachments = [])
    {
        $status = $isSuccess ? '成功' : '失败';
        $statusColor = $isSuccess ? '#52c41a' : '#ff4d4f';
        $executedAt = date('Y-m-d H:i:s');

        $html = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <div style='background: #f5f5f5; padding: 20px; border-radius: 8px;'>
                <h2 style='color: #333; margin-top: 0;'>定时任务执行通知</h2>

                <div style='background: white; padding: 20px; border-radius: 6px; margin: 20px 0;'>
                    <table style='width: 100%; border-collapse: collapse;'>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold; width: 120px;'>任务名称：</td>
                            <td style='padding: 8px 0;'>{$this->name}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold;'>执行状态：</td>
                            <td style='padding: 8px 0;'>
                                <span style='color: {$statusColor}; font-weight: bold;'>{$status}</span>
                            </td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold;'>任务类型：</td>
                            <td style='padding: 8px 0;'>{$this->type}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold;'>任务目标：</td>
                            <td style='padding: 8px 0; word-break: break-all;'>{$this->target}</td>
                        </tr>
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold;'>执行时间：</td>
                            <td style='padding: 8px 0;'>{$executedAt}</td>
                        </tr>";

        if (!empty($result)) {
            $html .= "
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold; vertical-align: top;'>执行结果：</td>
                            <td style='padding: 8px 0;'>
                                <pre style='background: #f8f8f8; padding: 10px; border-radius: 4px; white-space: pre-wrap; word-wrap: break-word;'>{$result}</pre>
                            </td>
                        </tr>";
        }

        // 添加附件信息
        if (!empty($attachments)) {
            $html .= "
                        <tr>
                            <td style='padding: 8px 0; font-weight: bold; vertical-align: top;'>附件文件：</td>
                            <td style='padding: 8px 0;'>";

            foreach ($attachments as $attachment) {
                $fileName = is_array($attachment) ? $attachment['name'] : basename($attachment);
                $fileSize = '';

                if (is_array($attachment) && file_exists($attachment['path'])) {
                    $fileSize = ' (' . $this->formatFileSize(filesize($attachment['path'])) . ')';
                } elseif (is_string($attachment) && file_exists($attachment)) {
                    $fileSize = ' (' . $this->formatFileSize(filesize($attachment)) . ')';
                }

                $html .= "<div style='margin: 4px 0; padding: 6px 10px; background: #e6f7ff; border-left: 3px solid #1890ff; border-radius: 4px;'>
                            <span style='font-weight: bold;'>📎 {$fileName}</span>{$fileSize}
                          </div>";
            }

            $html .= "      </td>
                        </tr>";
        }

        $html .= "
                    </table>
                </div>

                <div style='text-align: center; color: #666; font-size: 12px; margin-top: 20px;'>
                    <p>此邮件由报关系统自动发送，请勿回复</p>
                    <p>发送时间：{$executedAt}</p>
                </div>
            </div>
        </div>";

        return $html;
    }

    /**
     * 获取所有可用的类方法
     * 任务类名必须以Task结尾，例如：ExampleTask.php
     * 任务方法名必须以task开头，例如：taskSendSystemReport()
     * @return array
     */
    /**
     * 获取所有可用的类方法
     * 任务类名必须以Task结尾，例如：ExampleTask.php
     * 任务方法名必须以task开头，例如：taskSendSystemReport()
     * @return array
     */
    /**
     * 获取所有可用的类方法
     * 任务类名必须以Task结尾，例如：ExampleTask.php
     * 任务方法名必须以task开头，例如：taskSendSystemReport()
     * @return array
     */
    public static function getAvailableClassMethods()
    {
        $methods = [];
        $path = app_path() . '/task';
        if (!is_dir($path)) {
            return $methods;
        }

        $files = glob($path . '/*.php');
        foreach ($files as $file) {
            $className = 'app\\task\\' . basename($file, '.php');
            if (class_exists($className)) {
                $reflector = new \ReflectionClass($className);
                $classMethods = $reflector->getMethods(\ReflectionMethod::IS_PUBLIC);
                foreach ($classMethods as $method) {
                    // 只添加以'task'开头的方法
                    if (!$method->isConstructor() && strpos($method->getName(), 'task') === 0) {
                        $methods[] = [
                            'class' => $className,
                            'method' => $method->getName(),
                            'value' => $className . '::' . $method->getName()
                        ];
                    }
                }
            }
        }
        return $methods;
    }

    /**
     * 根据 target 字符串调用类方法
     */
    public static function callClassMethod($target)
    {
        list($class, $method) = explode('::', $target);
        if (class_exists($class)) {
            $instance = new $class();
            if (method_exists($instance, $method)) {
                return call_user_func([$instance, $method]);
            } else {
                throw new \Exception("Method {$method} not found in class {$class}");
            }
        } else {
            throw new \Exception("Class {$class} not found");
        }
    }

    /**
     * 格式化文件大小
     */
    protected function formatFileSize($bytes)
    {
        if ($bytes >= 1073741824) {
            return number_format($bytes / 1073741824, 2) . ' GB';
        } elseif ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' B';
        }
    }
}