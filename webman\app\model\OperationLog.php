<?php

namespace app\model;

use support\Model;

class OperationLog extends Model
{
    protected $table = 'operation_log';

    protected $fillable = [
        'member_id',
        'username',
        'module',
        'action',
        'method',
        'url',
        'params',
        'ip',
        'location',
        'browser',
        'os',
        'result'
    ];

    /**
     * 关联用户
     */
    public function member()
    {
        return $this->belongsTo(Member::class);
    }

    /**
     * 记录操作日志
     * @param array $data
     * @return bool
     */
    public static function record($data)
    {
        // 只记录除了get请求外的其他请求
        if($data['method'] == 'GET'){
            return true;
        }

        try {
            return self::create($data);
        } catch (\Exception $e) {
            \support\Log::error('记录操作日志失败：' . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取用户操作日志
     * @param int $memberId
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getMemberLogs($memberId, $limit = 10)
    {
        return self::where('member_id', $memberId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
} 