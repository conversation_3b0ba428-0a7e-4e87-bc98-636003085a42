<?php
namespace app\controller\api;

use support\Request;
use app\model\App;
use app\model\AppConfigTemplate;
use app\model\ApiRequestLog;

class Customs179Controller
{
    /**
     * 处理179公告数据
     */
    public function handle(Request $request)
    {
        $response = null;
        $error = null;
        
        try {
            // 获取访问key
            $key = $request->get('key');
            if (empty($key)) {
                throw new \Exception('缺少访问key');
            }

            // 获取应用实例
            $app = App::where('code', 'customs179')
                ->where('status', 1)
                ->first();
                
            if (!$app) {
                throw new \Exception('应用未启用');
            }

            // 获取配置模板
            $template = AppConfigTemplate::where('app_id', $app->id)
                ->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(settings, '$.key')) = ?", [$key])
                ->first();

            if (!$template) {
                throw new \Exception('无效的访问key');
            }

            // 创建服务实例
            $service = new \plugins\customs179\Service($app->id, $template->settings);
            
            // 验证访问key
            if (!$service->validateKey($key)) {
                throw new \Exception('无效的访问key');
            }

            // 获取请求数据
            $data = $request->post();
            if ($request->method() === 'POST' && empty($data)) {
                throw new \Exception('请提供179公告数据');
            }

            // 记录179请求
            if($service->handle179Data($data)){
                // 处理数据
                $response = [
                    'code' => 10000,
                    'message' => '成功',
                    // 生成毫秒时间戳
                    'serviceTime' => time() * 1000
                ];

                return json($response);
            }
            
        } catch (\Exception $e) {
            $error = $e->getMessage();
            $response = [
                'code' => 500,
                'message' => '处理失败：' . $error
            ];
            return json($response);
        } finally {
            // 记录请求日志
            ApiRequestLog::create([
                'app_id' => $app->id ?? null,
                'template_id' => $template->id ?? null,
                'method' => $request->method(),
                'url' => $request->url(),
                'headers' => $request->header(),
                'request_data' => $request->all(),
                'response_data' => $response,
                'ip' => $request->getRealIp(),
                'status' => $error ? 0 : 1,
                'error_message' => $error,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);
        }
    }
} 