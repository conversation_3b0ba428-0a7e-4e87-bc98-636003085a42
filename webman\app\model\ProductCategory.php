<?php
namespace app\model;

use support\Model;
use app\model\RegisteredProduct;

class ProductCategory extends Model
{
    /**
     * 与模型关联的表名
     */
    protected $table = 'product_categories';

    /**
     * 可以批量赋值的属性
     */
    protected $fillable = [
        'name',
        'code',
        'sort',
        'status',
        'remark'
    ];

    /**
     * 自动维护时间戳
     */
    public $timestamps = true;

    /**
     * 获取该分类下的所有商品
     */
    public function products()
    {
        return $this->hasMany(RegisteredProduct::class, 'category_id');
    }
} 