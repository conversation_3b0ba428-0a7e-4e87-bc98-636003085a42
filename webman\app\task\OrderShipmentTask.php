<?php

namespace app\task;

use app\model\Order;
use plugins\zto\Service as ZtoService;
use plugins\yto\Service as YtoService;

// 订单自动发货任务
class OrderShipmentTask
{
    /**
     * 定时自动发货
     */
    public function taskOrderShipment()
    {
        // 获取待发货订单列表
        $orders = Order::with(['template' => ['logisticsApp', 'logisticsTemplate'], 'items', 'address', 'declaration', 'channel'])
            ->where('order_status', Order::STATUS_PAID)
            ->get();

        if (empty($orders)) {
            return '没有待发货订单';
        }

        foreach ($orders as $order) {
            // var_dump($order->toArray());die;
            // 获取物流应用ID
            $appId = $order->template->logisticsApp->id;
            // 获取物流应用code
            $appCode = strtolower($order->template->logisticsApp->code);
            // 获取应用配置（如有config字段）
            $config = $order->template->logisticsTemplate->settings ?? [];
            // 申报模板配置
            $template = $order->template;

            // 根据appCode实例化对应Service
            $class = "\\plugins\\{$appCode}\\Service";
            if (class_exists($class)) {
                $service = new $class($appId, $config, $template);
                $result = $service->createOrder($order->toArray());
                var_dump($result);die;
            } else {
                throw new \Exception("暂不支持的快递公司：{$appCode}");
            }

            var_dump($result);die;

            $order->order_status = 15;
            $order->save();
        }
        // return true;
    }

    /**
     * 清理临时文件
     */
    public function taskCleanTempFiles()
    {
        // 这里实现清理临时文件的逻辑
        return true;
    }

    /**
     * 生成系统报表
     */
    public function generateReport()
    {
        // 这里实现生成系统报表的逻辑
        return true;
    }
} 